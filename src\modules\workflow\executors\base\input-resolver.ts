import { Injectable, Logger } from '@nestjs/common';
import { ExecutionContext } from '../../interfaces/execution-context.interface';

/**
 * Service để resolve input expressions trong workflow nodes
 * Handles expressions like {{node1.output}}, {{trigger.data}}, etc.
 */
@Injectable()
export class InputResolver {
  private readonly logger = new Logger(InputResolver.name);

  /**
   * Resolve tất cả expressions trong input object
   * @param inputs - Raw input object có thể chứa expressions
   * @param context - Execution context để lấy data
   * @returns Resolved input object
   */
  async resolveInputs(inputs: any, context: ExecutionContext): Promise<any> {
    if (!inputs) {
      return inputs;
    }

    try {
      return await this.resolveValue(inputs, context);
    } catch (error) {
      this.logger.error('Error resolving inputs:', error);
      throw new Error(`Failed to resolve inputs: ${error.message}`);
    }
  }

  /**
   * Resolve một value (có thể là object, array, string, etc.)
   * @param value - Value cần resolve
   * @param context - Execution context
   * @returns Resolved value
   */
  private async resolveValue(value: any, context: ExecutionContext): Promise<any> {
    if (typeof value === 'string') {
      return this.resolveStringExpression(value, context);
    }

    if (Array.isArray(value)) {
      return Promise.all(value.map(item => this.resolveValue(item, context)));
    }

    if (value && typeof value === 'object') {
      const resolved: any = {};
      for (const [key, val] of Object.entries(value)) {
        resolved[key] = await this.resolveValue(val, context);
      }
      return resolved;
    }

    return value;
  }

  /**
   * Resolve expressions trong string
   * Supports: {{node.output}}, {{trigger.data}}, {{workflow.id}}, etc.
   * @param str - String có thể chứa expressions
   * @param context - Execution context
   * @returns Resolved string
   */
  private resolveStringExpression(str: string, context: ExecutionContext): any {
    // Pattern để match expressions: {{expression}}
    const expressionPattern = /\{\{([^}]+)\}\}/g;
    
    // Nếu string chỉ là một expression duy nhất, return resolved value
    const singleExpressionMatch = str.match(/^\{\{([^}]+)\}\}$/);
    if (singleExpressionMatch) {
      const expression = singleExpressionMatch[1].trim();
      return this.evaluateExpression(expression, context);
    }

    // Nếu string chứa multiple expressions, replace tất cả
    return str.replace(expressionPattern, (match, expression) => {
      const resolvedValue = this.evaluateExpression(expression.trim(), context);
      return this.valueToString(resolvedValue);
    });
  }

  /**
   * Evaluate một expression và return value
   * @param expression - Expression string (without {{}})
   * @param context - Execution context
   * @returns Resolved value
   */
  private evaluateExpression(expression: string, context: ExecutionContext): any {
    try {
      // Split expression by dots: node1.output.data
      const parts = expression.split('.');
      const rootKey = parts[0];

      switch (rootKey) {
        case 'trigger':
          return this.resolveTriggerData(parts.slice(1), context);
        
        case 'workflow':
          return this.resolveWorkflowData(parts.slice(1), context);
        
        case 'execution':
          return this.resolveExecutionData(parts.slice(1), context);
        
        default:
          // Assume it's a node reference: node1.output
          return this.resolveNodeOutput(rootKey, parts.slice(1), context);
      }
    } catch (error) {
      this.logger.warn(`Failed to resolve expression: ${expression}`, error);
      return `{{${expression}}}`;  // Return original expression if failed
    }
  }

  /**
   * Resolve trigger data: {{trigger.data.message}}
   * @param path - Path array after 'trigger'
   * @param context - Execution context
   * @returns Resolved value
   */
  private resolveTriggerData(path: string[], context: ExecutionContext): any {
    let data = context.triggerData;
    
    for (const key of path) {
      if (data && typeof data === 'object' && key in data) {
        data = data[key];
      } else {
        return undefined;
      }
    }
    
    return data;
  }

  /**
   * Resolve workflow data: {{workflow.id}}, {{workflow.name}}
   * @param path - Path array after 'workflow'
   * @param context - Execution context
   * @returns Resolved value
   */
  private resolveWorkflowData(path: string[], context: ExecutionContext): any {
    const workflowData = {
      id: context.workflowId,
      executionId: context.executionId,
      userId: context.userId,
      triggerType: context.triggerType,
    };

    let data = workflowData;
    for (const key of path) {
      if (data && typeof data === 'object' && key in data) {
        data = data[key];
      } else {
        return undefined;
      }
    }

    return data;
  }

  /**
   * Resolve execution data: {{execution.id}}, {{execution.startTime}}
   * @param path - Path array after 'execution'
   * @param context - Execution context
   * @returns Resolved value
   */
  private resolveExecutionData(path: string[], context: ExecutionContext): any {
    const executionData = {
      id: context.executionId,
      workflowId: context.workflowId,
      userId: context.userId,
      startTime: Date.now(), // This would be actual start time
    };

    let data = executionData;
    for (const key of path) {
      if (data && typeof data === 'object' && key in data) {
        data = data[key];
      } else {
        return undefined;
      }
    }

    return data;
  }

  /**
   * Resolve node output: {{node1.output.data}}
   * @param nodeId - ID của node
   * @param path - Path array after nodeId
   * @param context - Execution context
   * @returns Resolved value
   */
  private resolveNodeOutput(nodeId: string, path: string[], context: ExecutionContext): any {
    // Get node output from context
    let nodeOutput = context.getNodeOutput(nodeId);
    
    if (nodeOutput === undefined) {
      this.logger.warn(`Node output not found for node: ${nodeId}`);
      return undefined;
    }

    // Navigate through the path
    for (const key of path) {
      if (nodeOutput && typeof nodeOutput === 'object' && key in nodeOutput) {
        nodeOutput = nodeOutput[key];
      } else {
        return undefined;
      }
    }

    return nodeOutput;
  }

  /**
   * Convert value to string for string interpolation
   * @param value - Value to convert
   * @returns String representation
   */
  private valueToString(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    if (typeof value === 'string') {
      return value;
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  /**
   * Check if a string contains expressions
   * @param str - String to check
   * @returns true if contains expressions
   */
  hasExpressions(str: string): boolean {
    return /\{\{[^}]+\}\}/.test(str);
  }

  /**
   * Extract all expressions from a string
   * @param str - String to extract from
   * @returns Array of expressions (without {{}})
   */
  extractExpressions(str: string): string[] {
    const matches = str.match(/\{\{([^}]+)\}\}/g);
    if (!matches) return [];
    
    return matches.map(match => match.slice(2, -2).trim());
  }
}
