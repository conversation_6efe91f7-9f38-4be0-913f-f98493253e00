import { IsString, IsObject, IsOptional, IsArray, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho validation error
 */
export class ValidationErrorDto {
  @IsString()
  field: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  code?: string;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

/**
 * DTO cho validation result
 */
export class ValidationResultDto {
  @IsBoolean()
  valid: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors?: ValidationErrorDto[];

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho schema validation request
 */
export class SchemaValidationRequestDto {
  @IsObject()
  data: any;

  @IsObject()
  schema: Record<string, any>;

  @IsOptional()
  @IsString()
  schemaType?: 'input' | 'output';
}

/**
 * DTO cho workflow definition validation
 */
export class WorkflowDefinitionValidationDto {
  @IsObject()
  definition: {
    nodes: any[];
    edges: any[];
    metadata?: Record<string, any>;
  };

  @IsOptional()
  @IsBoolean()
  strict?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredNodeTypes?: string[];
}

/**
 * DTO cho node configuration validation
 */
export class NodeConfigValidationDto {
  @IsString()
  nodeType: string;

  @IsObject()
  config: Record<string, any>;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

/**
 * DTO cho workflow execution validation
 */
export class WorkflowExecutionValidationDto {
  @IsString()
  workflowId: string;

  @IsString()
  triggerType: string;

  @IsObject()
  triggerData: any;

  @IsOptional()
  @IsObject()
  options?: Record<string, any>;
}

/**
 * DTO cho bulk validation request
 */
export class BulkValidationRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SchemaValidationRequestDto)
  validations: SchemaValidationRequestDto[];

  @IsOptional()
  @IsBoolean()
  stopOnFirstError?: boolean;
}

/**
 * DTO cho bulk validation result
 */
export class BulkValidationResultDto {
  @IsBoolean()
  allValid: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationResultDto)
  results: ValidationResultDto[];

  @IsOptional()
  @IsObject()
  summary?: {
    total: number;
    valid: number;
    invalid: number;
    errors: number;
  };
}

/**
 * DTO cho node validation request
 */
export class NodeValidationRequestDto {
  @IsString()
  nodeType: string;

  @IsObject()
  config: Record<string, any>;

  @IsOptional()
  @IsObject()
  inputs?: Record<string, any>;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

/**
 * DTO cho node validation result
 */
export class NodeValidationResultDto {
  @IsBoolean()
  valid: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors?: ValidationErrorDto[];

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho workflow validation request
 */
export class WorkflowValidationRequestDto {
  @IsString()
  workflowId: string;

  @IsOptional()
  @IsBoolean()
  validateNodes?: boolean;

  @IsOptional()
  @IsBoolean()
  validateEdges?: boolean;

  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

/**
 * DTO cho workflow validation result
 */
export class WorkflowValidationResultDto {
  @IsBoolean()
  valid: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors?: ValidationErrorDto[];

  @IsOptional()
  @IsObject()
  nodeValidations?: Record<string, NodeValidationResultDto>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho schema validation result
 */
export class SchemaValidationResultDto {
  @IsBoolean()
  valid: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors?: ValidationErrorDto[];

  @IsOptional()
  @IsObject()
  schema?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho validation config
 */
export class ValidationConfigDto {
  @IsOptional()
  @IsBoolean()
  strict?: boolean;

  @IsOptional()
  @IsBoolean()
  allowAdditionalProperties?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredFields?: string[];

  @IsOptional()
  @IsObject()
  customValidators?: Record<string, any>;
}

/**
 * DTO cho validation rule
 */
export class ValidationRuleDto {
  @IsString()
  field: string;

  @IsString()
  rule: string;

  @IsOptional()
  @IsObject()
  parameters?: Record<string, any>;

  @IsOptional()
  @IsString()
  message?: string;
}

/**
 * DTO cho validation context
 */
export class ValidationContextDto {
  @IsOptional()
  @IsString()
  workflowId?: string;

  @IsOptional()
  @IsString()
  nodeId?: string;

  @IsOptional()
  @IsString()
  executionId?: string;

  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho validation metrics
 */
export class ValidationMetricsDto {
  @IsOptional()
  @IsString()
  validationId?: string;

  @IsOptional()
  @IsString()
  startTime?: string;

  @IsOptional()
  @IsString()
  endTime?: string;

  @IsOptional()
  @IsString()
  duration?: string;

  @IsOptional()
  @IsString()
  status?: 'success' | 'error' | 'timeout';

  @IsOptional()
  @IsObject()
  metrics?: Record<string, any>;
}

/**
 * DTO cho validation cache
 */
export class ValidationCacheDto {
  @IsString()
  key: string;

  @IsObject()
  result: ValidationResultDto;

  @IsOptional()
  @IsString()
  expiresAt?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho validation batch request
 */
export class ValidationBatchRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NodeValidationRequestDto)
  requests: NodeValidationRequestDto[];

  @IsOptional()
  @IsBoolean()
  parallel?: boolean;

  @IsOptional()
  @IsString()
  batchId?: string;
}

/**
 * DTO cho validation batch result
 */
export class ValidationBatchResultDto {
  @IsString()
  batchId: string;

  @IsBoolean()
  allValid: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NodeValidationResultDto)
  results: NodeValidationResultDto[];

  @IsOptional()
  @IsObject()
  summary?: {
    total: number;
    valid: number;
    invalid: number;
    duration: number;
  };
}

/**
 * DTO cho validation stats
 */
export class ValidationStatsDto {
  @IsOptional()
  @IsString()
  period?: string;

  @IsOptional()
  @IsString()
  totalValidations?: string;

  @IsOptional()
  @IsString()
  successfulValidations?: string;

  @IsOptional()
  @IsString()
  failedValidations?: string;

  @IsOptional()
  @IsString()
  averageDuration?: string;

  @IsOptional()
  @IsObject()
  breakdown?: Record<string, any>;
}

/**
 * DTO cho validation report
 */
export class ValidationReportDto {
  @IsString()
  reportId: string;

  @IsString()
  generatedAt: string;

  @IsOptional()
  @IsObject()
  summary?: ValidationStatsDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors?: ValidationErrorDto[];

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
