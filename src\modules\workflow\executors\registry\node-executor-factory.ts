import { Injectable, Logger } from '@nestjs/common';
import { NodeCategory } from '../../entities/node-definition.entity';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { NodeExecutorRegistry } from './node-executor-registry';
import { ExecutionContext } from '../../interfaces/execution-context.interface';

/**
 * Factory service để create và manage node executor instances
 * Provides caching, lifecycle management, và error handling
 */
@Injectable()
export class NodeExecutorFactory {
  private readonly logger = new Logger(NodeExecutorFactory.name);
  private readonly instanceCache = new Map<string, BaseNodeExecutor>();
  private readonly creationStats = new Map<string, { count: number; lastCreated: number }>();

  constructor(private readonly registry: NodeExecutorRegistry) {}

  /**
   * Create executor instance for node type
   * @param nodeType - Type của node
   * @param context - Execution context (optional, for context-specific instances)
   * @returns Executor instance or null
   */
  async createExecutor(
    nodeType: string,
    context?: ExecutionContext
  ): Promise<BaseNodeExecutor | null> {
    try {
      // Check if executor is registered
      if (!this.registry.isRegistered(nodeType)) {
        this.logger.warn(`Executor not registered for node type: ${nodeType}`);
        return null;
      }

      // Get cached instance if available
      const cacheKey = this.getCacheKey(nodeType, context);
      if (this.instanceCache.has(cacheKey)) {
        const cachedInstance = this.instanceCache.get(cacheKey)!;
        this.logger.debug(`Using cached executor for ${nodeType}`);
        return cachedInstance;
      }

      // Create new instance
      const executor = await this.registry.getExecutor(nodeType);
      if (!executor) {
        this.logger.error(`Failed to create executor for node type: ${nodeType}`);
        return null;
      }

      // Cache instance
      this.instanceCache.set(cacheKey, executor);
      
      // Update creation stats
      this.updateCreationStats(nodeType);

      this.logger.debug(`Created new executor for ${nodeType}`);
      return executor;
    } catch (error) {
      this.logger.error(`Error creating executor for ${nodeType}:`, error);
      return null;
    }
  }

  /**
   * Create multiple executors for batch processing
   * @param nodeTypes - Array of node types
   * @param context - Execution context
   * @returns Map of node type to executor instance
   */
  async createExecutors(
    nodeTypes: string[],
    context?: ExecutionContext
  ): Promise<Map<string, BaseNodeExecutor>> {
    const executors = new Map<string, BaseNodeExecutor>();
    
    const creationPromises = nodeTypes.map(async (nodeType) => {
      const executor = await this.createExecutor(nodeType, context);
      if (executor) {
        executors.set(nodeType, executor);
      }
    });

    await Promise.all(creationPromises);
    
    this.logger.debug(`Created ${executors.size}/${nodeTypes.length} executors for batch processing`);
    return executors;
  }

  /**
   * Get executor by category
   * @param category - Node category
   * @param context - Execution context
   * @returns Array of executor instances
   */
  async getExecutorsByCategory(
    category: NodeCategory,
    context?: ExecutionContext
  ): Promise<BaseNodeExecutor[]> {
    const nodeTypes = this.registry.getExecutorsByCategory(category);
    const executors: BaseNodeExecutor[] = [];

    for (const nodeType of nodeTypes) {
      const executor = await this.createExecutor(nodeType, context);
      if (executor) {
        executors.push(executor);
      }
    }

    return executors;
  }

  /**
   * Check if executor can be created for node type
   * @param nodeType - Type của node
   * @returns true if executor can be created
   */
  canCreateExecutor(nodeType: string): boolean {
    return this.registry.isRegistered(nodeType);
  }

  /**
   * Get available node types
   * @param category - Optional category filter
   * @returns Array of available node types
   */
  getAvailableNodeTypes(category?: NodeCategory): string[] {
    if (category) {
      return this.registry.getExecutorsByCategory(category);
    }
    return this.registry.getRegisteredTypes();
  }

  /**
   * Validate node type format
   * @param nodeType - Node type to validate
   * @returns Validation result
   */
  validateNodeType(nodeType: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check format: category.subcategory.action
    if (!/^[a-z]+(\.[a-z]+)*$/.test(nodeType)) {
      errors.push('Node type must be in format: category.subcategory.action');
    }

    // Check if registered
    if (!this.registry.isRegistered(nodeType)) {
      errors.push(`Node type ${nodeType} is not registered`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Clear cached instances
   * @param nodeType - Optional specific node type to clear
   */
  clearCache(nodeType?: string): void {
    if (nodeType) {
      // Clear specific node type
      const keysToDelete = Array.from(this.instanceCache.keys())
        .filter(key => key.startsWith(nodeType));
      
      for (const key of keysToDelete) {
        this.instanceCache.delete(key);
      }
      
      this.logger.debug(`Cleared cache for node type: ${nodeType}`);
    } else {
      // Clear all cache
      this.instanceCache.clear();
      this.logger.debug('Cleared all executor cache');
    }
  }

  /**
   * Get factory statistics
   * @returns Factory statistics
   */
  getStatistics(): {
    cachedInstances: number;
    creationStats: Record<string, { count: number; lastCreated: number }>;
    registryStats: any;
  } {
    return {
      cachedInstances: this.instanceCache.size,
      creationStats: Object.fromEntries(this.creationStats.entries()),
      registryStats: this.registry.getStatistics(),
    };
  }

  /**
   * Cleanup unused instances
   * @param maxAge - Maximum age in milliseconds
   */
  cleanup(maxAge: number = 3600000): void { // Default 1 hour
    const now = Date.now();
    let cleanedCount = 0;

    for (const [nodeType, stats] of this.creationStats.entries()) {
      if (now - stats.lastCreated > maxAge) {
        this.clearCache(nodeType);
        this.creationStats.delete(nodeType);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} unused executor instances`);
    }
  }

  /**
   * Preload executors for common node types
   * @param nodeTypes - Array of node types to preload
   */
  async preloadExecutors(nodeTypes: string[]): Promise<void> {
    this.logger.log(`Preloading ${nodeTypes.length} executors...`);
    
    const preloadPromises = nodeTypes.map(async (nodeType) => {
      try {
        await this.createExecutor(nodeType);
        this.logger.debug(`Preloaded executor: ${nodeType}`);
      } catch (error) {
        this.logger.warn(`Failed to preload executor ${nodeType}:`, error);
      }
    });

    await Promise.all(preloadPromises);
    this.logger.log('Executor preloading completed');
  }

  /**
   * Get cache key for executor instance
   * @param nodeType - Node type
   * @param context - Execution context
   * @returns Cache key
   */
  private getCacheKey(nodeType: string, context?: ExecutionContext): string {
    // For now, use simple node type as key
    // In future, could include context-specific data
    return nodeType;
  }

  /**
   * Update creation statistics
   * @param nodeType - Node type
   */
  private updateCreationStats(nodeType: string): void {
    const stats = this.creationStats.get(nodeType) || { count: 0, lastCreated: 0 };
    stats.count++;
    stats.lastCreated = Date.now();
    this.creationStats.set(nodeType, stats);
  }

  /**
   * Get most used executors
   * @param limit - Number of results to return
   * @returns Array of most used node types with stats
   */
  getMostUsedExecutors(limit: number = 10): Array<{ nodeType: string; count: number; lastCreated: number }> {
    return Array.from(this.creationStats.entries())
      .map(([nodeType, stats]) => ({ nodeType, ...stats }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Health check for factory
   * @returns Health status
   */
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check registry health
    const registryStats = this.registry.getStatistics();
    if (registryStats.totalExecutors === 0) {
      issues.push('No executors registered');
    }

    // Check for high error rates
    const errorCount = Object.keys(registryStats.errorCounts).length;
    if (errorCount > registryStats.totalExecutors * 0.1) {
      issues.push(`High error rate: ${errorCount} executors with errors`);
    }

    // Check cache size
    if (this.instanceCache.size > 1000) {
      issues.push('Cache size is very large, consider cleanup');
    }

    return {
      healthy: issues.length === 0,
      issues,
    };
  }
}
