import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Error Handler Node Executor
 * Handles error processing, recovery, and workflow error management
 */
@Injectable()
export class ErrorHandlerExecutor extends BaseNodeExecutor {
  readonly type = 'system.error.handler';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Error Handler';
  readonly description = 'Handle errors with recovery strategies, logging, and workflow control';

  /**
   * Execute error handling
   * @param inputs - Error handling configuration
   * @param context - Execution context
   * @returns Error handling result
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      errorSource,
      errorData,
      strategy = 'log',
      retryConfig,
      fallbackValue,
      notificationConfig,
      continueOnError = false,
      logLevel = 'error',
    } = inputs;

    this.logger.debug(`Handling error from source: ${errorSource}`);

    try {
      const errorInfo = await this.analyzeError(errorData, errorSource, context);
      
      let handlingResult: any;

      switch (strategy) {
        case 'retry':
          handlingResult = await this.handleRetryStrategy(errorInfo, retryConfig, context);
          break;
        case 'fallback':
          handlingResult = await this.handleFallbackStrategy(errorInfo, fallbackValue, context);
          break;
        case 'notify':
          handlingResult = await this.handleNotificationStrategy(errorInfo, notificationConfig, context);
          break;
        case 'log':
          handlingResult = await this.handleLogStrategy(errorInfo, logLevel, context);
          break;
        case 'escalate':
          handlingResult = await this.handleEscalationStrategy(errorInfo, context);
          break;
        case 'ignore':
          handlingResult = await this.handleIgnoreStrategy(errorInfo, context);
          break;
        case 'custom':
          handlingResult = await this.handleCustomStrategy(errorInfo, inputs.customHandler, context);
          break;
        default:
          throw new Error(`Unsupported error handling strategy: ${strategy}`);
      }

      // Emit error handling event
      await this.emitEvent('error.handled', {
        errorSource,
        strategy,
        success: handlingResult.success,
        executionId: context.executionId,
      }, context);

      return {
        handled: true,
        strategy,
        errorSource,
        errorInfo,
        result: handlingResult,
        continueExecution: continueOnError || handlingResult.success,
        handledAt: new Date().toISOString(),
        metadata: {
          errorType: errorInfo.type,
          errorSeverity: errorInfo.severity,
          recoveryAttempted: handlingResult.recoveryAttempted || false,
          notificationSent: handlingResult.notificationSent || false,
        },
      };

    } catch (error) {
      this.logger.error(`Error handling failed: ${error.message}`);
      
      // If error handling itself fails, we need to decide what to do
      return {
        handled: false,
        strategy,
        errorSource,
        handlingError: error.message,
        continueExecution: false,
        handledAt: new Date().toISOString(),
        metadata: {
          errorType: 'handler_failure',
          errorSeverity: 'critical',
          recoveryAttempted: false,
        },
      };
    }
  }

  /**
   * Analyze error to determine type, severity, and recovery options
   * @private
   */
  private async analyzeError(errorData: any, source: string, context: ExecutionContext): Promise<any> {
    const errorInfo = {
      source,
      type: this.determineErrorType(errorData),
      severity: this.determineErrorSeverity(errorData),
      message: this.extractErrorMessage(errorData),
      code: this.extractErrorCode(errorData),
      stack: this.extractErrorStack(errorData),
      timestamp: new Date().toISOString(),
      context: {
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      },
      recoverable: this.isRecoverable(errorData),
      retryable: this.isRetryable(errorData),
    };

    return errorInfo;
  }

  /**
   * Handle retry strategy
   * @private
   */
  private async handleRetryStrategy(errorInfo: any, retryConfig: any, context: ExecutionContext): Promise<any> {
    const {
      maxAttempts = 3,
      delay = 1000,
      backoffMultiplier = 2,
      maxDelay = 30000,
    } = retryConfig || {};

    if (!errorInfo.retryable) {
      return {
        success: false,
        reason: 'Error is not retryable',
        recoveryAttempted: false,
      };
    }

    this.logger.debug(`Attempting retry strategy with max ${maxAttempts} attempts`);

    // In a real implementation, this would trigger the retry of the failed operation
    // For now, we'll simulate the retry logic
    
    return {
      success: false, // Would be determined by actual retry attempt
      reason: 'Retry mechanism would be implemented here',
      recoveryAttempted: true,
      retryConfig: {
        maxAttempts,
        delay,
        backoffMultiplier,
        maxDelay,
      },
    };
  }

  /**
   * Handle fallback strategy
   * @private
   */
  private async handleFallbackStrategy(errorInfo: any, fallbackValue: any, context: ExecutionContext): Promise<any> {
    this.logger.debug('Applying fallback strategy');

    // Set fallback value in context
    if (fallbackValue !== undefined) {
      if (!context.variables) {
        context.variables = {};
      }
      context.variables['_fallback_value'] = fallbackValue;
    }

    return {
      success: true,
      reason: 'Fallback value applied',
      recoveryAttempted: true,
      fallbackValue,
    };
  }

  /**
   * Handle notification strategy
   * @private
   */
  private async handleNotificationStrategy(errorInfo: any, notificationConfig: any, context: ExecutionContext): Promise<any> {
    const {
      channels = ['email'],
      recipients = [],
      template,
      severity = 'error',
    } = notificationConfig || {};

    this.logger.debug(`Sending error notifications to ${channels.join(', ')}`);

    // In a real implementation, this would send actual notifications
    const notificationResult = await this.sendErrorNotifications(errorInfo, {
      channels,
      recipients,
      template,
      severity,
    });

    return {
      success: notificationResult.success,
      reason: 'Error notification sent',
      recoveryAttempted: false,
      notificationSent: notificationResult.success,
      notificationDetails: notificationResult,
    };
  }

  /**
   * Handle log strategy
   * @private
   */
  private async handleLogStrategy(errorInfo: any, logLevel: string, context: ExecutionContext): Promise<any> {
    // Log the error with specified level
    const logMessage = `Error in ${errorInfo.source}: ${errorInfo.message}`;
    const logData = {
      errorInfo,
      context: {
        executionId: context.executionId,
        workflowId: context.workflowId,
      },
    };

    switch (logLevel) {
      case 'debug':
        this.logger.debug(logMessage, logData);
        break;
      case 'warn':
        this.logger.warn(logMessage, logData);
        break;
      case 'error':
        this.logger.error(logMessage, logData);
        break;
      case 'fatal':
        if (this.logger.fatal) {
          this.logger.fatal(logMessage, logData);
        } else {
          this.logger.error(logMessage, logData);
        }
        break;
    }

    return {
      success: true,
      reason: 'Error logged successfully',
      recoveryAttempted: false,
      logLevel,
    };
  }

  /**
   * Handle escalation strategy
   * @private
   */
  private async handleEscalationStrategy(errorInfo: any, context: ExecutionContext): Promise<any> {
    this.logger.debug('Escalating error to higher level');

    // In a real implementation, this would escalate to monitoring systems, alerts, etc.
    await this.emitEvent('error.escalated', {
      errorInfo,
      executionId: context.executionId,
      escalatedAt: new Date().toISOString(),
    }, context);

    return {
      success: true,
      reason: 'Error escalated to monitoring systems',
      recoveryAttempted: false,
      escalated: true,
    };
  }

  /**
   * Handle ignore strategy
   * @private
   */
  private async handleIgnoreStrategy(errorInfo: any, context: ExecutionContext): Promise<any> {
    this.logger.debug('Ignoring error as per strategy');

    return {
      success: true,
      reason: 'Error ignored as per configuration',
      recoveryAttempted: false,
      ignored: true,
    };
  }

  /**
   * Handle custom strategy
   * @private
   */
  private async handleCustomStrategy(errorInfo: any, customHandler: any, context: ExecutionContext): Promise<any> {
    if (!customHandler || !customHandler.script) {
      throw new Error('Custom handler script is required for custom strategy');
    }

    try {
      const func = new Function('errorInfo', 'context', customHandler.script);
      const result = await func(errorInfo, context);

      return {
        success: true,
        reason: 'Custom handler executed',
        recoveryAttempted: true,
        customResult: result,
      };
    } catch (error) {
      throw new Error(`Custom handler execution failed: ${error.message}`);
    }
  }

  /**
   * Send error notifications
   * @private
   */
  private async sendErrorNotifications(errorInfo: any, config: any): Promise<any> {
    // Placeholder implementation
    // In a real system, this would integrate with notification services
    
    return {
      success: true,
      channels: config.channels,
      recipients: config.recipients,
      sentAt: new Date().toISOString(),
    };
  }

  /**
   * Determine error type
   * @private
   */
  private determineErrorType(errorData: any): string {
    if (typeof errorData === 'string') {
      return 'generic';
    }

    if (errorData.code) {
      if (errorData.code.startsWith('HTTP_')) return 'http';
      if (errorData.code.startsWith('DB_')) return 'database';
      if (errorData.code.startsWith('AUTH_')) return 'authentication';
      if (errorData.code.startsWith('VALIDATION_')) return 'validation';
    }

    if (errorData.name) {
      if (errorData.name.includes('Timeout')) return 'timeout';
      if (errorData.name.includes('Network')) return 'network';
      if (errorData.name.includes('Permission')) return 'permission';
    }

    return 'unknown';
  }

  /**
   * Determine error severity
   * @private
   */
  private determineErrorSeverity(errorData: any): 'low' | 'medium' | 'high' | 'critical' {
    const errorType = this.determineErrorType(errorData);

    switch (errorType) {
      case 'authentication':
      case 'permission':
        return 'high';
      case 'database':
      case 'network':
        return 'medium';
      case 'validation':
      case 'timeout':
        return 'low';
      case 'unknown':
        return 'critical';
      default:
        return 'medium';
    }
  }

  /**
   * Extract error message
   * @private
   */
  private extractErrorMessage(errorData: any): string {
    if (typeof errorData === 'string') {
      return errorData;
    }

    return errorData.message || errorData.error || 'Unknown error';
  }

  /**
   * Extract error code
   * @private
   */
  private extractErrorCode(errorData: any): string | undefined {
    return errorData.code || errorData.errorCode || errorData.status;
  }

  /**
   * Extract error stack
   * @private
   */
  private extractErrorStack(errorData: any): string | undefined {
    return errorData.stack || errorData.stackTrace;
  }

  /**
   * Check if error is recoverable
   * @private
   */
  private isRecoverable(errorData: any): boolean {
    const errorType = this.determineErrorType(errorData);
    const recoverableTypes = ['timeout', 'network', 'http'];
    return recoverableTypes.includes(errorType);
  }

  /**
   * Check if error is retryable
   * @private
   */
  private isRetryable(errorData: any): boolean {
    const errorType = this.determineErrorType(errorData);
    const retryableTypes = ['timeout', 'network'];
    return retryableTypes.includes(errorType);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['errorSource', 'errorData'],
      properties: {
        errorSource: {
          type: 'string',
          description: 'Source of the error (node ID, service name, etc.)',
        },
        errorData: {
          description: 'Error data or error object',
        },
        strategy: {
          type: 'string',
          enum: ['retry', 'fallback', 'notify', 'log', 'escalate', 'ignore', 'custom'],
          default: 'log',
          description: 'Error handling strategy',
        },
        retryConfig: {
          type: 'object',
          description: 'Configuration for retry strategy',
        },
        fallbackValue: {
          description: 'Fallback value for fallback strategy',
        },
        notificationConfig: {
          type: 'object',
          description: 'Configuration for notification strategy',
        },
        continueOnError: {
          type: 'boolean',
          default: false,
          description: 'Whether to continue workflow execution after handling error',
        },
        logLevel: {
          type: 'string',
          enum: ['debug', 'warn', 'error', 'fatal'],
          default: 'error',
          description: 'Log level for log strategy',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        handled: {
          type: 'boolean',
          description: 'Whether the error was successfully handled',
        },
        strategy: {
          type: 'string',
          description: 'Error handling strategy used',
        },
        errorSource: {
          type: 'string',
          description: 'Source of the error',
        },
        errorInfo: {
          type: 'object',
          description: 'Analyzed error information',
        },
        result: {
          type: 'object',
          description: 'Result of the error handling strategy',
        },
        continueExecution: {
          type: 'boolean',
          description: 'Whether workflow execution should continue',
        },
        handledAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when error was handled',
        },
        handlingError: {
          type: 'string',
          description: 'Error message if handling itself failed',
        },
        metadata: {
          type: 'object',
          properties: {
            errorType: {
              type: 'string',
              description: 'Type of error that was handled',
            },
            errorSeverity: {
              type: 'string',
              enum: ['low', 'medium', 'high', 'critical'],
              description: 'Severity level of the error',
            },
            recoveryAttempted: {
              type: 'boolean',
              description: 'Whether recovery was attempted',
            },
            notificationSent: {
              type: 'boolean',
              description: 'Whether notification was sent',
            },
          },
        },
      },
    };
  }
}
