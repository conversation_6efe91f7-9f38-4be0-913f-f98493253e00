import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Loop Node Executor
 * Handles loop/iteration operations in workflows
 * Supports array iteration, fixed count loops, and conditional loops
 */
@Injectable()
export class LoopExecutor extends BaseNodeExecutor {
  readonly type = 'system.loop';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Loop';
  readonly description = 'Execute loop operations with array iteration, fixed count, or conditional loops';

  /**
   * Execute loop operation
   * @param inputs - Loop configuration
   * @param context - Execution context
   * @returns Loop execution results
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      loopType = 'array',
      data,
      count,
      condition,
      maxIterations = 1000,
      breakOnError = false,
      collectResults = true,
      parallel = false,
      batchSize = 10,
    } = inputs;

    this.logger.debug(`Starting ${loopType} loop with max ${maxIterations} iterations`);

    const startTime = Date.now();
    let iterations = 0;
    let results: any[] = [];
    let errors: any[] = [];
    let breakReason: string | null = null;

    try {
      switch (loopType) {
        case 'array':
          ({ iterations, results, errors, breakReason } = await this.executeArrayLoop(
            data, maxIterations, breakOnError, collectResults, parallel, batchSize, context
          ));
          break;
        case 'count':
          ({ iterations, results, errors, breakReason } = await this.executeCountLoop(
            count, maxIterations, breakOnError, collectResults, context
          ));
          break;
        case 'while':
          ({ iterations, results, errors, breakReason } = await this.executeWhileLoop(
            condition, maxIterations, breakOnError, collectResults, context
          ));
          break;
        case 'dowhile':
          ({ iterations, results, errors, breakReason } = await this.executeDoWhileLoop(
            condition, maxIterations, breakOnError, collectResults, context
          ));
          break;
        default:
          throw new Error(`Unsupported loop type: ${loopType}`);
      }

      const duration = Date.now() - startTime;

      this.logger.debug(`Loop completed: ${iterations} iterations in ${duration}ms`);

      return {
        completed: true,
        loopType,
        iterations,
        results: collectResults ? results : null,
        errors,
        duration,
        breakReason,
        metadata: {
          startTime,
          endTime: Date.now(),
          averageIterationTime: iterations > 0 ? duration / iterations : 0,
          successRate: iterations > 0 ? ((iterations - errors.length) / iterations) * 100 : 100,
          parallel,
          batchSize: parallel ? batchSize : 1,
        },
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error(`Loop execution failed: ${error.message}`);
      
      return {
        completed: false,
        loopType,
        iterations,
        results: collectResults ? results : null,
        errors: [...errors, { iteration: iterations, error: error.message }],
        duration,
        breakReason: 'error',
        error: error.message,
        metadata: {
          startTime,
          endTime: Date.now(),
          averageIterationTime: iterations > 0 ? duration / iterations : 0,
          successRate: iterations > 0 ? ((iterations - errors.length) / iterations) * 100 : 0,
          parallel,
          batchSize: parallel ? batchSize : 1,
        },
      };
    }
  }

  /**
   * Execute array iteration loop
   * @private
   */
  private async executeArrayLoop(
    data: any[],
    maxIterations: number,
    breakOnError: boolean,
    collectResults: boolean,
    parallel: boolean,
    batchSize: number,
    context: ExecutionContext
  ): Promise<{ iterations: number; results: any[]; errors: any[]; breakReason: string | null }> {
    if (!Array.isArray(data)) {
      throw new Error('Data must be an array for array loop');
    }

    const items = data.slice(0, maxIterations);
    let results: any[] = [];
    let errors: any[] = [];
    let breakReason: string | null = null;

    if (parallel) {
      // Execute in parallel batches
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchPromises = batch.map((item, batchIndex) => 
          this.executeLoopIteration(item, i + batchIndex, context)
        );

        try {
          const batchResults = await Promise.allSettled(batchPromises);
          
          batchResults.forEach((result, batchIndex) => {
            const globalIndex = i + batchIndex;
            if (result.status === 'fulfilled') {
              if (collectResults) {
                results[globalIndex] = result.value;
              }
            } else {
              const error = { iteration: globalIndex, error: result.reason?.message || 'Unknown error' };
              errors.push(error);
              
              if (breakOnError) {
                breakReason = 'error';
                return;
              }
            }
          });

          if (breakReason) break;

        } catch (error) {
          if (breakOnError) {
            breakReason = 'error';
            break;
          }
        }
      }
    } else {
      // Execute sequentially
      for (let i = 0; i < items.length; i++) {
        try {
          const result = await this.executeLoopIteration(items[i], i, context);
          
          if (collectResults) {
            results.push(result);
          }

        } catch (error) {
          const errorInfo = { iteration: i, error: error.message };
          errors.push(errorInfo);
          
          if (breakOnError) {
            breakReason = 'error';
            break;
          }
        }

        // Check for external break conditions
        if (await this.shouldBreakLoop(context)) {
          breakReason = 'external';
          break;
        }
      }
    }

    return {
      iterations: parallel ? items.length : results.length + errors.length,
      results,
      errors,
      breakReason,
    };
  }

  /**
   * Execute fixed count loop
   * @private
   */
  private async executeCountLoop(
    count: number,
    maxIterations: number,
    breakOnError: boolean,
    collectResults: boolean,
    context: ExecutionContext
  ): Promise<{ iterations: number; results: any[]; errors: any[]; breakReason: string | null }> {
    if (typeof count !== 'number' || count < 0) {
      throw new Error('Count must be a non-negative number');
    }

    const actualCount = Math.min(count, maxIterations);
    let results: any[] = [];
    let errors: any[] = [];
    let breakReason: string | null = null;

    for (let i = 0; i < actualCount; i++) {
      try {
        const result = await this.executeLoopIteration(i, i, context);
        
        if (collectResults) {
          results.push(result);
        }

      } catch (error) {
        const errorInfo = { iteration: i, error: error.message };
        errors.push(errorInfo);
        
        if (breakOnError) {
          breakReason = 'error';
          break;
        }
      }

      // Check for external break conditions
      if (await this.shouldBreakLoop(context)) {
        breakReason = 'external';
        break;
      }
    }

    return {
      iterations: results.length + errors.length,
      results,
      errors,
      breakReason,
    };
  }

  /**
   * Execute while loop
   * @private
   */
  private async executeWhileLoop(
    condition: string,
    maxIterations: number,
    breakOnError: boolean,
    collectResults: boolean,
    context: ExecutionContext
  ): Promise<{ iterations: number; results: any[]; errors: any[]; breakReason: string | null }> {
    if (!condition) {
      throw new Error('Condition is required for while loop');
    }

    let results: any[] = [];
    let errors: any[] = [];
    let breakReason: string | null = null;
    let iteration = 0;

    while (iteration < maxIterations) {
      // Evaluate condition
      const shouldContinue = await this.evaluateLoopCondition(condition, iteration, context);
      if (!shouldContinue) {
        breakReason = 'condition';
        break;
      }

      try {
        const result = await this.executeLoopIteration(iteration, iteration, context);
        
        if (collectResults) {
          results.push(result);
        }

      } catch (error) {
        const errorInfo = { iteration, error: error.message };
        errors.push(errorInfo);
        
        if (breakOnError) {
          breakReason = 'error';
          break;
        }
      }

      // Check for external break conditions
      if (await this.shouldBreakLoop(context)) {
        breakReason = 'external';
        break;
      }

      iteration++;
    }

    if (iteration >= maxIterations) {
      breakReason = 'maxIterations';
    }

    return {
      iterations: iteration,
      results,
      errors,
      breakReason,
    };
  }

  /**
   * Execute do-while loop
   * @private
   */
  private async executeDoWhileLoop(
    condition: string,
    maxIterations: number,
    breakOnError: boolean,
    collectResults: boolean,
    context: ExecutionContext
  ): Promise<{ iterations: number; results: any[]; errors: any[]; breakReason: string | null }> {
    if (!condition) {
      throw new Error('Condition is required for do-while loop');
    }

    let results: any[] = [];
    let errors: any[] = [];
    let breakReason: string | null = null;
    let iteration = 0;

    do {
      try {
        const result = await this.executeLoopIteration(iteration, iteration, context);
        
        if (collectResults) {
          results.push(result);
        }

      } catch (error) {
        const errorInfo = { iteration, error: error.message };
        errors.push(errorInfo);
        
        if (breakOnError) {
          breakReason = 'error';
          break;
        }
      }

      // Check for external break conditions
      if (await this.shouldBreakLoop(context)) {
        breakReason = 'external';
        break;
      }

      iteration++;

      // Evaluate condition for next iteration
      if (iteration < maxIterations) {
        const shouldContinue = await this.evaluateLoopCondition(condition, iteration, context);
        if (!shouldContinue) {
          breakReason = 'condition';
          break;
        }
      }

    } while (iteration < maxIterations);

    if (iteration >= maxIterations) {
      breakReason = 'maxIterations';
    }

    return {
      iterations: iteration,
      results,
      errors,
      breakReason,
    };
  }

  /**
   * Execute single loop iteration
   * @private
   */
  private async executeLoopIteration(item: any, index: number, context: ExecutionContext): Promise<any> {
    // In a real implementation, this would execute the loop body nodes
    // For now, return the item with iteration metadata
    await this.emitEvent('loop.iteration', {
      index,
      item,
      executionId: context.executionId,
    }, context);

    return {
      index,
      item,
      processedAt: new Date().toISOString(),
    };
  }

  /**
   * Evaluate loop condition
   * @private
   */
  private async evaluateLoopCondition(condition: string, iteration: number, context: ExecutionContext): Promise<boolean> {
    try {
      // Create evaluation context
      const evalContext = {
        iteration,
        context,
        // Add utility functions
        utils: {
          now: () => Date.now(),
          random: () => Math.random(),
        },
      };

      // Simple condition evaluation
      const func = new Function(...Object.keys(evalContext), `return (${condition});`);
      return Boolean(func(...Object.values(evalContext)));

    } catch (error) {
      this.logger.warn(`Loop condition evaluation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if loop should be broken externally
   * @private
   */
  private async shouldBreakLoop(context: ExecutionContext): Promise<boolean> {
    // This would check for workflow cancellation or other break signals
    // For now, return false
    return false;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['loopType'],
      properties: {
        loopType: {
          type: 'string',
          enum: ['array', 'count', 'while', 'dowhile'],
          description: 'Type of loop to execute',
        },
        data: {
          type: 'array',
          description: 'Array data for array loop type',
        },
        count: {
          type: 'integer',
          minimum: 0,
          description: 'Number of iterations for count loop type',
        },
        condition: {
          type: 'string',
          description: 'Condition expression for while/do-while loops',
        },
        maxIterations: {
          type: 'integer',
          minimum: 1,
          maximum: 10000,
          default: 1000,
          description: 'Maximum number of iterations allowed',
        },
        breakOnError: {
          type: 'boolean',
          default: false,
          description: 'Whether to break loop on first error',
        },
        collectResults: {
          type: 'boolean',
          default: true,
          description: 'Whether to collect and return iteration results',
        },
        parallel: {
          type: 'boolean',
          default: false,
          description: 'Whether to execute iterations in parallel (array loop only)',
        },
        batchSize: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
          default: 10,
          description: 'Batch size for parallel execution',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        completed: {
          type: 'boolean',
          description: 'Whether the loop completed successfully',
        },
        loopType: {
          type: 'string',
          description: 'Type of loop that was executed',
        },
        iterations: {
          type: 'integer',
          description: 'Number of iterations executed',
        },
        results: {
          type: 'array',
          description: 'Results from each iteration (if collectResults is true)',
        },
        errors: {
          type: 'array',
          description: 'Errors that occurred during execution',
        },
        duration: {
          type: 'integer',
          description: 'Total execution time in milliseconds',
        },
        breakReason: {
          type: 'string',
          enum: ['condition', 'error', 'external', 'maxIterations'],
          description: 'Reason why the loop was terminated',
        },
        error: {
          type: 'string',
          description: 'Error message if loop failed',
        },
        metadata: {
          type: 'object',
          properties: {
            startTime: {
              type: 'integer',
              description: 'Loop start timestamp',
            },
            endTime: {
              type: 'integer',
              description: 'Loop end timestamp',
            },
            averageIterationTime: {
              type: 'number',
              description: 'Average time per iteration in milliseconds',
            },
            successRate: {
              type: 'number',
              description: 'Success rate percentage',
            },
            parallel: {
              type: 'boolean',
              description: 'Whether parallel execution was used',
            },
            batchSize: {
              type: 'integer',
              description: 'Batch size used for parallel execution',
            },
          },
        },
      },
    };
  }
}
