import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { NodeExecutorRegistry } from './node-executor-registry';
import { NodeCategory } from '../../entities/node-definition.entity';
import {
  HttpRequestExecutor,
  ConditionExecutor,
  DelayExecutor,
  DataTransformExecutor,
  LoopExecutor,
  OutputExecutor,
  ManualTriggerExecutor,
  VariableExecutor,
  ErrorHandlerExecutor,
} from '../system';

/**
 * Service to register all system node executors
 * Automatically registers system executors with the main registry
 */
@Injectable()
export class SystemExecutorRegistryService implements OnModuleInit {
  private readonly logger = new Logger(SystemExecutorRegistryService.name);

  constructor(private readonly nodeExecutorRegistry: NodeExecutorRegistry) {}

  async onModuleInit() {
    await this.registerSystemExecutors();
  }

  /**
   * Register all system executors
   */
  private async registerSystemExecutors(): Promise<void> {
    this.logger.log('Registering system node executors...');

    const systemExecutors = [
      HttpRequestExecutor,
      ConditionExecutor,
      DelayExecutor,
      DataTransformExecutor,
      LoopExecutor,
      OutputExecutor,
      ManualTriggerExecutor,
      VariableExecutor,
      ErrorHandlerExecutor,
    ];

    let registeredCount = 0;
    let failedCount = 0;

    for (const ExecutorClass of systemExecutors) {
      try {
        const success = this.nodeExecutorRegistry.registerExecutor(ExecutorClass);
        if (success) {
          registeredCount++;
          this.logger.debug(`Registered system executor: ${ExecutorClass.name}`);
        } else {
          failedCount++;
          this.logger.warn(`Failed to register system executor: ${ExecutorClass.name}`);
        }
      } catch (error) {
        failedCount++;
        this.logger.error(`Error registering system executor ${ExecutorClass.name}:`, error);
      }
    }

    this.logger.log(
      `System executor registration completed: ${registeredCount} registered, ${failedCount} failed`
    );

    if (failedCount > 0) {
      this.logger.warn(`${failedCount} system executors failed to register`);
    }

    // Log registered system executors
    this.logRegisteredExecutors();
  }

  /**
   * Log all registered system executors
   */
  private logRegisteredExecutors(): void {
    const systemExecutors = this.nodeExecutorRegistry.getExecutorsByCategory(NodeCategory.SYSTEM);

    this.logger.log('Registered system executors:');
    systemExecutors.forEach(executor => {
      this.logger.log(`  - ${executor}: system executor`);
    });
  }

  /**
   * Get system executor types
   */
  getSystemExecutorTypes(): string[] {
    return [
      'system.http.request',
      'system.if.condition',
      'system.delay',
      'system.transform.data',
      'system.loop',
      'system.output',
      'system.manual.trigger',
      'system.variable',
      'system.error.handler',
    ];
  }

  /**
   * Validate system executor registration
   */
  async validateSystemExecutors(): Promise<{
    valid: boolean;
    registered: string[];
    missing: string[];
    errors: string[];
  }> {
    const expectedTypes = this.getSystemExecutorTypes();
    const registered: string[] = [];
    const missing: string[] = [];
    const errors: string[] = [];

    for (const type of expectedTypes) {
      try {
        const executor = await this.nodeExecutorRegistry.getExecutor(type);
        if (executor) {
          registered.push(type);
        } else {
          missing.push(type);
        }
      } catch (error) {
        errors.push(`${type}: ${error.message}`);
        missing.push(type);
      }
    }

    const valid = missing.length === 0 && errors.length === 0;

    return {
      valid,
      registered,
      missing,
      errors,
    };
  }

  /**
   * Get system executor capabilities
   */
  getSystemExecutorCapabilities(): Record<string, string[]> {
    return {
      'system.http.request': [
        'http-requests',
        'api-integration',
        'retry-logic',
        'timeout-handling',
        'response-processing',
      ],
      'system.if.condition': [
        'conditional-logic',
        'expression-evaluation',
        'workflow-branching',
        'variable-comparison',
        'boolean-operations',
      ],
      'system.delay': [
        'time-delays',
        'workflow-pacing',
        'rate-limiting',
        'scheduled-execution',
        'interruption-handling',
      ],
      'system.transform.data': [
        'data-mapping',
        'data-filtering',
        'data-aggregation',
        'format-conversion',
        'custom-transformations',
      ],
      'system.loop': [
        'array-iteration',
        'count-loops',
        'conditional-loops',
        'parallel-processing',
        'batch-processing',
      ],
      'system.output': [
        'result-formatting',
        'data-export',
        'multiple-formats',
        'metadata-inclusion',
        'output-validation',
      ],
      'system.manual.trigger': [
        'workflow-initiation',
        'data-validation',
        'context-enrichment',
        'trigger-processing',
        'metadata-injection',
      ],
      'system.variable': [
        'variable-management',
        'data-storage',
        'scope-handling',
        'type-conversion',
        'validation-rules',
      ],
      'system.error.handler': [
        'error-recovery',
        'retry-strategies',
        'fallback-handling',
        'notification-sending',
        'error-logging',
      ],
    };
  }

  /**
   * Get system executor usage examples
   */
  getSystemExecutorExamples(): Record<string, any> {
    return {
      'system.http.request': {
        description: 'Make HTTP API calls',
        example: {
          url: 'https://api.example.com/users',
          method: 'GET',
          headers: { 'Authorization': 'Bearer {{token}}' },
          timeout: 5000,
          retries: 2,
        },
      },
      'system.if.condition': {
        description: 'Evaluate conditions for workflow branching',
        example: {
          condition: 'user.age >= 18 && user.verified === true',
          variables: { user: { age: 25, verified: true } },
          operator: 'javascript',
        },
      },
      'system.delay': {
        description: 'Add delays to workflow execution',
        example: {
          duration: 30,
          unit: 'seconds',
          reason: 'Rate limiting delay',
        },
      },
      'system.transform.data': {
        description: 'Transform and manipulate data',
        example: {
          data: { firstName: 'John', lastName: 'Doe' },
          transformType: 'mapping',
          mapping: { fullName: 'firstName + " " + lastName' },
        },
      },
      'system.loop': {
        description: 'Iterate over data or execute repeated operations',
        example: {
          loopType: 'array',
          data: ['item1', 'item2', 'item3'],
          collectResults: true,
        },
      },
      'system.output': {
        description: 'Format and output workflow results',
        example: {
          data: { result: 'success', processedItems: 5 },
          format: 'json',
          includeMetadata: true,
        },
      },
      'system.manual.trigger': {
        description: 'Process manual workflow triggers',
        example: {
          triggerData: { userId: 123, action: 'signup' },
          validateInput: true,
          enrichData: true,
        },
      },
      'system.variable': {
        description: 'Manage workflow variables',
        example: {
          operation: 'set',
          variableName: 'userEmail',
          value: '<EMAIL>',
          scope: 'execution',
        },
      },
      'system.error.handler': {
        description: 'Handle errors with recovery strategies',
        example: {
          errorSource: 'api-call-node',
          errorData: { message: 'Connection timeout' },
          strategy: 'retry',
          retryConfig: { maxAttempts: 3, delay: 1000 },
        },
      },
    };
  }

  /**
   * Get system executor documentation
   */
  getSystemExecutorDocumentation(): Record<string, any> {
    const capabilities = this.getSystemExecutorCapabilities();
    const examples = this.getSystemExecutorExamples();

    return {
      overview: {
        description: 'System node executors provide core workflow functionality',
        totalExecutors: this.getSystemExecutorTypes().length,
        categories: ['HTTP', 'Logic', 'Data', 'Control', 'I/O', 'Variables', 'Error Handling'],
      },
      executors: this.getSystemExecutorTypes().map(type => ({
        type,
        capabilities: capabilities[type] || [],
        example: examples[type] || null,
      })),
    };
  }
}
