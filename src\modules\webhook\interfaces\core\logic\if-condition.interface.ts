/**
 * @file Interface cho If Condition node
 * 
 * Đ<PERSON>nh nghĩa type-safe interface cho node If Condition bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Condition evaluation logic
 * 
 * @version 1.0.0
 * <AUTHOR>
 */

import {
    ENodeGroup,
    ENodeType,
    INodeProperty,
} from '../../node-manager.interface';
import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    ICondition,
    EComparisonOperator,
    EDataType
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: IF-SPECIFIC ENUMS & TYPES
// Sử dụng shared logic từ condition-evaluation.interface.ts
// =================================================================

/**
 * Logical operators để kết hợp nhiều conditions - If node specific
 */
export enum ELogicalOperator {
    AND = 'and',
    OR = 'or'
}

// Helper functions đã được move sang shared/condition-evaluation.interface.ts

// =================================================================
// SECTION 2: CONDITION STRUCTURES
// Định nghĩa cấu trúc cho conditions
// =================================================================

// ICondition đã được move sang shared/condition-evaluation.interface.ts

/**
 * Group of conditions với logical operator
 */
export interface IConditionGroup {
    /** Logical operator để kết hợp conditions */
    logical_operator: ELogicalOperator;
    
    /** Danh sách conditions */
    conditions: ICondition[];
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// Định nghĩa cấu trúc parameters cho If Condition node
// =================================================================

/**
 * Interface cho parameters của If Condition node
 */
export interface IIfConditionParameters {
    /** Danh sách condition groups */
    condition_groups: IConditionGroup[];
    
    /** Logical operator để kết hợp các groups */
    groups_logical_operator: ELogicalOperator;
    
    /** Có continue execution khi condition false không */
    continue_on_false?: boolean;
    
    /** Custom JavaScript expression (advanced mode) */
    custom_expression?: string;
    
    /** Có sử dụng custom expression không */
    use_custom_expression?: boolean;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// Định nghĩa cấu trúc dữ liệu đầu vào và đầu ra
// =================================================================

/**
 * Interface cho input data của If Condition node
 */
export interface IIfConditionInput extends IBaseNodeInput {
    /** Dữ liệu để evaluate conditions */
    data: Record<string, any>;
    
    /** Context variables */
    variables?: Record<string, any>;
}

/**
 * Interface cho output data của If Condition node
 */
export interface IIfConditionOutput extends IBaseNodeOutput {
    /** Kết quả evaluation (true/false) */
    result: boolean;
    
    /** Dữ liệu gốc được pass through */
    data: Record<string, any>;
    
    /** Chi tiết evaluation của từng condition */
    evaluation_details: {
        condition_groups: Array<{
            logical_operator: ELogicalOperator;
            result: boolean;
            conditions: Array<{
                field: string;
                operator: EComparisonOperator;
                expected_value: any;
                actual_value: any;
                result: boolean;
            }>;
        }>;
        final_result: boolean;
    };
    
    /** Metadata */
    metadata: {
        evaluation_time: number;
        timestamp: number;
    };
}

// =================================================================
// SECTION 6: TYPED INTERFACES
// Kết hợp tất cả để tạo type-safe interfaces
// =================================================================

/**
 * Type-safe node execution cho If Condition
 */
export type IIfConditionNodeExecution = ITypedNodeExecution<
    IIfConditionInput,
    IIfConditionOutput,
    IIfConditionParameters
>;

// =================================================================
// SECTION 7: FACTORY & VALIDATION
// Helper functions
// =================================================================

/**
 * Validation function cho If Condition parameters
 */
export function validateIfConditionParameters(params: Partial<IIfConditionParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];
    
    if (params.use_custom_expression) {
        if (!params.custom_expression) {
            errors.push('Custom expression is required when use_custom_expression is true');
        }
    } else {
        if (!params.condition_groups || params.condition_groups.length === 0) {
            errors.push('At least one condition group is required');
        } else {
            params.condition_groups.forEach((group, groupIndex) => {
                if (!group.conditions || group.conditions.length === 0) {
                    errors.push(`Condition group ${groupIndex + 1} must have at least one condition`);
                } else {
                    group.conditions.forEach((condition, conditionIndex) => {
                        if (!condition.field) {
                            errors.push(`Condition ${conditionIndex + 1} in group ${groupIndex + 1}: Field is required`);
                        }
                        if (!condition.operator) {
                            errors.push(`Condition ${conditionIndex + 1} in group ${groupIndex + 1}: Operator is required`);
                        }
                    });
                }
            });
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}
