import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

@Injectable()
export class GoogleSheetsDeleteSheetExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.deleteSheet';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Delete Sheet from Google Spreadsheet';
  readonly description = 'Xóa sheet khỏi Google Spreadsheet';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      const { spreadsheetId, sheetId } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (sheetId === undefined) {
        throw new Error('Sheet ID is required');
      }

      const accessToken = await this.getGoogleAccessToken(context);

      const result = await this.googleSheetsService.deleteSheet(
        accessToken,
        spreadsheetId,
        sheetId
      );

      return {
        spreadsheetId,
        sheetId,
        success: true,
        deletedAt: new Date().toISOString(),
        sheetsResponse: result,
      };
    });
  }

  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (inputs.sheetId === undefined || !Number.isInteger(inputs.sheetId)) {
      throw new Error('Sheet ID must be an integer');
    }

    return true;
  }

  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        sheetId: {
          type: 'integer',
          description: 'ID của sheet cần xóa',
        },
      },
      required: ['spreadsheetId', 'sheetId'],
    };
  }

  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: { type: 'string' },
        sheetId: { type: 'integer' },
        success: { type: 'boolean' },
        deletedAt: { type: 'string' },
        sheetsResponse: { type: 'object' },
      },
    };
  }
}
