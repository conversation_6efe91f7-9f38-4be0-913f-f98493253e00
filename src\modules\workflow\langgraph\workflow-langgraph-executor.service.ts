import { Injectable, Logger } from '@nestjs/common';
import { CompiledStateGraph } from '@langchain/langgraph';
import { Workflow } from '../entities';
import { WorkflowGraphBuilderService } from './workflow-graph-builder.service';
import { WorkflowStateManagerService } from './workflow-state-manager.service';
import { EventService } from '../services/event.service';
import { LoggingService } from '../services/logging.service';
import {
  WorkflowState,
  WorkflowExecutionOptions,
  WorkflowExecutionResult,
  WorkflowGraphConfig,
  GraphExecutionStats,
} from './interfaces/langgraph.interface';

/**
 * LangGraph-based workflow executor service
 * Executes workflows using LangGraph state management and orchestration
 */
@Injectable()
export class WorkflowLangGraphExecutorService {
  private readonly logger = new Logger(WorkflowLangGraphExecutorService.name);
  private readonly compiledGraphs = new Map<string, CompiledStateGraph<WorkflowState, Partial<WorkflowState>, string>>();
  private readonly executionStats = new Map<string, GraphExecutionStats>();

  constructor(
    private readonly graphBuilder: WorkflowGraphBuilderService,
    private readonly stateManager: WorkflowStateManagerService,
    private readonly eventService: EventService,
    private readonly loggingService: LoggingService,
  ) {}

  /**
   * Execute workflow using LangGraph
   * @param workflow - Workflow definition to execute
   * @param options - Execution options
   * @returns Execution result
   */
  async executeWorkflow(
    workflow: Workflow,
    options: WorkflowExecutionOptions = {}
  ): Promise<WorkflowExecutionResult> {
    const executionId = this.generateExecutionId();
    const startTime = Date.now();

    this.logger.log(`Starting workflow execution: ${executionId} for workflow: ${workflow.id}`);

    try {
      // Validate workflow
      const validation = this.graphBuilder.validateWorkflowForGraph(workflow);
      if (!validation.valid) {
        throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
      }

      // Get or build compiled graph
      const compiledGraph = await this.getCompiledGraph(workflow, options);

      // Initialize workflow state
      const initialState = await this.initializeWorkflowState(
        executionId,
        workflow,
        options
      );

      // Execute workflow
      const finalState = await this.executeGraph(
        compiledGraph,
        initialState,
        options
      );

      // Calculate execution result
      const endTime = Date.now();
      const result = await this.buildExecutionResult(
        executionId,
        workflow,
        finalState,
        startTime,
        endTime,
        options
      );

      // Log completion
      await this.loggingService.logInfo(
        executionId,
        `Workflow execution completed: ${workflow.id}`,
        {
          status: result.status,
          duration: result.duration,
          completedNodes: result.stats.completedNodes,
          failedNodes: result.stats.failedNodes,
        }
      );

      // Emit completion event
      await this.eventService.publishEvent('workflow.completed', {
        executionId,
        workflowId: workflow.id,
        status: result.status,
        duration: result.duration,
      });

      return result;

    } catch (error) {
      const endTime = Date.now();
      
      this.logger.error(`Workflow execution failed: ${executionId}`, error);

      // Log error
      await this.loggingService.logError(
        executionId,
        `Workflow execution failed: ${workflow.id}`,
        { error: error.message, stack: error.stack }
      );

      // Emit error event
      await this.eventService.publishEvent('workflow.failed', {
        executionId,
        workflowId: workflow.id,
        error: error.message,
        duration: endTime - startTime,
      });

      // Return failed result
      return this.buildFailedExecutionResult(
        executionId,
        workflow,
        error,
        startTime,
        endTime
      );
    }
  }

  /**
   * Get or build compiled graph for workflow
   * @private
   */
  private async getCompiledGraph(
    workflow: Workflow,
    options: WorkflowExecutionOptions
  ): Promise<CompiledStateGraph<WorkflowState, Partial<WorkflowState>, string>> {
    const cacheKey = `${workflow.id}-'1.0'`;

    // Check cache
    if (this.compiledGraphs.has(cacheKey) && !options.isTest) {
      this.logger.debug(`Using cached compiled graph for workflow: ${workflow.id}`);
      return this.compiledGraphs.get(cacheKey)!;
    }

    // Build graph configuration
    const graphConfig: WorkflowGraphConfig = {
      stopOnError: options.stopOnError !== false,
      enableParallelExecution: options.enableParallel || false,
      isTest: options.isTest || false,
      enableDebugLogging: process.env.NODE_ENV === 'development',
      maxConcurrentNodes: 5,
      nodeTimeout: options.timeout || 300000, // 5 minutes default
    };

    // Build and compile graph
    const graph = await this.graphBuilder.buildGraph(workflow, graphConfig);
    const compiledGraph = graph.compile();

    // Cache compiled graph
    if (!options.isTest) {
      this.compiledGraphs.set(cacheKey, compiledGraph as any);
    }

    this.logger.debug(`Compiled graph for workflow: ${workflow.id}`);
    return compiledGraph as any;
  }

  /**
   * Initialize workflow state
   * @private
   */
  private async initializeWorkflowState(
    executionId: string,
    workflow: Workflow,
    options: WorkflowExecutionOptions
  ): Promise<WorkflowState> {
    const initialState: WorkflowState = {
      executionId,
      workflowId: workflow.id,
      userId: options.userId || 0,
      status: 'running',
      startTime: Date.now(),
      currentNode: null,
      nextNodes: [],
      completedNodes: [],
      failedNodes: [],
      nodeResults: {},
      variables: { ...(options.variables || {}) },
      triggerData: options.triggerData || {},
      triggerType: options.triggerType || 'manual',
      errors: [],
      lastError: null,
      messages: [],
      metadata: {
        workflowVersion:'1.0',
        executionOptions: options,
        environment: process.env.NODE_ENV || 'development',
      },
    };

    // Save initial state if checkpoints enabled
    if (options.saveCheckpoints) {
      await this.stateManager.saveCheckpoint(executionId, initialState, 'auto');
    }

    return initialState;
  }

  /**
   * Execute compiled graph
   * @private
   */
  private async executeGraph(
    compiledGraph: CompiledStateGraph<WorkflowState, Partial<WorkflowState>, string>,
    initialState: WorkflowState,
    options: WorkflowExecutionOptions
  ): Promise<WorkflowState> {
    const executionId = initialState.executionId;

    try {
      // Set up progress tracking
      let currentState = initialState;
      
      // Execute graph with streaming
      const stream = await compiledGraph.stream(initialState, {
        streamMode: 'values',
        configurable: {
          thread_id: executionId,
        },
      });

      // Process stream results
      for await (const chunk of stream) {
        currentState = { ...currentState, ...chunk };

        // Report progress
        if (options.progressCallback) {
          options.progressCallback(currentState);
        }

        // Emit progress event
        if (options.enableSSE) {
          await this.eventService.publishEvent('workflow.progress', {
            executionId,
            workflowId: currentState.workflowId,
            currentNode: currentState.currentNode,
            completedNodes: currentState.completedNodes.length,
            totalNodes: Object.keys(currentState.nodeResults).length,
            progress: this.calculateProgress(currentState),
          });
        }

        // Check for errors
        if (currentState.lastError && options.errorCallback) {
          options.errorCallback(currentState.lastError);
        }

        // Save checkpoint if enabled
        if (options.saveCheckpoints && currentState.currentNode) {
          await this.stateManager.saveCheckpoint(
            executionId,
            currentState,
            'auto',
            currentState.currentNode
          );
        }

        // Check timeout
        if (options.timeout && Date.now() - currentState.startTime > options.timeout) {
          throw new Error('Workflow execution timeout');
        }
      }

      // Update final status
      if (currentState.status === 'running') {
        currentState.status = currentState.failedNodes.length > 0 ? 'failed' : 'completed';
        currentState.endTime = Date.now();
      }

      return currentState;

    } catch (error) {
      this.logger.error(`Graph execution failed for ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Build execution result
   * @private
   */
  private async buildExecutionResult(
    executionId: string,
    workflow: Workflow,
    finalState: WorkflowState,
    startTime: number,
    endTime: number,
    options: WorkflowExecutionOptions
  ): Promise<WorkflowExecutionResult> {
    const duration = endTime - startTime;
    const totalNodes = workflow.nodes?.length || 0;

    // Extract output from final state
    const output = this.extractWorkflowOutput(finalState);

    // Calculate statistics
    const stats = {
      totalNodes,
      completedNodes: finalState.completedNodes.length,
      failedNodes: finalState.failedNodes.length,
      skippedNodes: totalNodes - finalState.completedNodes.length - finalState.failedNodes.length,
      executionTime: duration,
      memoryUsage: process.memoryUsage().heapUsed,
    };

    return {
      executionId,
      workflowId: workflow.id,
      status: finalState.status as 'completed' | 'failed' | 'cancelled',
      startTime,
      endTime,
      duration,
      output,
      nodeResults: finalState.nodeResults,
      finalState,
      stats,
      errors: finalState.errors,
      lastError: finalState.lastError || undefined,
      metadata: {
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checkpoints: options.saveCheckpoints ? await this.stateManager.getCheckpointIds(executionId) : [],
        events: [], // Could be populated with execution events
      },
    };
  }

  /**
   * Build failed execution result
   * @private
   */
  private buildFailedExecutionResult(
    executionId: string,
    workflow: Workflow,
    error: Error,
    startTime: number,
    endTime: number
  ): WorkflowExecutionResult {
    const duration = endTime - startTime;

    return {
      executionId,
      workflowId: workflow.id,
      status: 'failed',
      startTime,
      endTime,
      duration,
      output: null,
      nodeResults: {},
      finalState: {
        executionId,
        workflowId: workflow.id,
        userId: 0,
        status: 'failed',
        startTime,
        endTime,
        currentNode: null,
        nextNodes: [],
        completedNodes: [],
        failedNodes: [],
        nodeResults: {},
        variables: {},
        triggerData: {},
        triggerType: 'manual',
        errors: [{
          nodeId: 'system',
          nodeType: 'system',
          error: error.message,
          timestamp: Date.now(),
          stack: error.stack,
        }],
        lastError: {
          nodeId: 'system',
          nodeType: 'system',
          error: error.message,
          timestamp: Date.now(),
          stack: error.stack,
        },
        messages: [],
      },
      stats: {
        totalNodes: workflow.nodes?.length || 0,
        completedNodes: 0,
        failedNodes: 0,
        skippedNodes: workflow.nodes?.length || 0,
        executionTime: duration,
        memoryUsage: process.memoryUsage().heapUsed,
      },
      errors: [{
        nodeId: 'system',
        nodeType: 'system',
        error: error.message,
        timestamp: Date.now(),
        stack: error.stack,
      }],
      lastError: {
        nodeId: 'system',
        nodeType: 'system',
        error: error.message,
        timestamp: Date.now(),
        stack: error.stack,
      },
      metadata: {
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checkpoints: [],
        events: [],
      },
    };
  }

  /**
   * Extract workflow output from final state
   * @private
   */
  private extractWorkflowOutput(finalState: WorkflowState): any {
    // Look for output nodes
    const outputResults = Object.entries(finalState.nodeResults)
      .filter(([nodeId]) => nodeId.includes('output') || nodeId.includes('result'))
      .map(([nodeId, result]) => result);

    if (outputResults.length > 0) {
      return outputResults.length === 1 ? outputResults[0] : outputResults;
    }

    // Return all node results if no specific output
    return finalState.nodeResults;
  }

  /**
   * Calculate execution progress
   * @private
   */
  private calculateProgress(state: WorkflowState): number {
    const totalNodes = Object.keys(state.nodeResults).length + state.completedNodes.length + state.failedNodes.length;
    const processedNodes = state.completedNodes.length + state.failedNodes.length;
    
    return totalNodes > 0 ? Math.round((processedNodes / totalNodes) * 100) : 0;
  }

  /**
   * Generate unique execution ID
   * @private
   */
  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clear compiled graph cache
   */
  clearGraphCache(workflowId?: string): void {
    if (workflowId) {
      const keysToDelete = Array.from(this.compiledGraphs.keys())
        .filter(key => key.startsWith(workflowId));
      keysToDelete.forEach(key => this.compiledGraphs.delete(key));
    } else {
      this.compiledGraphs.clear();
    }
    
    this.logger.debug(`Cleared graph cache${workflowId ? ` for workflow: ${workflowId}` : ''}`);
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(executionId: string): GraphExecutionStats | null {
    return this.executionStats.get(executionId) || null;
  }
}
