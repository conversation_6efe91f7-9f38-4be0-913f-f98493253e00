import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Output Node Executor
 * Handles workflow output operations and result formatting
 */
@Injectable()
export class OutputExecutor extends BaseNodeExecutor {
  readonly type = 'system.output';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Output';
  readonly description = 'Define workflow output data and format results';

  /**
   * Execute output operation
   * @param inputs - Output configuration and data
   * @param context - Execution context
   * @returns Formatted output data
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      data,
      format = 'json',
      schema,
      metadata = {},
      includeMetadata = true,
      transform,
      validation = true,
    } = inputs;

    this.logger.debug(`Processing workflow output with format: ${format}`);

    try {
      let outputData = data;

      // Apply transformation if specified
      if (transform) {
        outputData = await this.applyTransformation(outputData, transform, context);
      }

      // Validate output against schema if provided
      if (validation && schema) {
        await this.validateOutput(outputData, schema);
      }

      // Format output according to specified format
      const formattedOutput = await this.formatOutput(outputData, format, context);

      // Prepare final output
      const result = {
        data: formattedOutput,
        format,
        generatedAt: new Date().toISOString(),
        executionId: context.executionId,
        workflowId: context.workflowId,
      };

      // Include metadata if requested
      if (includeMetadata) {
        result['metadata'] = {
          ...metadata,
          outputSize: this.calculateOutputSize(formattedOutput),
          processingTime: Date.now() - context.startTime,
          nodeCount: Object.keys(context.nodeResults || {}).length,
          userId: context.userId,
          version: '1.0.0',
        };
      }

      // Emit output event
      await this.emitEvent('workflow.output', {
        executionId: context.executionId,
        workflowId: context.workflowId,
        format,
        size: this.calculateOutputSize(formattedOutput),
      }, context);

      this.logger.debug(`Workflow output processed successfully`);

      return result;

    } catch (error) {
      this.logger.error(`Output processing failed: ${error.message}`);
      throw new Error(`Output processing failed: ${error.message}`);
    }
  }

  /**
   * Apply transformation to output data
   * @private
   */
  private async applyTransformation(data: any, transform: any, context: ExecutionContext): Promise<any> {
    const { type, config = {} } = transform;

    switch (type) {
      case 'filter':
        return this.filterData(data, config);
      case 'map':
        return this.mapData(data, config);
      case 'reduce':
        return this.reduceData(data, config);
      case 'sort':
        return this.sortData(data, config);
      case 'group':
        return this.groupData(data, config);
      case 'flatten':
        return this.flattenData(data, config);
      case 'custom':
        return this.customTransform(data, config, context);
      default:
        throw new Error(`Unsupported transformation type: ${type}`);
    }
  }

  /**
   * Format output according to specified format
   * @private
   */
  private async formatOutput(data: any, format: string, context: ExecutionContext): Promise<any> {
    switch (format.toLowerCase()) {
      case 'json':
        return data;
      case 'xml':
        return this.convertToXml(data);
      case 'csv':
        return this.convertToCsv(data);
      case 'text':
        return this.convertToText(data);
      case 'yaml':
        return this.convertToYaml(data);
      case 'html':
        return this.convertToHtml(data);
      case 'markdown':
        return this.convertToMarkdown(data);
      default:
        throw new Error(`Unsupported output format: ${format}`);
    }
  }

  /**
   * Validate output against schema
   * @private
   */
  private async validateOutput(data: any, schema: any): Promise<void> {
    // Simple validation implementation
    // In production, use a proper JSON schema validator like Ajv
    if (schema.type && typeof data !== schema.type) {
      throw new Error(`Output validation failed: expected ${schema.type}, got ${typeof data}`);
    }

    if (schema.required && Array.isArray(schema.required)) {
      for (const field of schema.required) {
        if (data[field] === undefined) {
          throw new Error(`Output validation failed: missing required field '${field}'`);
        }
      }
    }
  }

  /**
   * Filter data based on configuration
   * @private
   */
  private filterData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const { condition, limit, offset = 0 } = config;

    let filtered = data;

    if (condition) {
      filtered = data.filter((item, index) => {
        try {
          const func = new Function('item', 'index', `return ${condition}`);
          return func(item, index);
        } catch {
          return false;
        }
      });
    }

    if (offset > 0) {
      filtered = filtered.slice(offset);
    }

    if (limit && limit > 0) {
      filtered = filtered.slice(0, limit);
    }

    return filtered;
  }

  /**
   * Map data based on configuration
   * @private
   */
  private mapData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const { expression } = config;

    if (!expression) {
      return data;
    }

    return data.map((item, index) => {
      try {
        const func = new Function('item', 'index', `return ${expression}`);
        return func(item, index);
      } catch {
        return item;
      }
    });
  }

  /**
   * Reduce data based on configuration
   * @private
   */
  private reduceData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const { expression, initialValue = {} } = config;

    if (!expression) {
      return data;
    }

    return data.reduce((acc, item, index) => {
      try {
        const func = new Function('acc', 'item', 'index', `return ${expression}`);
        return func(acc, item, index);
      } catch {
        return acc;
      }
    }, initialValue);
  }

  /**
   * Sort data based on configuration
   * @private
   */
  private sortData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const { field, order = 'asc' } = config;

    if (!field) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aVal = this.getNestedValue(a, field);
      const bVal = this.getNestedValue(b, field);

      if (aVal < bVal) return order === 'asc' ? -1 : 1;
      if (aVal > bVal) return order === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Group data based on configuration
   * @private
   */
  private groupData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const { field } = config;

    if (!field) {
      return data;
    }

    const groups: { [key: string]: any[] } = {};

    data.forEach(item => {
      const groupKey = String(this.getNestedValue(item, field));
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
    });

    return groups;
  }

  /**
   * Flatten nested data
   * @private
   */
  private flattenData(data: any, config: any): any {
    const { depth = 1 } = config;

    if (Array.isArray(data)) {
      return data.flat(depth);
    }

    return data;
  }

  /**
   * Apply custom transformation
   * @private
   */
  private customTransform(data: any, config: any, context: ExecutionContext): any {
    const { script } = config;

    if (!script) {
      return data;
    }

    try {
      const func = new Function('data', 'context', script);
      return func(data, context);
    } catch (error) {
      this.logger.warn(`Custom transformation failed: ${error.message}`);
      return data;
    }
  }

  /**
   * Convert data to XML format
   * @private
   */
  private convertToXml(data: any): string {
    // Simple XML conversion
    const xmlify = (obj: any, indent = 0): string => {
      const spaces = '  '.repeat(indent);
      
      if (typeof obj !== 'object' || obj === null) {
        return String(obj);
      }

      if (Array.isArray(obj)) {
        return obj.map(item => `${spaces}<item>\n${xmlify(item, indent + 1)}\n${spaces}</item>`).join('\n');
      }

      return Object.entries(obj)
        .map(([key, value]) => `${spaces}<${key}>\n${xmlify(value, indent + 1)}\n${spaces}</${key}>`)
        .join('\n');
    };

    return `<?xml version="1.0" encoding="UTF-8"?>\n<root>\n${xmlify(data, 1)}\n</root>`;
  }

  /**
   * Convert data to CSV format
   * @private
   */
  private convertToCsv(data: any): string {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : String(value);
        }).join(',')
      )
    ];

    return csvRows.join('\n');
  }

  /**
   * Convert data to text format
   * @private
   */
  private convertToText(data: any): string {
    if (typeof data === 'string') {
      return data;
    }

    return JSON.stringify(data, null, 2);
  }

  /**
   * Convert data to YAML format
   * @private
   */
  private convertToYaml(data: any): string {
    // Simple YAML conversion
    const yamlify = (obj: any, indent = 0): string => {
      const spaces = '  '.repeat(indent);

      if (typeof obj !== 'object' || obj === null) {
        return String(obj);
      }

      if (Array.isArray(obj)) {
        return obj.map(item => `${spaces}- ${yamlify(item, indent + 1)}`).join('\n');
      }

      return Object.entries(obj)
        .map(([key, value]) => `${spaces}${key}: ${yamlify(value, indent + 1)}`)
        .join('\n');
    };

    return yamlify(data);
  }

  /**
   * Convert data to HTML format
   * @private
   */
  private convertToHtml(data: any): string {
    if (Array.isArray(data)) {
      const headers = data.length > 0 ? Object.keys(data[0]) : [];
      const rows = data.map(row => 
        `<tr>${headers.map(header => `<td>${row[header]}</td>`).join('')}</tr>`
      ).join('');

      return `
        <table>
          <thead>
            <tr>${headers.map(header => `<th>${header}</th>`).join('')}</tr>
          </thead>
          <tbody>
            ${rows}
          </tbody>
        </table>
      `;
    }

    return `<pre>${JSON.stringify(data, null, 2)}</pre>`;
  }

  /**
   * Convert data to Markdown format
   * @private
   */
  private convertToMarkdown(data: any): string {
    if (Array.isArray(data) && data.length > 0) {
      const headers = Object.keys(data[0]);
      const headerRow = `| ${headers.join(' | ')} |`;
      const separatorRow = `| ${headers.map(() => '---').join(' | ')} |`;
      const dataRows = data.map(row => 
        `| ${headers.map(header => row[header]).join(' | ')} |`
      ).join('\n');

      return `${headerRow}\n${separatorRow}\n${dataRows}`;
    }

    return `\`\`\`json\n${JSON.stringify(data, null, 2)}\n\`\`\``;
  }

  /**
   * Get nested value from object
   * @private
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Calculate output size
   * @private
   */
  private calculateOutputSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['data'],
      properties: {
        data: {
          description: 'Data to output from the workflow',
        },
        format: {
          type: 'string',
          enum: ['json', 'xml', 'csv', 'text', 'yaml', 'html', 'markdown'],
          default: 'json',
          description: 'Output format',
        },
        schema: {
          type: 'object',
          description: 'Schema for output validation',
        },
        metadata: {
          type: 'object',
          description: 'Additional metadata to include in output',
        },
        includeMetadata: {
          type: 'boolean',
          default: true,
          description: 'Whether to include execution metadata',
        },
        transform: {
          type: 'object',
          description: 'Transformation to apply to output data',
        },
        validation: {
          type: 'boolean',
          default: true,
          description: 'Whether to validate output against schema',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        data: {
          description: 'Formatted output data',
        },
        format: {
          type: 'string',
          description: 'Output format used',
        },
        generatedAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when output was generated',
        },
        executionId: {
          type: 'string',
          description: 'Workflow execution ID',
        },
        workflowId: {
          type: 'string',
          description: 'Workflow ID',
        },
        metadata: {
          type: 'object',
          description: 'Execution metadata and statistics',
        },
      },
    };
  }
}
