import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { GoogleApiMinimalModule } from '../../../../shared/services/google/google-api-minimal.module';

// Import Google Sheets Executors
import { GoogleSheetsCreateExecutor } from './sheets/google-sheets-create.executor';
import { GoogleSheetsAppendRowExecutor } from './sheets/google-sheets-update-values.executor';
import { GoogleSheetsReadExecutor } from './sheets/google-sheets-read.executor';
import { GoogleSheetsClearExecutor } from './sheets/google-sheets-clear.executor';
import { GoogleSheetsUpdateRowExecutor } from './sheets/google-sheets-update-row.executor';
import { GoogleSheetsDeleteRowExecutor } from './sheets/google-sheets-delete-row.executor';
import { GoogleSheetsFormatCellsExecutor } from './sheets/google-sheets-format-cells.executor';
import { GoogleSheetsAddSheetExecutor } from './sheets/google-sheets-add-sheet.executor';
import { GoogleSheetsDeleteSheetExecutor } from './sheets/google-sheets-delete-sheet.executor';
import { GoogleSheetsCopySheetExecutor } from './sheets/google-sheets-copy-sheet.executor';
import { GoogleSheetsTestController } from './sheets/google-sheets-test.controller';

// Import Google Calendar Executors
import { GoogleCalendarCreateEventExecutor } from './calendar/google-calendar-create-event.executor';
import { GoogleCalendarListEventsExecutor } from './calendar/google-calendar-list-events.executor';
import { GoogleCalendarUpdateEventExecutor } from './calendar/google-calendar-update-event.executor';
import { GoogleCalendarDeleteEventExecutor } from './calendar/google-calendar-delete-event.executor';

// Import Google Analytics Executors
import { GoogleAnalyticsRunReportExecutor } from './analytics/google-analytics-run-report.executor';
import { GoogleAnalyticsPageViewsExecutor } from './analytics/google-analytics-page-views.executor';

// Import Google Ads Executors - TODO: Fix all syntax errors and re-enable
// import { GoogleAdsGetCampaignsExecutor } from './ads/google-ads-get-campaigns.executor';
// import { GoogleAdsCreateCampaignExecutor } from './ads/google-ads-create-campaign.executor';
// import { GoogleAdsGetAdGroupsExecutor } from './ads/google-ads-get-ad-groups.executor';
// import { GoogleAdsCreateAdGroupExecutor } from './ads/google-ads-create-ad-group.executor';
// import { GoogleAdsGetKeywordsExecutor } from './ads/google-ads-get-keywords.executor';
// import { GoogleAdsCreateKeywordExecutor } from './ads/google-ads-create-keyword.executor';
// import { GoogleAdsUpdateKeywordExecutor } from './ads/google-ads-update-keyword.executor';
// import { GoogleAdsGetPerformanceReportExecutor } from './ads/google-ads-get-performance-report.executor';
// import { GoogleAdsUpdateCampaignExecutor } from './ads/google-ads-update-campaign.executor';
// import { GoogleAdsDeleteCampaignExecutor } from './ads/google-ads-delete-campaign.executor';
// import { GoogleAdsUpdateAdGroupExecutor } from './ads/google-ads-update-ad-group.executor';
// import { GoogleAdsCreateTextAdExecutor } from './ads/google-ads-create-text-ad.executor';
// import { GoogleAdsGetAdsExecutor } from './ads/google-ads-get-ads.executor';
// import { GoogleAdsUpdateAdExecutor } from './ads/google-ads-update-ad.executor';
// import { GoogleAdsDeleteAdExecutor } from './ads/google-ads-delete-ad.executor';

// Import Google Drive Executors - TODO: Fix syntax errors
// import { GoogleDriveUploadFileExecutor } from './drive/google-drive-upload-file.executor';
// import { GoogleDriveDownloadFileExecutor } from './drive/google-drive-download-file.executor';
// import { GoogleDriveCreateFolderExecutor } from './drive/google-drive-create-folder.executor';
// import { GoogleDriveSearchFilesExecutor } from './drive/google-drive-search-files.executor';
// TODO: Fix syntax errors
// import { GoogleDriveListFilesExecutor } from './drive/google-drive-list-files.executor';
// import { GoogleDriveDeleteFileExecutor } from './drive/google-drive-delete-file.executor';
// import { GoogleDriveShareFileExecutor } from './drive/google-drive-share-file.executor';
// import { GoogleDriveCopyFileExecutor } from './drive/google-drive-copy-file.executor';
// import { GoogleDriveMoveFileExecutor } from './drive/google-drive-move-file.executor';
// import { GoogleDriveManagePermissionsExecutor } from './drive/google-drive-manage-permissions.executor';

// Import Google Ads Advanced Executors - TODO: Fix all syntax errors and re-enable
// import { GoogleAdsPauseCampaignExecutor } from './ads/google-ads-pause-campaign.executor';
// import { GoogleAdsUpdateCampaignBudgetExecutor } from './ads/google-ads-update-campaign-budget.executor';
// import { GoogleAdsDeleteAdGroupExecutor } from './ads/google-ads-delete-ad-group.executor';
// import { GoogleAdsDeleteKeywordExecutor } from './ads/google-ads-delete-keyword.executor';
// import { GoogleAdsCreateCustomAudienceExecutor } from './ads/google-ads-create-custom-audience.executor';
// import { GoogleAdsUpdateCampaignTargetingExecutor } from './ads/google-ads-update-campaign-targeting.executor';
// import { GoogleAdsManageCampaignExtensionsExecutor } from './ads/google-ads-manage-campaign-extensions.executor';
// import { GoogleAdsUpdateCampaignScheduleExecutor } from './ads/google-ads-update-campaign-schedule.executor';
// import { GoogleAdsUpdateCampaignBidStrategyExecutor } from './ads/google-ads-update-campaign-bid-strategy.executor';
// import { GoogleAdsUpdateAdGroupBidsExecutor } from './ads/google-ads-update-ad-group-bids.executor';
// import { GoogleAdsUpdateAdGroupDemographicsExecutor } from './ads/google-ads-update-ad-group-demographics.executor';
// import { GoogleAdsManageAdGroupNegativeKeywordsExecutor } from './ads/google-ads-manage-ad-group-negative-keywords.executor';
// import { GoogleAdsUpdateAdGroupAudiencesExecutor } from './ads/google-ads-update-ad-group-audiences.executor';
// import { GoogleAdsUpdateKeywordBidsExecutor } from './ads/google-ads-update-keyword-bids.executor';
// import { GoogleAdsAnalyzeKeywordQualityScoreExecutor } from './ads/google-ads-analyze-keyword-quality-score.executor';
// import { GoogleAdsKeywordResearchExecutor } from './ads/google-ads-keyword-research.executor'; // TODO: Fix syntax errors
// import { GoogleAdsKeywordBulkOperationsExecutor } from './ads/google-ads-keyword-bulk-operations.executor';
// import { GoogleAdsKeywordPerformanceAnalysisExecutor } from './ads/google-ads-keyword-performance-analysis.executor'; // TODO: Fix syntax errors
// import { GoogleAdsCreateImageAdExecutor } from './ads/google-ads-create-image-ad.executor';
// import { GoogleAdsCreateVideoAdExecutor } from './ads/google-ads-create-video-ad.executor';
// import { GoogleAdsManageAdExtensionsExecutor } from './ads/google-ads-manage-ad-extensions.executor';
// import { GoogleAdsAdPerformanceAnalysisExecutor } from './ads/google-ads-ad-performance-analysis.executor'; // TODO: Fix syntax errors
// import { GoogleAdsAdABTestingExecutor } from './ads/google-ads-ad-ab-testing.executor';
// import { GoogleAdsAdCreativeOptimizationExecutor } from './ads/google-ads-ad-creative-optimization.executor'; // TODO: Fix syntax errors

/**
 * Module chứa tất cả Google service executors
 */
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    GoogleApiMinimalModule, // Import minimal Google services
  ],
  controllers: [
    GoogleSheetsTestController, // Test controller for development
  ],
  providers: [
    // Google Sheets Executors (10 total)
    GoogleSheetsCreateExecutor,
    GoogleSheetsAppendRowExecutor,
    GoogleSheetsReadExecutor,
    GoogleSheetsClearExecutor,
    GoogleSheetsUpdateRowExecutor,
    GoogleSheetsDeleteRowExecutor,
    GoogleSheetsFormatCellsExecutor,
    GoogleSheetsAddSheetExecutor,
    GoogleSheetsDeleteSheetExecutor,
    GoogleSheetsCopySheetExecutor,

    // Google Calendar Executors (4/4 total) ✅
    GoogleCalendarCreateEventExecutor,
    GoogleCalendarListEventsExecutor,
    GoogleCalendarUpdateEventExecutor,
    GoogleCalendarDeleteEventExecutor,

    // Google Analytics Executors (2/2 total) ✅
    GoogleAnalyticsRunReportExecutor,
    GoogleAnalyticsPageViewsExecutor,

    // Google Ads Executors (39 total) - TODO: Fix all syntax errors and re-enable
    // GoogleAdsGetCampaignsExecutor,
    // GoogleAdsCreateCampaignExecutor,
    // GoogleAdsUpdateCampaignExecutor,
    // GoogleAdsDeleteCampaignExecutor,
    // GoogleAdsPauseCampaignExecutor,
    // GoogleAdsUpdateCampaignBudgetExecutor,
    // GoogleAdsUpdateCampaignTargetingExecutor,
    // GoogleAdsManageCampaignExtensionsExecutor,
    // GoogleAdsUpdateCampaignScheduleExecutor,
    // GoogleAdsUpdateCampaignBidStrategyExecutor,
    // GoogleAdsGetAdGroupsExecutor,
    // GoogleAdsCreateAdGroupExecutor,
    // GoogleAdsUpdateAdGroupExecutor,
    // GoogleAdsDeleteAdGroupExecutor,
    // GoogleAdsUpdateAdGroupBidsExecutor,
    // GoogleAdsUpdateAdGroupDemographicsExecutor,
    // GoogleAdsManageAdGroupNegativeKeywordsExecutor,
    // GoogleAdsUpdateAdGroupAudiencesExecutor,
    // GoogleAdsGetKeywordsExecutor,
    // GoogleAdsCreateKeywordExecutor,
    // GoogleAdsUpdateKeywordExecutor,
    // GoogleAdsDeleteKeywordExecutor,
    // GoogleAdsUpdateKeywordBidsExecutor,
    // GoogleAdsAnalyzeKeywordQualityScoreExecutor,
    // GoogleAdsKeywordResearchExecutor, // TODO: Fix syntax errors
    // GoogleAdsKeywordBulkOperationsExecutor,
    // GoogleAdsKeywordPerformanceAnalysisExecutor, // TODO: Fix syntax errors
    // GoogleAdsCreateTextAdExecutor,
    // GoogleAdsGetAdsExecutor,
    // GoogleAdsUpdateAdExecutor,
    // GoogleAdsDeleteAdExecutor,
    // GoogleAdsCreateImageAdExecutor,
    // GoogleAdsCreateVideoAdExecutor,
    // GoogleAdsManageAdExtensionsExecutor,
    // GoogleAdsAdPerformanceAnalysisExecutor, // TODO: Fix syntax errors
    // GoogleAdsAdABTestingExecutor,
    // GoogleAdsAdCreativeOptimizationExecutor, // TODO: Fix syntax errors
    // GoogleAdsCreateCustomAudienceExecutor,
    // GoogleAdsGetPerformanceReportExecutor,

    // Google Drive Executors (4 total) - TODO: Fix syntax errors
    // GoogleDriveUploadFileExecutor,
    // GoogleDriveDownloadFileExecutor,
    // GoogleDriveCreateFolderExecutor,
    // GoogleDriveSearchFilesExecutor,
  ],
  exports: [
    // Google Sheets Executors (10 total)
    GoogleSheetsCreateExecutor,
    GoogleSheetsAppendRowExecutor,
    GoogleSheetsReadExecutor,
    GoogleSheetsClearExecutor,
    GoogleSheetsUpdateRowExecutor,
    GoogleSheetsDeleteRowExecutor,
    GoogleSheetsFormatCellsExecutor,
    GoogleSheetsAddSheetExecutor,
    GoogleSheetsDeleteSheetExecutor,
    GoogleSheetsCopySheetExecutor,

    // Google Calendar Executors (4/4 total) ✅
    GoogleCalendarCreateEventExecutor,
    GoogleCalendarListEventsExecutor,
    GoogleCalendarUpdateEventExecutor,
    GoogleCalendarDeleteEventExecutor,

    // Google Analytics Executors (2/2 total) ✅
    GoogleAnalyticsRunReportExecutor,
    GoogleAnalyticsPageViewsExecutor,

    // Google Ads Executors (15 total) - TODO: Fix syntax errors
    // GoogleAdsGetCampaignsExecutor,
    // GoogleAdsCreateCampaignExecutor,
    // GoogleAdsUpdateCampaignExecutor,
    // GoogleAdsDeleteCampaignExecutor,
    // GoogleAdsGetAdGroupsExecutor,
    // GoogleAdsCreateAdGroupExecutor,
    // GoogleAdsUpdateAdGroupExecutor,
    // GoogleAdsGetKeywordsExecutor,
    // GoogleAdsCreateKeywordExecutor,
    // GoogleAdsUpdateKeywordExecutor,
    // GoogleAdsCreateTextAdExecutor,
    // GoogleAdsGetAdsExecutor,
    // GoogleAdsUpdateAdExecutor,
    // GoogleAdsDeleteAdExecutor,
    // GoogleAdsGetPerformanceReportExecutor,

    // Google Drive Executors (10 total) - TODO: Fix syntax errors
    // GoogleDriveUploadFileExecutor,
    // GoogleDriveDownloadFileExecutor,
    // GoogleDriveCreateFolderExecutor,
    // GoogleDriveSearchFilesExecutor,
    // TODO: Fix syntax errors
    // GoogleDriveListFilesExecutor,
    // GoogleDriveDeleteFileExecutor,
    // GoogleDriveShareFileExecutor,
    // GoogleDriveCopyFileExecutor,
    // GoogleDriveMoveFileExecutor,
    // GoogleDriveManagePermissionsExecutor,
  ],
})
export class GoogleExecutorsModule {}
