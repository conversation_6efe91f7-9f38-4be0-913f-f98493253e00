import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleCalendarService } from '../../../../../shared/services/google/calendar/google-calendar.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Calendar List Events node
 */
export interface GoogleCalendarListEventsInput {

  /**
   * ID của calendar (mặc định: 'primary')
   */
  calendarId?: string;

  /**
   * Thời gian bắt đầu tìm kiếm (ISO string)
   */
  timeMin?: string;

  /**
   * Thời gian kết thúc tìm kiếm (ISO string)
   */
  timeMax?: string;

  /**
   * Số lượng events tối đa (mặc định: 10)
   */
  maxResults?: number;

  /**
   * Từ khóa tìm kiếm
   */
  query?: string;

  /**
   * Sắp xếp theo (startTime hoặc updated)
   */
  orderBy?: 'startTime' | 'updated';

  /**
   * Chỉ lấy single events (không lấy recurring)
   */
  singleEvents?: boolean;

  /**
   * Timezone
   */
  timeZone?: string;

  /**
   * Page token cho pagination
   */
  pageToken?: string;
}

/**
 * Output schema cho Google Calendar List Events node
 */
export interface GoogleCalendarListEventsOutput {
  /**
   * Danh sách events
   */
  events: Array<{
    id: string;
    summary: string;
    description?: string;
    location?: string;
    startDateTime: string;
    endDateTime: string;
    allDay: boolean;
    status: string;
    htmlLink: string;
    meetingLink?: string;
    attendees?: Array<{
      email: string;
      responseStatus: string;
      displayName?: string;
    }>;
    creator: {
      email: string;
      displayName?: string;
    };
    organizer: {
      email: string;
      displayName?: string;
    };
    colorId?: string;
    visibility?: string;
    recurrence?: string[];
  }>;

  /**
   * Metadata
   */
  metadata: {
    calendarId: string;
    totalEvents: number;
    nextPageToken?: string;
    timeMin?: string;
    timeMax?: string;
    query?: string;
  };
}

/**
 * Executor để lấy danh sách events từ Google Calendar
 */
@Injectable()
export class GoogleCalendarListEventsExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.calendar.list_events';
  readonly category = NodeCategory.GOOGLE_CALENDAR;
  readonly name = 'List Calendar Events';
  readonly description = 'Lấy danh sách events từ Google Calendar';

  constructor(
    integrationService: IntegrationService,
    private readonly calendarService: GoogleCalendarService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        calendarId: { type: 'string' },
        timeMin: { type: 'string' },
        timeMax: { type: 'string' },
        maxResults: { type: 'number', minimum: 1, maximum: 2500 },
        query: { type: 'string' },
        orderBy: { type: 'string', enum: ['startTime', 'updated'] },
        singleEvents: { type: 'boolean' },
        timeZone: { type: 'string' },
        pageToken: { type: 'string' },
      },
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        events: {
          type: 'array',
          items: { type: 'object' },
        },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node lấy danh sách events
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Listing calendar events from: ${inputs.calendarId || 'primary'}`);

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Prepare request parameters
      const calendarId = inputs.calendarId || 'primary';
      const listRequest = {
        calendarId,
        timeMin: inputs.timeMin,
        timeMax: inputs.timeMax,
        maxResults: inputs.maxResults || 10,
        q: inputs.query,
        orderBy: inputs.orderBy,
        singleEvents: inputs.singleEvents !== false, // Default to true
        timeZone: inputs.timeZone || 'Asia/Ho_Chi_Minh',
        pageToken: inputs.pageToken,
      };

      // Get events
      const eventsResult = await this.calendarService.listEvents(
        accessToken,
        listRequest,
      );

      // Map events to output format
      const events = eventsResult.items.map(event => ({
        id: event.id || '',
        summary: event.summary || '',
        description: event.description,
        location: event.location,
        startDateTime: event.start?.dateTime || event.start?.date || '',
        endDateTime: event.end?.dateTime || event.end?.date || '',
        allDay: !!event.start?.date, // If date is set instead of dateTime, it's all day
        status: event.status || '',
        htmlLink: event.htmlLink || '',
        meetingLink: event.hangoutLink,
        attendees: event.attendees?.map(attendee => ({
          email: attendee.email || '',
          responseStatus: attendee.responseStatus || 'needsAction',
          displayName: attendee.displayName,
        })),
        creator: {
          email: event.creator?.email || '',
          displayName: event.creator?.displayName,
        },
        organizer: {
          email: event.organizer?.email || '',
          displayName: event.organizer?.displayName,
        },
        colorId: event.colorId,
        visibility: event.visibility,
        recurrence: event.recurrence,
      }));

      // Prepare output
      const output: GoogleCalendarListEventsOutput = {
        events,
        metadata: {
          calendarId,
          totalEvents: events.length,
          nextPageToken: eventsResult.nextPageToken,
          timeMin: inputs.timeMin,
          timeMax: inputs.timeMax,
          query: inputs.query,
        },
      };

      this.logger.log(`Retrieved ${events.length} calendar events`);

      return output;
    });
  }
}
