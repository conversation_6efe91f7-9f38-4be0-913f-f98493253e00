import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để xóa dữ liệu trong Google Sheets
 */
@Injectable()
export class GoogleSheetsClearExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.clearRange';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Clear Google Sheets Data';
  readonly description = 'Xóa dữ liệu trong Google Sheets theo range chỉ định';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node xóa dữ liệu Google Sheets
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const { 
        spreadsheetId, 
        range
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (!range) {
        throw new Error('Range is required');
      }

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Clear data using GoogleSheetsService
      const result = await this.googleSheetsService.clearData(
        accessToken,
        spreadsheetId,
        range
      );

      // Return formatted result
      return {
        spreadsheetId,
        range: result.clearedRange,
        success: true,
        clearedAt: new Date().toISOString(),
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (!inputs.range || typeof inputs.range !== 'string') {
      throw new Error('Range must be a non-empty string');
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        range: {
          type: 'string',
          description: 'Phạm vi cells để xóa (ví dụ: "Sheet1!A1:C10")',
          pattern: '^[^!]+![A-Z]+[0-9]+:[A-Z]+[0-9]+$',
        },
      },
      required: ['spreadsheetId', 'range'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet',
        },
        range: {
          type: 'string',
          description: 'Phạm vi đã được xóa',
        },
        success: {
          type: 'boolean',
          description: 'Trạng thái thành công',
        },
        clearedAt: {
          type: 'string',
          description: 'Thời gian xóa',
        },
      },
    };
  }
}
