import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleCalendarService } from '../../../../../shared/services/google/calendar/google-calendar.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Calendar Update Event node
 */
export interface GoogleCalendarUpdateEventInput {
  /**
   * Access token cho Google Calendar API
   */
  accessToken: string;

  /**
   * ID của calendar (mặc định: 'primary')
   */
  calendarId?: string;

  /**
   * ID của event cần update
   */
  eventId: string;

  /**
   * Tiêu đề event (optional)
   */
  summary?: string;

  /**
   * <PERSON><PERSON> tả event (optional)
   */
  description?: string;

  /**
   * Đ<PERSON><PERSON> điểm (optional)
   */
  location?: string;

  /**
   * Thời gian bắt đầu (ISO string) (optional)
   */
  startDateTime?: string;

  /**
   * Thời gian kết thúc (ISO string) (optional)
   */
  endDateTime?: string;

  /**
   * Timezone (optional)
   */
  timeZone?: string;

  /**
   * Danh sách email người tham dự (optional)
   */
  attendees?: string[];

  /**
   * Có gửi thông báo không
   */
  sendNotifications?: boolean;

  /**
   * Có phải event cả ngày không (optional)
   */
  allDay?: boolean;

  /**
   * Màu sắc event (1-11) (optional)
   */
  colorId?: string;

  /**
   * Visibility của event (optional)
   */
  visibility?: 'default' | 'public' | 'private' | 'confidential';

  /**
   * Status của event (optional)
   */
  status?: 'confirmed' | 'tentative' | 'cancelled';
}

/**
 * Output schema cho Google Calendar Update Event node
 */
export interface GoogleCalendarUpdateEventOutput {
  /**
   * ID của event đã update
   */
  eventId: string;

  /**
   * Link HTML để xem event
   */
  htmlLink: string;

  /**
   * Link để join meeting (nếu có)
   */
  meetingLink?: string;

  /**
   * Thông tin event đã update
   */
  event: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    startDateTime: string;
    endDateTime: string;
    status: string;
    attendees?: Array<{
      email: string;
      responseStatus: string;
    }>;
    creator: {
      email: string;
      displayName?: string;
    };
    organizer: {
      email: string;
      displayName?: string;
    };
  };

  /**
   * Metadata
   */
  metadata: {
    calendarId: string;
    updated: string;
    sequence: number;
  };
}

/**
 * Executor để update event trong Google Calendar
 */
@Injectable()
export class GoogleCalendarUpdateEventExecutor implements NodeExecutor {
  private readonly logger = new Logger(GoogleCalendarUpdateEventExecutor.name);

  constructor(private readonly calendarService: GoogleCalendarService) {}

  /**
   * Thực thi node update event
   */
  async execute(
    inputs: GoogleCalendarUpdateEventInput,
    context: ExecutionContext,
  ): Promise<NodeExecutionResult> {
    try {
      this.logger.log(`Updating calendar event: ${inputs.eventId}`);

      // Validate required inputs
      if (!inputs.accessToken) {
        throw new Error('Access token is required');
      }

      if (!inputs.eventId) {
        throw new Error('Event ID is required');
      }

      // Prepare update data
      const calendarId = inputs.calendarId || 'primary';
      const timeZone = inputs.timeZone || 'Asia/Ho_Chi_Minh';

      const updateData: any = {};

      // Only update fields that are provided
      if (inputs.summary !== undefined) {
        updateData.summary = inputs.summary;
      }

      if (inputs.description !== undefined) {
        updateData.description = inputs.description;
      }

      if (inputs.location !== undefined) {
        updateData.location = inputs.location;
      }

      if (inputs.startDateTime !== undefined && inputs.endDateTime !== undefined) {
        updateData.start = inputs.allDay ? {
          date: inputs.startDateTime.split('T')[0],
          timeZone,
        } : {
          dateTime: inputs.startDateTime,
          timeZone,
        };

        updateData.end = inputs.allDay ? {
          date: inputs.endDateTime.split('T')[0],
          timeZone,
        } : {
          dateTime: inputs.endDateTime,
          timeZone,
        };
      }

      if (inputs.attendees !== undefined) {
        updateData.attendees = inputs.attendees.map(email => ({ email }));
      }

      if (inputs.colorId !== undefined) {
        updateData.colorId = inputs.colorId;
      }

      if (inputs.visibility !== undefined) {
        updateData.visibility = inputs.visibility;
      }

      if (inputs.status !== undefined) {
        updateData.status = inputs.status;
      }

      // Update event
      const updatedEvent = await this.calendarService.updateEvent(
        calendarId,
        inputs.eventId,
        updateData,
        inputs.accessToken,
      );

      // Prepare output
      const output: GoogleCalendarUpdateEventOutput = {
        eventId: updatedEvent.id || '',
        htmlLink: updatedEvent.htmlLink || '',
        meetingLink: updatedEvent.hangoutLink,
        event: {
          id: updatedEvent.id || '',
          summary: updatedEvent.summary || '',
          description: updatedEvent.description,
          location: updatedEvent.location,
          startDateTime: updatedEvent.start?.dateTime || updatedEvent.start?.date || '',
          endDateTime: updatedEvent.end?.dateTime || updatedEvent.end?.date || '',
          status: updatedEvent.status || '',
          attendees: updatedEvent.attendees?.map(attendee => ({
            email: attendee.email || '',
            responseStatus: attendee.responseStatus || 'needsAction',
          })),
          creator: {
            email: updatedEvent.creator?.email || '',
            displayName: updatedEvent.creator?.displayName,
          },
          organizer: {
            email: updatedEvent.organizer?.email || '',
            displayName: updatedEvent.organizer?.displayName,
          },
        },
        metadata: {
          calendarId,
          updated: updatedEvent.updated || new Date().toISOString(),
          sequence: updatedEvent.sequence || 0,
        },
      };

      this.logger.log(`Calendar event updated successfully: ${output.eventId}`);

      return {
        success: true,
        output,
        metadata: {
          nodeType: 'google_calendar_update_event',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(`Error updating calendar event: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        errorDetails: error,
        metadata: {
          nodeType: 'google_calendar_update_event',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    }
  }
}
