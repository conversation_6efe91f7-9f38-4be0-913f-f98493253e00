import { Test, TestingModule } from '@nestjs/testing';
import { NodeExecutorFactory } from '../registry/node-executor-factory';
import { NodeExecutorRegistry } from '../registry/node-executor-registry';

describe('NodeExecutorFactory', () => {
  let factory: NodeExecutorFactory;
  let registry: jest.Mocked<NodeExecutorRegistry>;

  beforeEach(async () => {
    const mockRegistry = {
      getExecutor: jest.fn(),
      registerExecutor: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NodeExecutorFactory,
        { provide: NodeExecutorRegistry, useValue: mockRegistry },
      ],
    }).compile();

    factory = module.get<NodeExecutorFactory>(NodeExecutorFactory);
    registry = module.get(NodeExecutorRegistry);
  });

  it('should be defined', () => {
    expect(factory).toBeDefined();
  });

  // TODO: Add more tests for factory functionality
});
