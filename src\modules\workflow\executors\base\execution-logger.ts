import { Injectable, Logger } from '@nestjs/common';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { LoggingService } from '../../services/logging.service';
import { EventService } from '../../services/event.service';

/**
 * Service để log execution events cho node executors
 * Integrates với existing LoggingService và EventService từ WK-001
 */
@Injectable()
export class ExecutionLogger {
  private readonly logger = new Logger(ExecutionLogger.name);

  constructor(
    private readonly loggingService?: LoggingService,
    private readonly eventService?: EventService,
  ) {}

  /**
   * Log khi node bắt đầu thực thi
   * @param nodeType - Type của node
   * @param inputs - Input data
   * @param context - Execution context
   */
  async logStart(nodeType: string, inputs: any, context: ExecutionContext): Promise<void> {
    try {
      const logData = {
        nodeType,
        inputs: this.sanitizeInputs(inputs),
        timestamp: Date.now(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      };

      // Log to console
      this.logger.log(`Node execution started: ${nodeType}`, {
        executionId: context.executionId,
        nodeType,
      });

      // Log to database via LoggingService
      if (this.loggingService) {
        await this.loggingService.logNodeEvent(
          context.executionId,
          nodeType,
          'node.start',
          logData
        );
      }

      // Emit event via EventService
      if (this.eventService) {
        await this.eventService.emitNodeEvent(
          context.executionId,
          nodeType,
          'node.start',
          logData
        );
      }
    } catch (error) {
      this.logger.error('Failed to log node start:', error);
      // Don't throw error to avoid breaking execution
    }
  }

  /**
   * Log khi node thực thi thành công
   * @param nodeType - Type của node
   * @param output - Output data
   * @param executionTime - Execution time in milliseconds
   * @param context - Execution context
   */
  async logSuccess(
    nodeType: string,
    output: any,
    executionTime: number,
    context: ExecutionContext
  ): Promise<void> {
    try {
      const logData = {
        nodeType,
        output: this.sanitizeOutput(output),
        executionTime,
        timestamp: Date.now(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      };

      // Log to console
      this.logger.log(`Node execution completed: ${nodeType} (${executionTime}ms)`, {
        executionId: context.executionId,
        nodeType,
        executionTime,
      });

      // Log to database via LoggingService
      if (this.loggingService) {
        await this.loggingService.logNodeEvent(
          context.executionId,
          nodeType,
          'node.success',
          logData
        );
      }

      // Emit event via EventService
      if (this.eventService) {
        await this.eventService.emitNodeEvent(
          context.executionId,
          nodeType,
          'node.success',
          logData
        );
      }
    } catch (error) {
      this.logger.error('Failed to log node success:', error);
      // Don't throw error to avoid breaking execution
    }
  }

  /**
   * Log khi node thực thi thất bại
   * @param nodeType - Type của node
   * @param error - Error object
   * @param executionTime - Execution time in milliseconds
   * @param context - Execution context
   */
  async logError(
    nodeType: string,
    error: Error,
    executionTime: number,
    context: ExecutionContext
  ): Promise<void> {
    try {
      const logData = {
        nodeType,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        executionTime,
        timestamp: Date.now(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      };

      // Log to console
      this.logger.error(`Node execution failed: ${nodeType} (${executionTime}ms)`, {
        executionId: context.executionId,
        nodeType,
        error: error.message,
        executionTime,
      });

      // Log to database via LoggingService
      if (this.loggingService) {
        await this.loggingService.logNodeEvent(
          context.executionId,
          nodeType,
          'node.error',
          logData
        );
      }

      // Emit event via EventService
      if (this.eventService) {
        await this.eventService.emitNodeEvent(
          context.executionId,
          nodeType,
          'node.error',
          logData
        );
      }
    } catch (logError) {
      this.logger.error('Failed to log node error:', logError);
      // Don't throw error to avoid breaking execution
    }
  }

  /**
   * Log custom event during node execution
   * @param nodeType - Type của node
   * @param eventType - Custom event type
   * @param data - Event data
   * @param context - Execution context
   */
  async logCustomEvent(
    nodeType: string,
    eventType: string,
    data: any,
    context: ExecutionContext
  ): Promise<void> {
    try {
      const logData = {
        nodeType,
        eventType,
        data: this.sanitizeData(data),
        timestamp: Date.now(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      };

      // Log to console
      this.logger.debug(`Node custom event: ${nodeType} - ${eventType}`, {
        executionId: context.executionId,
        nodeType,
        eventType,
      });

      // Log to database via LoggingService
      if (this.loggingService) {
        await this.loggingService.logNodeEvent(
          context.executionId,
          nodeType,
          eventType,
          logData
        );
      }

      // Emit event via EventService
      if (this.eventService) {
        await this.eventService.emitNodeEvent(
          context.executionId,
          nodeType,
          eventType,
          logData
        );
      }
    } catch (error) {
      this.logger.error('Failed to log custom event:', error);
      // Don't throw error to avoid breaking execution
    }
  }

  /**
   * Sanitize input data để remove sensitive information
   * @param inputs - Raw input data
   * @returns Sanitized input data
   */
  private sanitizeInputs(inputs: any): any {
    return this.sanitizeData(inputs);
  }

  /**
   * Sanitize output data để remove sensitive information
   * @param output - Raw output data
   * @returns Sanitized output data
   */
  private sanitizeOutput(output: any): any {
    return this.sanitizeData(output);
  }

  /**
   * Generic data sanitization
   * @param data - Raw data
   * @returns Sanitized data
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    try {
      // Clone data to avoid modifying original
      const sanitized = JSON.parse(JSON.stringify(data));

      // Remove sensitive fields
      this.removeSensitiveFields(sanitized);

      return sanitized;
    } catch (error) {
      this.logger.warn('Failed to sanitize data:', error);
      return '[Data sanitization failed]';
    }
  }

  /**
   * Remove sensitive fields from object
   * @param obj - Object to sanitize
   */
  private removeSensitiveFields(obj: any): void {
    if (!obj || typeof obj !== 'object') {
      return;
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'credential',
      'auth',
      'authorization',
      'apiKey',
      'accessToken',
      'refreshToken',
      'privateKey',
    ];

    for (const field of sensitiveFields) {
      if (field in obj) {
        obj[field] = '[REDACTED]';
      }
    }

    // Recursively sanitize nested objects
    for (const value of Object.values(obj)) {
      if (value && typeof value === 'object') {
        this.removeSensitiveFields(value);
      }
    }
  }

  /**
   * Create execution summary log
   * @param nodeType - Type của node
   * @param success - Whether execution was successful
   * @param executionTime - Total execution time
   * @param context - Execution context
   */
  async logExecutionSummary(
    nodeType: string,
    success: boolean,
    executionTime: number,
    context: ExecutionContext
  ): Promise<void> {
    const summaryData = {
      nodeType,
      success,
      executionTime,
      timestamp: Date.now(),
      executionId: context.executionId,
      workflowId: context.workflowId,
      userId: context.userId,
    };

    this.logger.log(`Node execution summary: ${nodeType}`, summaryData);

    // This could be used for metrics and monitoring
    if (this.eventService) {
      await this.eventService.emitNodeEvent(
        context.executionId,
        nodeType,
        'node.summary',
        summaryData
      );
    }
  }
}
