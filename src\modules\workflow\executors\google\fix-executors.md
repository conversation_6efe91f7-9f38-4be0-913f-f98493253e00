# Hướng dẫn Fix Google Executors

## Vấn đề hiện tại:
1. **Thiếu implementation**: <PERSON><PERSON><PERSON> hết executors thiếu `name`, `getInputSchema()`, `getOutputSchema()`
2. **Method signature sai**: `validateInputs()` tr<PERSON> về `void` thay vì `boolean`
3. **Type errors**: Array operations với type `never`, type mismatch
4. **Service method không tồn tại**: Gọi `getAdGroupById()` thay vì `getAdGroups()`

## Cấu trúc chuẩn (dựa trên google-analytics-page-views.executor.ts):

```typescript
@Injectable()
export class GoogleExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.service.action';           // ✅ REQUIRED
  readonly category = NodeCategory.GOOGLE_ADS;       // ✅ REQUIRED  
  readonly name = 'Action Name';                     // ✅ REQUIRED
  readonly description = 'Description';              // ✅ REQUIRED

  constructor(
    integrationService: IntegrationService,
    private readonly service: ServiceClass,          // ✅ Inject service
  ) {
    super(integrationService);
  }

  getInputSchema(): Record<string, any> {            // ✅ REQUIRED
    return { /* schema */ };
  }

  getOutputSchema(): Record<string, any> {           // ✅ REQUIRED  
    return { /* schema */ };
  }

  protected validateInputs(inputs: any): boolean {   // ✅ Return boolean
    // validation logic
    return true;
  }

  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // execution logic
    });
  }
}
```

## Các bước fix:

### 1. Fix missing implementations
- Thêm `readonly name = 'Tên Executor';`
- Implement `getInputSchema()` và `getOutputSchema()`

### 2. Fix method signatures  
- Đổi `validateInputs(): void` thành `validateInputs(): boolean`
- Return `true/false` thay vì throw error

### 3. Fix service method calls
- Đổi `getAdGroupById()` thành `getAdGroups()` với filter
- Kiểm tra tất cả service methods có tồn tại

### 4. Fix type issues
- Định nghĩa proper interfaces cho Input/Output
- Fix array type declarations
- Handle undefined/null values properly

### 5. Standardize error handling
```typescript
try {
  // logic
} catch (error) {
  this.logger.error(`Error: ${error.message}`);
  throw new Error(`Failed: ${error.message}`);
}
```

## Ưu tiên fix:
1. **High Priority**: Ads executors (nhiều lỗi nhất)
2. **Medium Priority**: Drive executors  
3. **Low Priority**: Calendar executors (ít lỗi hơn)

## Template sử dụng:
Sử dụng `base-google-executor.template.ts` làm template chuẩn cho tất cả executors mới.
