import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Utility service for migrating services from BE App to BE Worker
 * Provides tools for analyzing, planning, and executing service migrations
 */
@Injectable()
export class ServiceMigrationUtility {
  private readonly logger = new Logger(ServiceMigrationUtility.name);

  /**
   * Analyze existing services in BE App for migration planning
   * @param beAppPath - Path to BE App source directory
   * @returns Analysis report
   */
  async analyzeExistingServices(beAppPath: string): Promise<ServiceAnalysisReport> {
    this.logger.log('Analyzing existing services for migration...');

    try {
      const analysis: ServiceAnalysisReport = {
        totalServices: 0,
        servicesByCategory: {},
        migrationCandidates: [],
        dependencies: [],
        complexityScore: 0,
        recommendations: [],
      };

      // Analyze Google services
      const googleServices = await this.analyzeGoogleServices(beAppPath);
      analysis.servicesByCategory['google'] = googleServices;
      analysis.totalServices += googleServices.services.length;

      // Analyze Facebook services (if exists)
      const facebookServices = await this.analyzeFacebookServices(beAppPath);
      if (facebookServices.services.length > 0) {
        analysis.servicesByCategory['facebook'] = facebookServices;
        analysis.totalServices += facebookServices.services.length;
      }

      // Analyze Zalo services (if exists)
      const zaloServices = await this.analyzeZaloServices(beAppPath);
      if (zaloServices.services.length > 0) {
        analysis.servicesByCategory['zalo'] = zaloServices;
        analysis.totalServices += zaloServices.services.length;
      }

      // Generate migration candidates
      analysis.migrationCandidates = this.generateMigrationCandidates(analysis.servicesByCategory);

      // Analyze dependencies
      analysis.dependencies = await this.analyzeDependencies(beAppPath);

      // Calculate complexity score
      analysis.complexityScore = this.calculateComplexityScore(analysis);

      // Generate recommendations
      analysis.recommendations = this.generateRecommendations(analysis);

      this.logger.log(`Service analysis completed: ${analysis.totalServices} services analyzed`);
      return analysis;

    } catch (error) {
      this.logger.error('Service analysis failed:', error);
      throw new Error(`Service analysis failed: ${error.message}`);
    }
  }

  /**
   * Generate migration plan for specific service category
   * @param category - Service category (google, facebook, zalo)
   * @param services - Services to migrate
   * @returns Migration plan
   */
  generateMigrationPlan(category: string, services: ServiceInfo[]): MigrationPlan {
    this.logger.log(`Generating migration plan for ${category} services...`);

    const plan: MigrationPlan = {
      category,
      totalServices: services.length,
      phases: [],
      estimatedHours: 0,
      dependencies: [],
      risks: [],
      successCriteria: [],
    };

    // Phase 1: Infrastructure Setup
    plan.phases.push({
      name: 'Infrastructure Setup',
      description: `Setup base infrastructure for ${category} node executors`,
      tasks: [
        `Create base ${category} executor class`,
        `Setup ${category} authentication`,
        `Configure ${category} API clients`,
        `Setup error handling and logging`,
      ],
      estimatedHours: 8,
      dependencies: [],
    });

    // Phase 2: Service Migration
    const migrationPhases = this.createServiceMigrationPhases(category, services);
    plan.phases.push(...migrationPhases);

    // Phase 3: Testing & Integration
    plan.phases.push({
      name: 'Testing & Integration',
      description: `Test and integrate ${category} node executors`,
      tasks: [
        'Create unit tests',
        'Create integration tests',
        'Performance testing',
        'Documentation updates',
      ],
      estimatedHours: 12,
      dependencies: ['Service Migration'],
    });

    // Calculate total estimated hours
    plan.estimatedHours = plan.phases.reduce((total, phase) => total + phase.estimatedHours, 0);

    // Add dependencies
    plan.dependencies = this.identifyMigrationDependencies(category, services);

    // Add risks
    plan.risks = this.identifyMigrationRisks(category, services);

    // Add success criteria
    plan.successCriteria = this.defineSuccessCriteria(category, services);

    return plan;
  }

  /**
   * Create executor template for a service
   * @param serviceName - Name of the service
   * @param category - Service category
   * @param serviceInfo - Service information
   * @returns Executor template code
   */
  generateExecutorTemplate(
    serviceName: string,
    category: string,
    serviceInfo: ServiceInfo
  ): string {
    const className = this.toPascalCase(`${serviceName}-executor`);
    const nodeType = `${category}.${serviceName.toLowerCase()}`;

    return `import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * ${serviceName} Node Executor
 * Handles ${serviceName} operations in workflows
 */
@Injectable()
export class ${className} extends BaseNodeExecutor {
  readonly type = '${nodeType}';
  readonly category = NodeCategory.${category.toUpperCase()};
  readonly name = '${serviceName}';
  readonly description = 'Execute ${serviceName} operations';

  /**
   * Execute ${serviceName} operation
   * @param inputs - ${serviceName} configuration
   * @param context - Execution context
   * @returns ${serviceName} operation result
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    this.logger.debug(\`Executing ${serviceName} operation\`);

    try {
      // TODO: Implement ${serviceName} logic here
      // This is a template - replace with actual implementation
      
      const result = {
        success: true,
        data: {},
        timestamp: new Date().toISOString(),
      };

      this.logger.debug(\`${serviceName} operation completed successfully\`);
      return result;

    } catch (error) {
      this.logger.error(\`${serviceName} operation failed: \${error.message}\`);
      throw new Error(\`${serviceName} operation failed: \${error.message}\`);
    }
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        // TODO: Define input schema based on ${serviceName} requirements
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          description: 'Operation success status',
        },
        data: {
          type: 'object',
          description: '${serviceName} operation result data',
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          description: 'Operation timestamp',
        },
      },
    };
  }
}`;
  }

  /**
   * Analyze Google services
   * @private
   */
  private async analyzeGoogleServices(beAppPath: string): Promise<ServiceCategoryInfo> {
    const googlePath = path.join(beAppPath, 'src/shared/services/google');
    const services: ServiceInfo[] = [];

    if (fs.existsSync(googlePath)) {
      const items = fs.readdirSync(googlePath, { withFileTypes: true });
      
      for (const item of items) {
        if (item.isDirectory()) {
          const serviceName = item.name;
          const servicePath = path.join(googlePath, serviceName);
          
          services.push({
            name: serviceName,
            path: servicePath,
            type: 'directory',
            complexity: await this.calculateServiceComplexity(servicePath),
            dependencies: await this.extractServiceDependencies(servicePath),
            migrationPriority: this.calculateMigrationPriority(serviceName),
          });
        } else if (item.name.endsWith('.service.ts')) {
          const serviceName = item.name.replace('.service.ts', '');
          const servicePath = path.join(googlePath, item.name);
          
          services.push({
            name: serviceName,
            path: servicePath,
            type: 'file',
            complexity: await this.calculateServiceComplexity(servicePath),
            dependencies: await this.extractServiceDependencies(servicePath),
            migrationPriority: this.calculateMigrationPriority(serviceName),
          });
        }
      }
    }

    return {
      category: 'google',
      services,
      totalComplexity: services.reduce((sum, s) => sum + s.complexity, 0),
      migrationEstimate: services.length * 4, // 4 hours per service average
    };
  }

  /**
   * Analyze Facebook services
   * @private
   */
  private async analyzeFacebookServices(beAppPath: string): Promise<ServiceCategoryInfo> {
    // Similar implementation for Facebook services
    return {
      category: 'facebook',
      services: [],
      totalComplexity: 0,
      migrationEstimate: 0,
    };
  }

  /**
   * Analyze Zalo services
   * @private
   */
  private async analyzeZaloServices(beAppPath: string): Promise<ServiceCategoryInfo> {
    // Similar implementation for Zalo services
    return {
      category: 'zalo',
      services: [],
      totalComplexity: 0,
      migrationEstimate: 0,
    };
  }

  /**
   * Calculate service complexity
   * @private
   */
  private async calculateServiceComplexity(servicePath: string): Promise<number> {
    try {
      if (fs.existsSync(servicePath)) {
        const stats = fs.statSync(servicePath);
        if (stats.isFile()) {
          const content = fs.readFileSync(servicePath, 'utf8');
          // Simple complexity calculation based on file size and method count
          const methodCount = (content.match(/async\s+\w+\s*\(/g) || []).length;
          const lineCount = content.split('\n').length;
          return Math.min(10, Math.floor((methodCount * 2 + lineCount / 100)));
        } else if (stats.isDirectory()) {
          const files = fs.readdirSync(servicePath);
          const tsFiles = files.filter(f => f.endsWith('.ts'));
          return Math.min(10, tsFiles.length * 2);
        }
      }
      return 1;
    } catch (error) {
      return 1;
    }
  }

  /**
   * Extract service dependencies
   * @private
   */
  private async extractServiceDependencies(servicePath: string): Promise<string[]> {
    try {
      if (fs.existsSync(servicePath) && fs.statSync(servicePath).isFile()) {
        const content = fs.readFileSync(servicePath, 'utf8');
        const imports = content.match(/import\s+.*\s+from\s+['"]([^'"]+)['"]/g) || [];
        return imports.map(imp => imp.match(/from\s+['"]([^'"]+)['"]/)?.[1] || '').filter(Boolean);
      }
      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Calculate migration priority
   * @private
   */
  private calculateMigrationPriority(serviceName: string): 'high' | 'medium' | 'low' {
    const highPriority = ['ads', 'analytics', 'gmail', 'calendar'];
    const mediumPriority = ['drive', 'sheets', 'docs'];
    
    if (highPriority.some(p => serviceName.includes(p))) return 'high';
    if (mediumPriority.some(p => serviceName.includes(p))) return 'medium';
    return 'low';
  }

  /**
   * Generate migration candidates
   * @private
   */
  private generateMigrationCandidates(servicesByCategory: Record<string, ServiceCategoryInfo>): MigrationCandidate[] {
    const candidates: MigrationCandidate[] = [];
    
    Object.values(servicesByCategory).forEach(category => {
      category.services.forEach(service => {
        candidates.push({
          serviceName: service.name,
          category: category.category,
          priority: service.migrationPriority,
          complexity: service.complexity,
          estimatedHours: service.complexity * 2,
          dependencies: service.dependencies,
        });
      });
    });

    return candidates.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Analyze dependencies
   * @private
   */
  private async analyzeDependencies(beAppPath: string): Promise<DependencyInfo[]> {
    // Implementation for dependency analysis
    return [];
  }

  /**
   * Calculate complexity score
   * @private
   */
  private calculateComplexityScore(analysis: ServiceAnalysisReport): number {
    const totalComplexity = Object.values(analysis.servicesByCategory)
      .reduce((sum, category) => sum + category.totalComplexity, 0);
    
    return Math.min(100, totalComplexity);
  }

  /**
   * Generate recommendations
   * @private
   */
  private generateRecommendations(analysis: ServiceAnalysisReport): string[] {
    const recommendations: string[] = [];
    
    if (analysis.totalServices > 20) {
      recommendations.push('Consider phased migration approach due to large number of services');
    }
    
    if (analysis.complexityScore > 70) {
      recommendations.push('High complexity detected - allocate additional testing time');
    }
    
    return recommendations;
  }

  /**
   * Create service migration phases
   * @private
   */
  private createServiceMigrationPhases(category: string, services: ServiceInfo[]): MigrationPhase[] {
    const phases: MigrationPhase[] = [];
    
    // Group services by priority
    const highPriority = services.filter(s => s.migrationPriority === 'high');
    const mediumPriority = services.filter(s => s.migrationPriority === 'medium');
    const lowPriority = services.filter(s => s.migrationPriority === 'low');
    
    if (highPriority.length > 0) {
      phases.push({
        name: `${category} High Priority Services`,
        description: `Migrate high priority ${category} services`,
        tasks: highPriority.map(s => `Migrate ${s.name} service`),
        estimatedHours: highPriority.reduce((sum, s) => sum + s.complexity * 2, 0),
        dependencies: ['Infrastructure Setup'],
      });
    }
    
    if (mediumPriority.length > 0) {
      phases.push({
        name: `${category} Medium Priority Services`,
        description: `Migrate medium priority ${category} services`,
        tasks: mediumPriority.map(s => `Migrate ${s.name} service`),
        estimatedHours: mediumPriority.reduce((sum, s) => sum + s.complexity * 2, 0),
        dependencies: [`${category} High Priority Services`],
      });
    }
    
    if (lowPriority.length > 0) {
      phases.push({
        name: `${category} Low Priority Services`,
        description: `Migrate low priority ${category} services`,
        tasks: lowPriority.map(s => `Migrate ${s.name} service`),
        estimatedHours: lowPriority.reduce((sum, s) => sum + s.complexity * 2, 0),
        dependencies: [`${category} Medium Priority Services`],
      });
    }
    
    return phases;
  }

  /**
   * Identify migration dependencies
   * @private
   */
  private identifyMigrationDependencies(category: string, services: ServiceInfo[]): string[] {
    return [
      'Database entities setup',
      'Authentication configuration',
      'API client libraries',
      'Error handling framework',
    ];
  }

  /**
   * Identify migration risks
   * @private
   */
  private identifyMigrationRisks(category: string, services: ServiceInfo[]): string[] {
    return [
      'Service downtime during migration',
      'Data consistency issues',
      'Authentication token management',
      'API rate limiting changes',
    ];
  }

  /**
   * Define success criteria
   * @private
   */
  private defineSuccessCriteria(category: string, services: ServiceInfo[]): string[] {
    return [
      'All services migrated successfully',
      'No functionality regression',
      'Performance maintained or improved',
      'Test coverage >90%',
    ];
  }

  /**
   * Convert string to PascalCase
   * @private
   */
  private toPascalCase(str: string): string {
    return str
      .split(/[-_\s]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }
}

// Interfaces for migration planning
export interface ServiceAnalysisReport {
  totalServices: number;
  servicesByCategory: Record<string, ServiceCategoryInfo>;
  migrationCandidates: MigrationCandidate[];
  dependencies: DependencyInfo[];
  complexityScore: number;
  recommendations: string[];
}

export interface ServiceCategoryInfo {
  category: string;
  services: ServiceInfo[];
  totalComplexity: number;
  migrationEstimate: number;
}

export interface ServiceInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  complexity: number;
  dependencies: string[];
  migrationPriority: 'high' | 'medium' | 'low';
}

export interface MigrationCandidate {
  serviceName: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  complexity: number;
  estimatedHours: number;
  dependencies: string[];
}

export interface DependencyInfo {
  name: string;
  type: 'internal' | 'external';
  impact: 'high' | 'medium' | 'low';
}

export interface MigrationPlan {
  category: string;
  totalServices: number;
  phases: MigrationPhase[];
  estimatedHours: number;
  dependencies: string[];
  risks: string[];
  successCriteria: string[];
}

export interface MigrationPhase {
  name: string;
  description: string;
  tasks: string[];
  estimatedHours: number;
  dependencies: string[];
}
