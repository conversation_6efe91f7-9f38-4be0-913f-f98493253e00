import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { FacebookPageService } from '../../../../../shared/services/facebook/page/facebook-page.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Facebook Page Get Posts node
 */
export interface FacebookPageGetPostsInput {
  /**
   * ID của Facebook Page
   */
  pageId: string;

  /**
   * <PERSON><PERSON> lượng posts cần lấy (mặc định: 25, tối đa: 100)
   */
  limit?: number;

  /**
   * <PERSON><PERSON><PERSON> fields cần lấy
   */
  fields?: string[];
}

/**
 * Output schema cho Facebook Page Get Posts node
 */
export interface FacebookPageGetPostsOutput {
  /**
   * <PERSON>h sách posts
   */
  posts: Array<{
    id: string;
    message?: string;
    created_time: string;
    likes?: {
      summary: {
        total_count: number;
      };
    };
    comments?: {
      summary: {
        total_count: number;
      };
    };
    shares?: {
      count: number;
    };
  }>;

  /**
   * Thông tin phân trang
   */
  paging?: {
    previous?: string;
    next?: string;
  };

  /**
   * Metadata
   */
  metadata: {
    pageId: string;
    totalPosts: number;
    retrieved: string;
  };
}

/**
 * Executor để lấy danh sách posts từ Facebook Page
 */
@Injectable()
export class FacebookPageGetPostsExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'facebook.page.get_posts';
  readonly category = NodeCategory.FACEBOOK_PAGE;
  readonly name = 'Get Page Posts';
  readonly description = 'Lấy danh sách bài viết từ Facebook Page';

  constructor(
    integrationService: IntegrationService,
    private readonly facebookPageService: FacebookPageService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        pageId: { type: 'string' },
        limit: { 
          type: 'number', 
          minimum: 1, 
          maximum: 100,
          default: 25,
        },
        fields: { 
          type: 'array', 
          items: { type: 'string' },
          default: ['id', 'message', 'created_time', 'likes.summary(true)', 'comments.summary(true)', 'shares'],
        },
      },
      required: ['pageId'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        posts: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              message: { type: 'string' },
              created_time: { type: 'string' },
              likes: {
                type: 'object',
                properties: {
                  summary: {
                    type: 'object',
                    properties: {
                      total_count: { type: 'number' },
                    },
                  },
                },
              },
              comments: {
                type: 'object',
                properties: {
                  summary: {
                    type: 'object',
                    properties: {
                      total_count: { type: 'number' },
                    },
                  },
                },
              },
              shares: {
                type: 'object',
                properties: {
                  count: { type: 'number' },
                },
              },
            },
          },
        },
        paging: {
          type: 'object',
          properties: {
            previous: { type: 'string' },
            next: { type: 'string' },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            pageId: { type: 'string' },
            totalPosts: { type: 'number' },
            retrieved: { type: 'string' },
          },
        },
      },
      required: ['posts', 'metadata'],
    };
  }

  /**
   * Get provider for authentication
   */
  protected getProvider(): ProviderEnum {
    return ProviderEnum.FACEBOOK_PAGE;
  }

  /**
   * Execute the Facebook Page Get Posts node
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.FACEBOOK_PAGE, async () => {
      this.logger.log(`Getting Facebook Page posts for page: ${inputs.pageId}`);

      // Set defaults
      const limit = inputs.limit || 25;

      // Get access token
      const accessToken = await this.getFacebookPageAccessToken(context);

      // Get posts
      const postsData = await this.facebookPageService.getPosts(
        inputs.pageId,
        accessToken,
        limit,
      );

      // Prepare output
      const output: FacebookPageGetPostsOutput = {
        posts: postsData.data || [],
        paging: postsData.paging,
        metadata: {
          pageId: inputs.pageId,
          totalPosts: postsData.data?.length || 0,
          retrieved: new Date().toISOString(),
        },
      };

      this.logger.log(`Retrieved ${output.metadata.totalPosts} posts from Facebook Page: ${inputs.pageId}`);

      return output;
    });
  }


}
