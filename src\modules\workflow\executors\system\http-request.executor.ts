import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * HTTP Request Node Executor
 * Handles HTTP API calls with comprehensive configuration options
 */
@Injectable()
export class HttpRequestExecutor extends BaseNodeExecutor {
  readonly type = 'system.http.request';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'HTTP Request';
  readonly description = 'Make HTTP requests to external APIs with configurable options';

  /**
   * Execute HTTP request with provided configuration
   * @param inputs - HTTP request configuration
   * @param context - Execution context
   * @returns HTTP response data
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      url,
      method = 'GET',
      headers = {},
      body,
      timeout = 30000,
      retries = 0,
      retryDelay = 1000,
      followRedirects = true,
      validateStatus,
      responseType = 'json',
    } = inputs;

    this.logger.debug(`Making HTTP ${method} request to: ${url}`);

    // Build axios configuration
    const config: AxiosRequestConfig = {
      url,
      method: method.toUpperCase(),
      headers: {
        'User-Agent': 'RedAI-Workflow/1.0',
        ...headers,
      },
      timeout,
      maxRedirects: followRedirects ? 5 : 0,
      responseType,
      validateStatus: validateStatus || ((status) => status >= 200 && status < 300),
    };

    // Add request body for POST/PUT/PATCH requests
    if (config.method && ['POST', 'PUT', 'PATCH'].includes(config.method) && body) {
      if (typeof body === 'object') {
        config.data = body;
        if (config.headers && !config.headers['Content-Type']) {
          config.headers['Content-Type'] = 'application/json';
        }
      } else {
        config.data = body;
      }
    }

    // Execute request with retry logic
    let lastError: Error | undefined;
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await this.executeHttpRequest(config);
        
        this.logger.debug(`HTTP request successful: ${response.status} ${response.statusText}`);
        
        return {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
          url: response.config.url,
          method: response.config.method,
          requestHeaders: response.config.headers,
          duration: response.headers['x-response-time'] || null,
        };

      } catch (error) {
        lastError = error;
        
        if (attempt < retries) {
          this.logger.warn(`HTTP request failed (attempt ${attempt + 1}/${retries + 1}): ${error.message}`);
          await this.delay(retryDelay * Math.pow(2, attempt)); // Exponential backoff
        } else {
          this.logger.error(`HTTP request failed after ${retries + 1} attempts: ${error.message}`);
        }
      }
    }

    // If we get here, all retries failed
    throw this.createHttpError(lastError || new Error('Unknown error'), url, method);
  }

  /**
   * Execute HTTP request with axios
   * @private
   */
  private async executeHttpRequest(config: AxiosRequestConfig): Promise<AxiosResponse> {
    const startTime = Date.now();
    
    try {
      const response = await axios(config);
      
      // Add response time header
      const duration = Date.now() - startTime;
      response.headers['x-response-time'] = `${duration}ms`;
      
      return response;
      
    } catch (error) {
      if (axios.isAxiosError(error)) {
        // Handle axios-specific errors
        if (error.response) {
          // Server responded with error status
          const response = error.response;
          return {
            ...response,
            headers: {
              ...response.headers,
              'x-response-time': `${Date.now() - startTime}ms`,
            },
          };
        } else if (error.request) {
          // Request was made but no response received
          throw new Error(`Network error: ${error.message}`);
        } else {
          // Request configuration error
          throw new Error(`Request configuration error: ${error.message}`);
        }
      }
      
      throw error;
    }
  }

  /**
   * Create standardized HTTP error
   * @private
   */
  private createHttpError(error: any, url: string, method: string): Error {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        return new Error(
          `HTTP ${error.response.status} ${error.response.statusText}: ${url} (${method})`
        );
      } else if (error.request) {
        return new Error(`Network timeout or connection error: ${url} (${method})`);
      }
    }
    
    return new Error(`HTTP request failed: ${error.message} - ${url} (${method})`);
  }

  /**
   * Delay execution for specified milliseconds
   * @private
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['url'],
      properties: {
        url: {
          type: 'string',
          format: 'uri',
          description: 'Target URL for the HTTP request',
        },
        method: {
          type: 'string',
          enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
          default: 'GET',
          description: 'HTTP method to use',
        },
        headers: {
          type: 'object',
          additionalProperties: { type: 'string' },
          description: 'HTTP headers to include in the request',
        },
        body: {
          description: 'Request body data (for POST/PUT/PATCH requests)',
        },
        timeout: {
          type: 'integer',
          minimum: 1000,
          maximum: 300000,
          default: 30000,
          description: 'Request timeout in milliseconds',
        },
        retries: {
          type: 'integer',
          minimum: 0,
          maximum: 5,
          default: 0,
          description: 'Number of retry attempts on failure',
        },
        retryDelay: {
          type: 'integer',
          minimum: 100,
          maximum: 10000,
          default: 1000,
          description: 'Delay between retry attempts in milliseconds',
        },
        followRedirects: {
          type: 'boolean',
          default: true,
          description: 'Whether to follow HTTP redirects',
        },
        responseType: {
          type: 'string',
          enum: ['json', 'text', 'blob', 'arraybuffer', 'stream'],
          default: 'json',
          description: 'Expected response data type',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        status: {
          type: 'integer',
          description: 'HTTP response status code',
        },
        statusText: {
          type: 'string',
          description: 'HTTP response status text',
        },
        headers: {
          type: 'object',
          description: 'HTTP response headers',
        },
        data: {
          description: 'HTTP response body data',
        },
        url: {
          type: 'string',
          description: 'Final request URL (after redirects)',
        },
        method: {
          type: 'string',
          description: 'HTTP method used',
        },
        requestHeaders: {
          type: 'object',
          description: 'HTTP request headers sent',
        },
        duration: {
          type: 'string',
          description: 'Request duration (e.g., "150ms")',
        },
      },
    };
  }

  /**
   * Check if node is ready to execute
   */
  async isReady(context: ExecutionContext): Promise<boolean> {
    // HTTP requests are always ready to execute
    return true;
  }
}
