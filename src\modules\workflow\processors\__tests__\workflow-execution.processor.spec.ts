import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bullmq';
import { WorkflowExecutionProcessor } from '../workflow-execution.processor';
import { WorkflowExecutorService } from '../../services/workflow-executor.service';
import { LoggingService } from '../../services/logging.service';
import { WorkflowExecutionJobData, WorkflowNodeExecutionJobData } from '../../../../queue/queue.types';
import { WorkflowExecutionJobName } from '../../../../queue/queue-name.enum';

describe('WorkflowExecutionProcessor', () => {
  let processor: WorkflowExecutionProcessor;
  let workflowExecutorService: jest.Mocked<WorkflowExecutorService>;
  let loggingService: jest.Mocked<LoggingService>;

  const mockWorkflowExecutionJobData: WorkflowExecutionJobData = {
    executionId: 'exec-123',
    workflowId: 'workflow-456',
    userId: 1,
    triggerData: { test: 'data' },
    triggerType: 'manual',
  };

  const mockNodeExecutionJobData: WorkflowNodeExecutionJobData = {
    executionId: 'exec-123',
    nodeId: 'node-456',
    nodeType: 'system.http.request',
    nodeConfig: { url: 'https://api.example.com' },
    inputData: { test: 'input' },
    executionContext: { context: 'data' },
  };

  beforeEach(async () => {
    const mockWorkflowExecutorService = {
      executeWorkflow: jest.fn(),
      executeNode: jest.fn(),
      cleanupExecution: jest.fn(),
    };

    const mockLoggingService = {
      logInfo: jest.fn(),
      logError: jest.fn(),
      logWarn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowExecutionProcessor,
        {
          provide: WorkflowExecutorService,
          useValue: mockWorkflowExecutorService,
        },
        {
          provide: LoggingService,
          useValue: mockLoggingService,
        },
      ],
    }).compile();

    processor = module.get<WorkflowExecutionProcessor>(WorkflowExecutionProcessor);
    workflowExecutorService = module.get(WorkflowExecutorService);
    loggingService = module.get(LoggingService);
  });

  describe('process', () => {
    it('should process EXECUTE_WORKFLOW job successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.EXECUTE_WORKFLOW,
        data: mockWorkflowExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job<WorkflowExecutionJobData>;

      const mockResult = {
        executionId: 'exec-123',
        status: 'completed',
        result: { success: true },
      };

      workflowExecutorService.executeWorkflow.mockResolvedValue(mockResult);

      const result = await processor.process(mockJob);

      expect(workflowExecutorService.executeWorkflow).toHaveBeenCalledWith(mockWorkflowExecutionJobData);
      expect(loggingService.logInfo).toHaveBeenCalledWith(
        'exec-123',
        'Workflow execution started',
        expect.any(Object)
      );
      expect(loggingService.logInfo).toHaveBeenCalledWith(
        'exec-123',
        'Workflow execution completed',
        expect.any(Object)
      );
      expect(mockJob.updateProgress).toHaveBeenCalledWith(100);
      expect(result).toBe(mockResult);
    });

    it('should process EXECUTE_NODE job successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.EXECUTE_NODE,
        data: mockNodeExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job<WorkflowNodeExecutionJobData>;

      const mockResult = {
        nodeId: 'node-456',
        status: 'completed',
        result: { output: 'test' },
      };

      workflowExecutorService.executeNode.mockResolvedValue(mockResult);

      const result = await processor.process(mockJob);

      expect(workflowExecutorService.executeNode).toHaveBeenCalledWith(mockNodeExecutionJobData);
      expect(loggingService.logInfo).toHaveBeenCalledWith(
        'exec-123',
        'Node execution started: system.http.request',
        expect.any(Object)
      );
      expect(loggingService.logInfo).toHaveBeenCalledWith(
        'exec-123',
        'Node execution completed: system.http.request',
        expect.any(Object)
      );
      expect(result).toBe(mockResult);
    });

    it('should process RETRY_WORKFLOW job successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.RETRY_WORKFLOW,
        data: mockWorkflowExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
        attemptsMade: 2,
        opts: { attempts: 3 },
      } as unknown as Job<WorkflowExecutionJobData>;

      const mockResult = {
        executionId: 'exec-123',
        status: 'completed',
        result: { success: true },
      };

      workflowExecutorService.executeWorkflow.mockResolvedValue(mockResult);

      const result = await processor.process(mockJob);

      expect(loggingService.logInfo).toHaveBeenCalledWith(
        'exec-123',
        'Workflow retry started',
        expect.objectContaining({
          attempt: 2,
          maxAttempts: 3,
        })
      );
      expect(result).toBe(mockResult);
    });

    it('should process CLEANUP_EXECUTION job successfully', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.CLEANUP_EXECUTION,
        data: { executionId: 'exec-123' },
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job;

      workflowExecutorService.cleanupExecution.mockResolvedValue(undefined);

      await processor.process(mockJob);

      expect(workflowExecutorService.cleanupExecution).toHaveBeenCalledWith('exec-123');
    });

    it('should handle unknown job names', async () => {
      const mockJob = {
        id: 'job-123',
        name: 'unknown-job',
        data: mockWorkflowExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job<WorkflowExecutionJobData>;

      await expect(processor.process(mockJob)).rejects.toThrow('Unknown job name: unknown-job');
    });

    it('should handle workflow execution errors', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.EXECUTE_WORKFLOW,
        data: mockWorkflowExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job<WorkflowExecutionJobData>;

      const error = new Error('Workflow execution failed');
      workflowExecutorService.executeWorkflow.mockRejectedValue(error);

      await expect(processor.process(mockJob)).rejects.toThrow(error);

      expect(loggingService.logError).toHaveBeenCalledWith(
        'exec-123',
        'Workflow execution failed',
        expect.any(Object)
      );
    });

    it('should handle node execution errors', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.EXECUTE_NODE,
        data: mockNodeExecutionJobData,
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job<WorkflowNodeExecutionJobData>;

      const error = new Error('Node execution failed');
      workflowExecutorService.executeNode.mockRejectedValue(error);

      await expect(processor.process(mockJob)).rejects.toThrow(error);

      expect(loggingService.logError).toHaveBeenCalledWith(
        'exec-123',
        'Node execution failed: system.http.request',
        expect.any(Object)
      );
    });

    it('should handle cleanup errors gracefully', async () => {
      const mockJob = {
        id: 'job-123',
        name: WorkflowExecutionJobName.CLEANUP_EXECUTION,
        data: { executionId: 'exec-123' },
        timestamp: Date.now(),
        updateProgress: jest.fn(),
      } as unknown as Job;

      const error = new Error('Cleanup failed');
      workflowExecutorService.cleanupExecution.mockRejectedValue(error);

      // Should not throw error for cleanup failures
      await processor.process(mockJob);

      expect(workflowExecutorService.cleanupExecution).toHaveBeenCalledWith('exec-123');
    });
  });

  describe('event handlers', () => {
    it('should handle completed event', () => {
      const mockJob = { id: 'job-123' } as Job;
      const result = { success: true };

      // Should not throw
      processor.onCompleted(mockJob, result);
    });

    it('should handle failed event', () => {
      const mockJob = { id: 'job-123' } as Job;
      const error = new Error('Job failed');

      // Should not throw
      processor.onFailed(mockJob, error);
    });

    it('should handle progress event', () => {
      const mockJob = { id: 'job-123' } as Job;
      const progress = 50;

      // Should not throw
      processor.onProgress(mockJob, progress);
    });

    it('should handle stalled event', () => {
      const mockJob = { id: 'job-123' } as Job;

      // Should not throw
      processor.onStalled(mockJob);
    });
  });
});
