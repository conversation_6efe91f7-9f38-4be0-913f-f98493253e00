import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { FacebookPageService, FacebookPageInfo } from '../../../../../shared/services/facebook/page/facebook-page.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Facebook Page Get Info node
 */
export interface FacebookPageGetInfoInput {
  /**
   * ID của Facebook Page
   */
  pageId: string;

  /**
   * Các fields cần lấy
   */
  fields?: string[];
}

/**
 * Output schema cho Facebook Page Get Info node
 */
export interface FacebookPageGetInfoOutput {
  /**
   * Thông tin page
   */
  page: {
    id: string;
    name: string;
    category?: string;
    about?: string;
    description?: string;
    picture?: any;
    cover?: any;
    fan_count?: number;
    followers_count?: number;
  };

  /**
   * Metadata
   */
  metadata: {
    pageId: string;
    retrieved: string;
  };
}

/**
 * Executor để lấy thông tin Facebook Page
 */
@Injectable()
export class FacebookPageGetInfoExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'facebook.page.get_info';
  readonly category = NodeCategory.FACEBOOK_PAGE;
  readonly name = 'Get Page Info';
  readonly description = 'Lấy thông tin chi tiết của Facebook Page';

  constructor(
    integrationService: IntegrationService,
    private readonly facebookPageService: FacebookPageService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        pageId: { type: 'string' },
        fields: { 
          type: 'array', 
          items: { type: 'string' },
          default: ['id', 'name', 'category', 'about', 'description', 'picture', 'cover', 'fan_count', 'followers_count'],
        },
      },
      required: ['pageId'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        page: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            category: { type: 'string' },
            about: { type: 'string' },
            description: { type: 'string' },
            picture: { type: 'object' },
            cover: { type: 'object' },
            fan_count: { type: 'number' },
            followers_count: { type: 'number' },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            pageId: { type: 'string' },
            retrieved: { type: 'string' },
          },
        },
      },
      required: ['page', 'metadata'],
    };
  }

  /**
   * Get provider for authentication
   */
  protected getProvider(): ProviderEnum {
    return ProviderEnum.FACEBOOK_PAGE;
  }

  /**
   * Execute the Facebook Page Get Info node
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.FACEBOOK_PAGE, async () => {
      this.logger.log(`Getting Facebook Page info for page: ${inputs.pageId}`);

      // Get access token
      const accessToken = await this.getFacebookPageAccessToken(context);

      // Get page info
      const pageInfo: FacebookPageInfo = await this.facebookPageService.getPageInfo(
        inputs.pageId,
        accessToken,
      );

      // Prepare output
      const output: FacebookPageGetInfoOutput = {
        page: {
          id: pageInfo.id,
          name: pageInfo.name,
          category: pageInfo.category,
          about: pageInfo.about,
          description: pageInfo.description,
          picture: pageInfo.picture,
          cover: pageInfo.cover,
          fan_count: pageInfo.fan_count,
          followers_count: pageInfo.followers_count,
        },
        metadata: {
          pageId: inputs.pageId,
          retrieved: new Date().toISOString(),
        },
      };

      this.logger.log(`Retrieved Facebook Page info: ${pageInfo.name} (${pageInfo.id})`);

      return output;
    });
  }


}
