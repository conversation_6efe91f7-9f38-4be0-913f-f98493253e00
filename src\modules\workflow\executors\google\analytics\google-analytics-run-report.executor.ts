import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleAnalyticsService } from '../../../../../shared/services/google/analytics/google-analytics.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Analytics Run Report node
 */
export interface GoogleAnalyticsRunReportInput {
  /**
   * Property ID (GA4 property)
   */
  propertyId: string;

  /**
   * Dimensions (e.g., ['country', 'city'])
   */
  dimensions?: string[];

  /**
   * Metrics (e.g., ['activeUsers', 'sessions'])
   */
  metrics: string[];

  /**
   * Date range
   */
  dateRanges: Array<{
    startDate: string; // YYYY-MM-DD
    endDate: string;   // YYYY-MM-DD
    name?: string;
  }>;

  /**
   * Dimension filter (optional)
   */
  dimensionFilter?: {
    fieldName: string;
    stringFilter?: {
      matchType: 'EXACT' | 'BEGINS_WITH' | 'ENDS_WITH' | 'CONTAINS' | 'FULL_REGEXP' | 'PARTIAL_REGEXP';
      value: string;
      caseSensitive?: boolean;
    };
    inListFilter?: {
      values: string[];
      caseSensitive?: boolean;
    };
    numericFilter?: {
      operation: 'EQUAL' | 'LESS_THAN' | 'LESS_THAN_OR_EQUAL' | 'GREATER_THAN' | 'GREATER_THAN_OR_EQUAL';
      value: {
        int64Value?: string;
        doubleValue?: number;
      };
    };
  };

  /**
   * Metric filter (optional)
   */
  metricFilter?: {
    fieldName: string;
    operation: 'EQUAL' | 'LESS_THAN' | 'LESS_THAN_OR_EQUAL' | 'GREATER_THAN' | 'GREATER_THAN_OR_EQUAL';
    value: {
      int64Value?: string;
      doubleValue?: number;
    };
  };

  /**
   * Order by (optional)
   */
  orderBys?: Array<{
    fieldName: string;
    desc?: boolean;
  }>;

  /**
   * Limit (optional, default: 100)
   */
  limit?: number;

  /**
   * Offset (optional, default: 0)
   */
  offset?: number;
}

/**
 * Output schema cho Google Analytics Run Report node
 */
export interface GoogleAnalyticsRunReportOutput {
  /**
   * Report data
   */
  rows: Array<{
    dimensionValues: Array<{
      value: string;
    }>;
    metricValues: Array<{
      value: string;
    }>;
  }>;

  /**
   * Dimension headers
   */
  dimensionHeaders: Array<{
    name: string;
  }>;

  /**
   * Metric headers
   */
  metricHeaders: Array<{
    name: string;
    type: string;
  }>;

  /**
   * Row count
   */
  rowCount: number;

  /**
   * Metadata
   */
  metadata: {
    propertyId: string;
    dateRanges: Array<{
      startDate: string;
      endDate: string;
    }>;
    samplingLevel?: string;
    dataLossFromOtherRow?: boolean;
  };
}

/**
 * Executor để chạy report từ Google Analytics
 */
@Injectable()
export class GoogleAnalyticsRunReportExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.analytics.run_report';
  readonly category = NodeCategory.GOOGLE_ANALYTICS;
  readonly name = 'Run Analytics Report';
  readonly description = 'Chạy report từ Google Analytics GA4';

  constructor(
    integrationService: IntegrationService,
    private readonly analyticsService: GoogleAnalyticsService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        propertyId: { type: 'string' },
        dimensions: { type: 'array', items: { type: 'string' } },
        metrics: { type: 'array', items: { type: 'string' }, minItems: 1 },
        dateRanges: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              startDate: { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
              endDate: { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
              name: { type: 'string' },
            },
            required: ['startDate', 'endDate'],
          },
          minItems: 1,
        },
        dimensionFilter: { type: 'object' },
        metricFilter: { type: 'object' },
        orderBys: { type: 'array', items: { type: 'object' } },
        limit: { type: 'number', minimum: 1, maximum: 100000 },
        offset: { type: 'number', minimum: 0 },
      },
      required: ['propertyId', 'metrics', 'dateRanges'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        rows: { type: 'array', items: { type: 'object' } },
        dimensionHeaders: { type: 'array', items: { type: 'object' } },
        metricHeaders: { type: 'array', items: { type: 'object' } },
        rowCount: { type: 'number' },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node run report
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Running Google Analytics report for property: ${inputs.propertyId}`);

      // Validate required inputs
      if (!inputs.propertyId) {
        throw new Error('Property ID is required');
      }

      if (!inputs.metrics || inputs.metrics.length === 0) {
        throw new Error('At least one metric is required');
      }

      if (!inputs.dateRanges || inputs.dateRanges.length === 0) {
        throw new Error('At least one date range is required');
      }

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Prepare request
      const request = {
        property: `properties/${inputs.propertyId}`,
        dimensions: inputs.dimensions?.map(name => ({ name })) || [],
        metrics: inputs.metrics.map(name => ({ name })),
        dateRanges: inputs.dateRanges,
        dimensionFilter: inputs.dimensionFilter,
        metricFilter: inputs.metricFilter,
        orderBys: inputs.orderBys,
        limit: inputs.limit || 100,
        offset: inputs.offset || 0,
      };

      // Run report
      const reportResult = await this.analyticsService.runReport(request, accessToken);

      // Prepare output
      const output: GoogleAnalyticsRunReportOutput = {
        rows: reportResult.rows || [],
        dimensionHeaders: reportResult.dimensionHeaders || [],
        metricHeaders: reportResult.metricHeaders || [],
        rowCount: reportResult.rowCount || 0,
        metadata: {
          propertyId: inputs.propertyId,
          dateRanges: inputs.dateRanges,
          samplingLevel: (reportResult.metadata as any)?.samplingLevel,
          dataLossFromOtherRow: (reportResult.metadata as any)?.dataLossFromOtherRow,
        },
      };

      this.logger.log(`Analytics report completed with ${output.rowCount} rows`);

      return output;
    });
  }
}
