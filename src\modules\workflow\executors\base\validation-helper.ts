import { Injectable, Logger } from '@nestjs/common';
import Ajv, { ValidateFunction } from 'ajv';

/**
 * Helper service cho JSON Schema validation
 * Validates node inputs against their schemas
 */
@Injectable()
export class ValidationHelper {
  private readonly logger = new Logger(ValidationHelper.name);
  private readonly ajv: Ajv;
  private readonly schemaCache = new Map<string, ValidateFunction>();

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: false,
      useDefaults: true,
      coerceTypes: true,
    });
    
    // Add format validators (email, date, uri, etc.)
    // addFormats(this.ajv); // Version conflict - using manual formats instead
    
    // Add custom formats if needed
    this.addCustomFormats();
  }

  /**
   * Validate data against JSON schema
   * @param data - Data to validate
   * @param schema - JSON Schema object
   * @returns true if valid, throws error if invalid
   */
  validateAgainstSchema(data: any, schema: Record<string, any>): boolean {
    try {
      // Get or compile validator
      const validator = this.getValidator(schema);
      
      // Validate data
      const isValid = validator(data);
      
      if (!isValid) {
        const errors = this.formatValidationErrors(validator.errors || []);
        throw new Error(`Validation failed: ${errors.join(', ')}`);
      }
      
      return true;
    } catch (error) {
      this.logger.error('Schema validation error:', error);
      throw error;
    }
  }

  /**
   * Validate data and return detailed results
   * @param data - Data to validate
   * @param schema - JSON Schema object
   * @returns Validation result object
   */
  validateWithDetails(data: any, schema: Record<string, any>): {
    valid: boolean;
    errors: string[];
    data: any;
  } {
    try {
      const validator = this.getValidator(schema);
      const isValid = validator(data);
      
      return {
        valid: Boolean(isValid),
        errors: isValid ? [] : this.formatValidationErrors(validator.errors || []),
        data: data, // Data might be modified by ajv (defaults, coercion)
      };
    } catch (error) {
      this.logger.error('Schema validation error:', error);
      return {
        valid: false,
        errors: [`Schema validation error: ${error.message}`],
        data: data,
      };
    }
  }

  /**
   * Get or compile validator for schema
   * @param schema - JSON Schema object
   * @returns Compiled validator function
   */
  private getValidator(schema: Record<string, any>): ValidateFunction {
    const schemaKey = JSON.stringify(schema);
    
    if (this.schemaCache.has(schemaKey)) {
      return this.schemaCache.get(schemaKey)!;
    }
    
    try {
      const validator = this.ajv.compile(schema);
      this.schemaCache.set(schemaKey, validator);
      return validator;
    } catch (error) {
      throw new Error(`Failed to compile schema: ${error.message}`);
    }
  }

  /**
   * Format AJV validation errors into readable messages
   * @param errors - AJV error objects
   * @returns Array of formatted error messages
   */
  private formatValidationErrors(errors: any[]): string[] {
    return errors.map(error => {
      const path = error.instancePath || 'root';
      const message = error.message || 'validation failed';
      
      switch (error.keyword) {
        case 'required':
          return `Missing required field: ${error.params.missingProperty}`;
        
        case 'type':
          return `Field '${path}' should be ${error.params.type}`;
        
        case 'format':
          return `Field '${path}' should match format '${error.params.format}'`;
        
        case 'minimum':
          return `Field '${path}' should be >= ${error.params.limit}`;
        
        case 'maximum':
          return `Field '${path}' should be <= ${error.params.limit}`;
        
        case 'minLength':
          return `Field '${path}' should have at least ${error.params.limit} characters`;
        
        case 'maxLength':
          return `Field '${path}' should have at most ${error.params.limit} characters`;
        
        case 'pattern':
          return `Field '${path}' should match pattern`;
        
        case 'enum':
          return `Field '${path}' should be one of: ${error.params.allowedValues?.join(', ')}`;
        
        default:
          return `Field '${path}': ${message}`;
      }
    });
  }

  /**
   * Add custom format validators
   */
  private addCustomFormats(): void {
    // Add email format validator
    this.ajv.addFormat('email', {
      type: 'string',
      validate: (data: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data),
    });

    // Add URI format validator
    this.ajv.addFormat('uri', {
      type: 'string',
      validate: (data: string) => {
        try {
          new URL(data);
          return true;
        } catch {
          return false;
        }
      },
    });

    // Add node ID format validator
    this.ajv.addFormat('nodeId', {
      type: 'string',
      validate: (data: string) => /^[a-zA-Z0-9_-]+$/.test(data),
    });

    // Add workflow ID format validator (UUID)
    this.ajv.addFormat('workflowId', {
      type: 'string',
      validate: (data: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(data),
    });

    // Add expression format validator
    this.ajv.addFormat('expression', {
      type: 'string',
      validate: (data: string) => {
        // Allow expressions like {{node.output}} or regular strings
        return true; // For now, accept any string
      },
    });

    // Add JSON string format validator
    this.ajv.addFormat('json', {
      type: 'string',
      validate: (data: string) => {
        try {
          JSON.parse(data);
          return true;
        } catch {
          return false;
        }
      },
    });
  }

  /**
   * Create a basic schema for common input types
   * @param type - Input type ('string', 'number', 'boolean', 'object', 'array')
   * @param required - Whether the field is required
   * @param additionalProps - Additional schema properties
   * @returns JSON Schema object
   */
  createBasicSchema(
    type: string,
    required: boolean = false,
    additionalProps: Record<string, any> = {}
  ): Record<string, any> {
    const schema: Record<string, any> = {
      type,
      ...additionalProps,
    };

    if (required) {
      schema.required = true;
    }

    return schema;
  }

  /**
   * Create schema for object with properties
   * @param properties - Object properties schemas
   * @param requiredFields - Array of required field names
   * @param additionalProperties - Allow additional properties
   * @returns JSON Schema object
   */
  createObjectSchema(
    properties: Record<string, any>,
    requiredFields: string[] = [],
    additionalProperties: boolean = false
  ): Record<string, any> {
    return {
      type: 'object',
      properties,
      required: requiredFields,
      additionalProperties,
    };
  }

  /**
   * Create schema for array with items
   * @param itemSchema - Schema for array items
   * @param minItems - Minimum number of items
   * @param maxItems - Maximum number of items
   * @returns JSON Schema object
   */
  createArraySchema(
    itemSchema: Record<string, any>,
    minItems?: number,
    maxItems?: number
  ): Record<string, any> {
    const schema: Record<string, any> = {
      type: 'array',
      items: itemSchema,
    };

    if (minItems !== undefined) {
      schema.minItems = minItems;
    }

    if (maxItems !== undefined) {
      schema.maxItems = maxItems;
    }

    return schema;
  }

  /**
   * Validate that a value matches one of the allowed types
   * @param value - Value to check
   * @param allowedTypes - Array of allowed types
   * @returns true if valid type
   */
  validateType(value: any, allowedTypes: string[]): boolean {
    const valueType = Array.isArray(value) ? 'array' : typeof value;
    return allowedTypes.includes(valueType);
  }

  /**
   * Clear schema cache (useful for testing)
   */
  clearCache(): void {
    this.schemaCache.clear();
  }
}
