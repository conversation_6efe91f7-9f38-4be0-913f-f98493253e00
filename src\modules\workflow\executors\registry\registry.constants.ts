/**
 * Constants cho executor registry system
 */

/**
 * Injection token cho node executor registry
 */
export const NODE_EXECUTOR_REGISTRY_TOKEN = 'NODE_EXECUTOR_REGISTRY';

/**
 * Injection token cho node executor factory
 */
export const NODE_EXECUTOR_FACTORY_TOKEN = 'NODE_EXECUTOR_FACTORY';

/**
 * Injection token cho executor metadata
 */
export const EXECUTOR_METADATA_TOKEN = 'EXECUTOR_METADATA';

/**
 * Prefix cho tất cả node types
 */
export const NODE_TYPE_PREFIXES = {
  SYSTEM: 'system',
  GOOGLE_SHEET: 'google.sheet',
  GOOGLE_DOCS: 'google.docs',
  GOOGLE_GMAIL: 'google.gmail',
  GOOGLE_ADS: 'google.ads',
  GOOGLE_DRIVE: 'google.drive',
  GOOGLE_CALENDAR: 'google.calendar',
  FACEBOOK_PAGE: 'facebook.page',
  FACEBOOK_ADS: 'facebook.ads',
  FACEBOOK_MESSENGER: 'facebook.messenger',
  INSTAGRAM: 'instagram',
  ZALO_OA: 'zalo.oa',
  ZALO_ZNS: 'zalo.zns',
  ZALO: 'zalo',
} as const;

/**
 * System node types (9 types)
 */
export const SYSTEM_NODE_TYPES = [
  'system.start',
  'system.end',
  'system.condition',
  'system.loop',
  'system.delay',
  'system.webhook',
  'system.manual',
  'system.schedule',
  'system.http',
] as const;

/**
 * Google service node types (66 types total)
 */
export const GOOGLE_NODE_TYPES = {
  SHEETS: [
    'google.sheet.create',
    'google.sheet.getRows',
    'google.sheet.appendRow',
    'google.sheet.updateRow',
    'google.sheet.deleteRow',
    'google.sheet.clearRange',
    'google.sheet.formatCells',
    'google.sheet.addSheet',
    'google.sheet.deleteSheet',
    'google.sheet.copySheet',
    // ... more sheet operations
  ],
  DOCS: [
    'google.docs.create',
    'google.docs.get',
    'google.docs.update',
    'google.docs.insertText',
    'google.docs.replaceText',
    'google.docs.insertImage',
    'google.docs.insertTable',
    'google.docs.exportPdf',
    // ... more docs operations
  ],
  GMAIL: [
    'google.gmail.send',
    'google.gmail.list',
    'google.gmail.get',
    'google.gmail.search',
    'google.gmail.markRead',
    'google.gmail.markUnread',
    'google.gmail.delete',
    'google.gmail.addLabel',
    'google.gmail.removeLabel',
    // ... more gmail operations
  ],
  DRIVE: [
    'google.drive.upload',
    'google.drive.download',
    'google.drive.list',
    'google.drive.get',
    'google.drive.delete',
    'google.drive.copy',
    'google.drive.move',
    'google.drive.share',
    'google.drive.createFolder',
    // ... more drive operations
  ],
  CALENDAR: [
    'google.calendar.createEvent',
    'google.calendar.listEvents',
    'google.calendar.getEvent',
    'google.calendar.updateEvent',
    'google.calendar.deleteEvent',
    'google.calendar.listCalendars',
    // ... more calendar operations
  ],
  ADS: [
    'google.ads.getCampaigns',
    'google.ads.createCampaign',
    'google.ads.updateCampaign',
    'google.ads.pauseCampaign',
    'google.ads.getAdGroups',
    'google.ads.createAdGroup',
    'google.ads.getKeywords',
    'google.ads.addKeywords',
    'google.ads.getAds',
    'google.ads.createAd',
    // ... more ads operations
  ],
} as const;

/**
 * Facebook service node types (45 types total)
 */
export const FACEBOOK_NODE_TYPES = {
  PAGE: [
    'facebook.page.post',
    'facebook.page.getPost',
    'facebook.page.updatePost',
    'facebook.page.deletePost',
    'facebook.page.getInsights',
    'facebook.page.getFollowers',
    'facebook.page.uploadPhoto',
    'facebook.page.uploadVideo',
    'facebook.page.createEvent',
    'facebook.page.getEvents',
    // ... more page operations
  ],
  ADS: [
    'facebook.ads.getCampaigns',
    'facebook.ads.createCampaign',
    'facebook.ads.updateCampaign',
    'facebook.ads.pauseCampaign',
    'facebook.ads.getAdSets',
    'facebook.ads.createAdSet',
    'facebook.ads.getAds',
    'facebook.ads.createAd',
    'facebook.ads.getInsights',
    'facebook.ads.getAudiences',
    // ... more ads operations
  ],
  MESSENGER: [
    'facebook.messenger.send',
    'facebook.messenger.sendTemplate',
    'facebook.messenger.sendQuickReply',
    'facebook.messenger.sendButton',
    'facebook.messenger.sendCarousel',
    'facebook.messenger.sendFile',
    'facebook.messenger.getProfile',
    'facebook.messenger.setMenu',
    // ... more messenger operations
  ],
  INSTAGRAM: [
    'instagram.post',
    'instagram.getPost',
    'instagram.getMedia',
    'instagram.getInsights',
    'instagram.getFollowers',
    'instagram.uploadPhoto',
    'instagram.uploadVideo',
    'instagram.createStory',
    // ... more instagram operations
  ],
} as const;

/**
 * Zalo service node types (27 types total)
 */
export const ZALO_NODE_TYPES = {
  OA: [
    'zalo.oa.sendText',
    'zalo.oa.sendImage',
    'zalo.oa.sendFile',
    'zalo.oa.sendList',
    'zalo.oa.sendTemplate',
    'zalo.oa.getProfile',
    'zalo.oa.getFollowers',
    'zalo.oa.broadcast',
    'zalo.oa.getConversation',
    'zalo.oa.uploadFile',
    // ... more OA operations
  ],
  ZNS: [
    'zalo.zns.send',
    'zalo.zns.getTemplate',
    'zalo.zns.getQuota',
    'zalo.zns.getDeliveryStatus',
    // ... more ZNS operations
  ],
  GENERAL: [
    'zalo.getUserInfo',
    'zalo.getFriends',
    'zalo.sendMessage',
    'zalo.uploadPhoto',
    'zalo.createPost',
    'zalo.getPost',
    'zalo.likePost',
    'zalo.commentPost',
    'zalo.sharePost',
    // ... more general operations
  ],
} as const;

/**
 * Total count của node types theo category
 */
export const NODE_TYPE_COUNTS = {
  SYSTEM: 9,
  GOOGLE: 66,
  FACEBOOK: 45,
  ZALO: 27,
  TOTAL: 147, // Will be 192 when all are implemented
} as const;
