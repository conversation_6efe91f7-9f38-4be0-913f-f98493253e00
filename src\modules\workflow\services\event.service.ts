import { Injectable, Logger } from '@nestjs/common';
import { WorkflowEventTypes } from '../interfaces/workflow-job.interface';
import { RedisService } from '../../../infra/redis/redis.service';

/**
 * Service để publish events cho SSE và webhooks
 * Hỗ trợ real-time communication với BE App và Frontend
 * Uses Redis pub/sub for real-time event publishing
 */
@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(private readonly redisService: RedisService) {}
  /**
   * Publish event cho workflow execution
   * @param executionId - ID của execution
   * @param eventType - Loại event
   * @param data - Event data
   * @param nodeId - ID của node (optional)
   * @param enableSSE - Flag to enable SSE publishing (default: true)
   */
  async publishWorkflowEvent(
    executionId: string,
    eventType: WorkflowEventTypes,
    data: any,
    nodeId?: string,
    enableSSE: boolean = true
  ): Promise<void> {
    const event = {
      executionId,
      eventType,
      data,
      nodeId,
      timestamp: Date.now(),
      type: 'workflow.event'
    };

    try {
      // Only publish if SSE is enabled
      if (enableSSE) {
        // Publish to general workflow events channel
        await this.publishToRedis('workflow.events', event);

        // Publish to execution-specific channel
        await this.publishToRedis(`workflow.execution.${executionId}`, event);

        this.logger.debug(`Published workflow event: ${eventType} for execution: ${executionId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to publish workflow event: ${eventType}`, error);
      // Don't throw error to avoid breaking workflow execution
    }
  }

  /**
   * Publish event to Redis channel
   * @private
   */
  private async publishToRedis(channel: string, event: any): Promise<void> {
    const redisClient = this.redisService.getRawClient();
    await redisClient.publish(channel, JSON.stringify(event));
  }

  /**
   * Publish execution started event
   * @param executionId - ID của execution
   * @param workflowId - ID của workflow
   * @param triggerData - Trigger data
   */
  async publishExecutionStarted(
    executionId: string,
    workflowId: string,
    triggerData: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.EXECUTION_STARTED,
      {
        workflowId,
        triggerData,
        startedAt: Date.now()
      }
    );
  }

  /**
   * Publish execution completed event
   * @param executionId - ID của execution
   * @param result - Execution result
   */
  async publishExecutionCompleted(
    executionId: string,
    result: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.EXECUTION_COMPLETED,
      {
        result,
        completedAt: Date.now()
      }
    );
  }

  /**
   * Publish execution failed event
   * @param executionId - ID của execution
   * @param error - Error information
   */
  async publishExecutionFailed(
    executionId: string,
    error: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.EXECUTION_FAILED,
      {
        error: error.message || error,
        errorDetails: error.details || error,
        failedAt: Date.now()
      }
    );
  }

  /**
   * Publish execution paused event
   * @param executionId - ID của execution
   * @param reason - Pause reason
   */
  async publishExecutionPaused(
    executionId: string,
    reason: string
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.EXECUTION_PAUSED,
      {
        reason,
        pausedAt: Date.now()
      }
    );
  }

  /**
   * Publish node started event
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param nodeType - Type của node
   * @param inputs - Input data
   */
  async publishNodeStarted(
    executionId: string,
    nodeId: string,
    nodeType: string,
    inputs: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.NODE_STARTED,
      {
        nodeType,
        inputs,
        startedAt: Date.now()
      },
      nodeId
    );
  }

  /**
   * Publish node completed event
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param output - Output data
   * @param executionTime - Execution time in ms
   */
  async publishNodeCompleted(
    executionId: string,
    nodeId: string,
    output: any,
    executionTime: number
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.NODE_COMPLETED,
      {
        output,
        executionTime,
        completedAt: Date.now()
      },
      nodeId
    );
  }

  /**
   * Publish node failed event
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param error - Error information
   */
  async publishNodeFailed(
    executionId: string,
    nodeId: string,
    error: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.NODE_FAILED,
      {
        error: error.message || error,
        errorDetails: error.details || error,
        failedAt: Date.now()
      },
      nodeId
    );
  }

  /**
   * Publish node skipped event
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param reason - Skip reason
   */
  async publishNodeSkipped(
    executionId: string,
    nodeId: string,
    reason: string
  ): Promise<void> {
    await this.publishWorkflowEvent(
      executionId,
      WorkflowEventTypes.NODE_SKIPPED,
      {
        reason,
        skippedAt: Date.now()
      },
      nodeId
    );
  }

  /**
   * Publish test started event
   * @param testId - ID của test
   * @param nodeType - Type của node
   * @param inputs - Input data
   */
  async publishTestStarted(
    testId: string,
    nodeType: string,
    inputs: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      testId,
      WorkflowEventTypes.TEST_STARTED,
      {
        nodeType,
        inputs,
        startedAt: Date.now()
      }
    );
  }

  /**
   * Publish test completed event
   * @param testId - ID của test
   * @param result - Test result
   */
  async publishTestCompleted(
    testId: string,
    result: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      testId,
      WorkflowEventTypes.TEST_COMPLETED,
      {
        result,
        completedAt: Date.now()
      }
    );
  }

  /**
   * Publish test failed event
   * @param testId - ID của test
   * @param error - Error information
   */
  async publishTestFailed(
    testId: string,
    error: any
  ): Promise<void> {
    await this.publishWorkflowEvent(
      testId,
      WorkflowEventTypes.TEST_FAILED,
      {
        error: error.message || error,
        errorDetails: error.details || error,
        failedAt: Date.now()
      }
    );
  }

  /**
   * Subscribe to workflow events (for BE App SSE implementation)
   * @param executionId - ID của execution
   * @param callback - Callback function
   */
  async subscribeToWorkflowEvents(
    executionId: string,
    callback: (event: any) => void
  ): Promise<void> {
    try {
      const redisClient = this.redisService.getRawClient();
      const channel = `workflow.execution.${executionId}`;

      // Subscribe to execution-specific channel
      await redisClient.subscribe(channel);

      // Handle incoming messages
      redisClient.on('message', (receivedChannel: string, message: string) => {
        if (receivedChannel === channel) {
          try {
            const event = JSON.parse(message);
            callback(event);
          } catch (error) {
            this.logger.error(`Failed to parse event message: ${message}`, error);
          }
        }
      });

      this.logger.debug(`Subscribed to workflow events for execution: ${executionId}`);
    } catch (error) {
      this.logger.error(`Failed to subscribe to workflow events: ${executionId}`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe from workflow events
   * @param executionId - ID của execution
   */
  async unsubscribeFromWorkflowEvents(executionId: string): Promise<void> {
    try {
      const redisClient = this.redisService.getRawClient();
      const channel = `workflow.execution.${executionId}`;

      await redisClient.unsubscribe(channel);

      this.logger.debug(`Unsubscribed from workflow events for execution: ${executionId}`);
    } catch (error) {
      this.logger.error(`Failed to unsubscribe from workflow events: ${executionId}`, error);
      throw error;
    }
  }

  /**
   * Emit node event (generic method)
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param eventType - Loại event
   * @param data - Event data
   */
  async emitNodeEvent(
    executionId: string,
    nodeId: string,
    eventType: string,
    data?: any
  ): Promise<void> {
    const event = {
      executionId,
      nodeId,
      eventType,
      data,
      timestamp: Date.now(),
      type: 'node.event'
    };

    try {
      // Publish to execution-specific channel
      await this.publishToRedis(`workflow.execution.${executionId}`, event);

      // Publish to general node events channel
      await this.publishToRedis('workflow.node.events', event);
    } catch (error) {
      this.logger.error(`Failed to emit node event: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Publish event (generic method)
   * @param eventType - Loại event
   * @param data - Event data
   */
  async publishEvent(
    eventType: string,
    data: any
  ): Promise<void> {
    const event = {
      eventType,
      data,
      timestamp: Date.now(),
      type: 'workflow.event'
    };

    try {
      await this.publishToRedis(`workflow.events.${eventType}`, event);
    } catch (error) {
      this.logger.error(`Failed to publish event: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Publish test events with conditional SSE
   * @param testId - ID của test
   * @param eventType - Loại event
   * @param data - Event data
   * @param enableSSE - Flag to enable SSE publishing
   */
  async publishTestEvent(
    testId: string,
    eventType: WorkflowEventTypes,
    data: any,
    enableSSE: boolean = true
  ): Promise<void> {
    const event = {
      testId,
      eventType,
      data,
      timestamp: Date.now(),
      type: 'test.event'
    };

    try {
      if (enableSSE) {
        // Publish to general test events channel
        await this.publishToRedis('workflow.test.events', event);

        // Publish to test-specific channel
        await this.publishToRedis(`workflow.test.${testId}`, event);

        this.logger.debug(`Published test event: ${eventType} for test: ${testId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to publish test event: ${eventType}`, error);
      // Don't throw error to avoid breaking test execution
    }
  }
}
