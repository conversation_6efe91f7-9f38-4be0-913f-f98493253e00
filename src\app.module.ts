import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { env } from './config';
import { QueueModule } from './queue';
import { InfraModule } from './infra';
import { SharedModule } from './shared/shared.module';
import Redis from 'ioredis';
import { TypeOrmModule } from '@nestjs/typeorm';
import { databaseConfig } from './config';
import { DatabaseModule } from './modules/database';
import { EmailSystemModule } from './modules/email-system/email-system.module';
import { EmailMarketingModule } from './modules/marketing/email/email-marketing.module';
import { SmsMarketingModule } from './modules/marketing/sms/sms-marketing.module';
import { SmsSystemModule } from './modules/sms_system/sms-system.module';
import { ZaloZnsModule } from './modules/marketing/zalo/zalo-zns.module';
import { AgentModule } from './modules/agent';
import { IntegrationModule } from './modules/integration/integration.module';
import { DataProcessModule } from './modules/data-process';
import { ZaloAudienceSyncModule } from './modules/marketing/zalo-audience-sync/zalo-audience-sync.module';
import { ZaloVideoTrackingModule } from './modules/marketing/zalo-video-tracking/zalo-video-tracking.module';
import { MarketingModule } from './modules/marketing/marketing.module';
import { WebhookModule } from './modules/webhook/webhook.module';

@Module({
  imports: [
    SharedModule,
    QueueModule,
    InfraModule,
    AgentModule,
    DatabaseModule,
    EmailSystemModule,
    EmailMarketingModule,
    SmsMarketingModule,
    SmsSystemModule,
    ZaloZnsModule,
    IntegrationModule,
    DataProcessModule,
    ZaloAudienceSyncModule,
    ZaloVideoTrackingModule,
    MarketingModule,
    WebhookModule,
    TypeOrmModule.forRoot(databaseConfig),
    BullModule.forRoot({
      connection: {
        url: env.external.REDIS_URL,
      },
    }),
  ],
})
export class AppModule {}
