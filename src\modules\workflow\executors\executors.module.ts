import { Module } from '@nestjs/common';
import { GoogleExecutorsModule } from './google/google-executors.module';
import { FacebookExecutorsModule } from './facebook/facebook-executors.module';
// import { ZaloExecutorsModule } from './zalo/zalo-executors.module';
// import { AIExecutorsModule } from './ai/ai-executors.module';
// import { SMSExecutorsModule } from './sms/sms-executors.module';

/**
 * Main module chứa tất cả service executors
 * Tập trung quản lý các executors cho external services
 */
@Module({
  imports: [
    GoogleExecutorsModule,
    FacebookExecutorsModule,
    // ZaloExecutorsModule, // TODO: Fix import path
    // AIExecutorsModule, // TODO: Fix import path
    // SMSExecutorsModule,       // TODO: Implement
  ],
  exports: [
    GoogleExecutorsModule,
    FacebookExecutorsModule,
    // ZaloExecutorsModule,
    // AIExecutorsModule,
    // SMSExecutorsModule,
  ],
})
export class ExecutorsModule {}
