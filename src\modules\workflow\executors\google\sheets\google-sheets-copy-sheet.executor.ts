import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

@Injectable()
export class GoogleSheetsCopySheetExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.copySheet';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Copy Sheet in Google Spreadsheet';
  readonly description = 'Sao chép sheet trong Google Spreadsheet';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      const {
        spreadsheetId,
        sourceSheetId,
        destinationSpreadsheetId
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (sourceSheetId === undefined) {
        throw new Error('Source sheet ID is required');
      }

      const accessToken = await this.getGoogleAccessToken(context);

      const result = await this.googleSheetsService.copySheet(
        accessToken,
        spreadsheetId,
        sourceSheetId,
        destinationSpreadsheetId || spreadsheetId
      );

      return {
        sourceSpreadsheetId: spreadsheetId,
        sourceSheetId,
        destinationSpreadsheetId: destinationSpreadsheetId || spreadsheetId,
        newSheetId: result.sheetId,
        newSheetName: result.title,
        copiedAt: new Date().toISOString(),
        sheetsResponse: result,
      };
    });
  }

  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (inputs.sourceSheetId === undefined || !Number.isInteger(inputs.sourceSheetId)) {
      throw new Error('Source sheet ID must be an integer');
    }

    return true;
  }

  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet nguồn',
          minLength: 1,
        },
        sourceSheetId: {
          type: 'integer',
          description: 'ID của sheet cần sao chép',
        },
        destinationSpreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet đích (mặc định: cùng spreadsheet)',
        },
        newSheetName: {
          type: 'string',
          description: 'Tên cho sheet mới (mặc định: "Copy of [tên cũ]")',
        },
      },
      required: ['spreadsheetId', 'sourceSheetId'],
    };
  }

  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        sourceSpreadsheetId: { type: 'string' },
        sourceSheetId: { type: 'integer' },
        destinationSpreadsheetId: { type: 'string' },
        newSheetId: { type: 'integer' },
        newSheetName: { type: 'string' },
        copiedAt: { type: 'string' },
        sheetsResponse: { type: 'object' },
      },
    };
  }
}
