import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleAnalyticsService } from '../../../../../shared/services/google/analytics/google-analytics.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Analytics Page Views node
 */
export interface GoogleAnalyticsPageViewsInput {
  /**
   * Property ID (GA4 property)
   */
  propertyId: string;

  /**
   * Date range
   */
  dateRange: {
    startDate: string; // YYYY-MM-DD
    endDate: string;   // YYYY-MM-DD
  };

  /**
   * Page path filter (optional)
   */
  pagePath?: string;

  /**
   * Country filter (optional)
   */
  country?: string;

  /**
   * Device category filter (optional)
   */
  deviceCategory?: 'desktop' | 'mobile' | 'tablet';

  /**
   * Include page title (optional, default: false)
   */
  includePageTitle?: boolean;

  /**
   * Include source/medium (optional, default: false)
   */
  includeSourceMedium?: boolean;

  /**
   * Limit (optional, default: 100)
   */
  limit?: number;

  /**
   * Order by (optional, default: 'screenPageViews')
   */
  orderBy?: 'screenPageViews' | 'sessions' | 'activeUsers';

  /**
   * Order direction (optional, default: 'desc')
   */
  orderDirection?: 'asc' | 'desc';
}

/**
 * Output schema cho Google Analytics Page Views node
 */
export interface GoogleAnalyticsPageViewsOutput {
  /**
   * Page views data
   */
  pageViews: Array<{
    pagePath: string;
    pageTitle?: string;
    screenPageViews: number;
    sessions: number;
    activeUsers: number;
    bounceRate?: number;
    avgSessionDuration?: number;
    sourceMedium?: string;
  }>;

  /**
   * Summary metrics
   */
  summary: {
    totalPageViews: number;
    totalSessions: number;
    totalActiveUsers: number;
    avgBounceRate: number;
    avgSessionDuration: number;
  };

  /**
   * Metadata
   */
  metadata: {
    propertyId: string;
    dateRange: {
      startDate: string;
      endDate: string;
    };
    totalRows: number;
    filters: {
      pagePath?: string;
      country?: string;
      deviceCategory?: string;
    };
  };
}

/**
 * Executor để lấy page views từ Google Analytics
 */
@Injectable()
export class GoogleAnalyticsPageViewsExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.analytics.page_views';
  readonly category = NodeCategory.GOOGLE_ANALYTICS;
  readonly name = 'Get Page Views';
  readonly description = 'Lấy dữ liệu page views từ Google Analytics GA4';

  constructor(
    integrationService: IntegrationService,
    private readonly analyticsService: GoogleAnalyticsService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        propertyId: { type: 'string' },
        dateRange: {
          type: 'object',
          properties: {
            startDate: { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
            endDate: { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
          },
          required: ['startDate', 'endDate'],
        },
        pagePath: { type: 'string' },
        country: { type: 'string' },
        deviceCategory: { type: 'string', enum: ['desktop', 'mobile', 'tablet'] },
        includePageTitle: { type: 'boolean' },
        includeSourceMedium: { type: 'boolean' },
        limit: { type: 'number', minimum: 1, maximum: 10000 },
        orderBy: { type: 'string', enum: ['screenPageViews', 'sessions', 'activeUsers'] },
        orderDirection: { type: 'string', enum: ['asc', 'desc'] },
      },
      required: ['propertyId', 'dateRange'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        pageViews: { type: 'array', items: { type: 'object' } },
        summary: { type: 'object' },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node lấy page views
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Getting page views for property: ${inputs.propertyId}`);

      // Validate required inputs
      if (!inputs.propertyId) {
        throw new Error('Property ID is required');
      }

      if (!inputs.dateRange) {
        throw new Error('Date range is required');
      }

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Prepare dimensions
      const dimensions = ['pagePath'];
      if (inputs.includePageTitle) {
        dimensions.push('pageTitle');
      }
      if (inputs.includeSourceMedium) {
        dimensions.push('sessionSourceMedium');
      }

      // Prepare metrics
      const metrics = [
        'screenPageViews',
        'sessions',
        'activeUsers',
        'bounceRate',
        'averageSessionDuration',
      ];

      // Prepare filters
      const dimensionFilter: any = {};
      if (inputs.pagePath || inputs.country || inputs.deviceCategory) {
        const filters: any[] = [];

        if (inputs.pagePath) {
          filters.push({
            fieldName: 'pagePath',
            stringFilter: {
              matchType: 'CONTAINS',
              value: inputs.pagePath,
            },
          });
        }

        if (inputs.country) {
          filters.push({
            fieldName: 'country',
            stringFilter: {
              matchType: 'EXACT',
              value: inputs.country,
            },
          });
        }

        if (inputs.deviceCategory) {
          filters.push({
            fieldName: 'deviceCategory',
            stringFilter: {
              matchType: 'EXACT',
              value: inputs.deviceCategory,
            },
          });
        }

        if (filters.length === 1) {
          dimensionFilter.filter = filters[0];
        } else if (filters.length > 1) {
          dimensionFilter.andGroup = { expressions: filters };
        }
      }

      // Prepare request
      const request = {
        property: `properties/${inputs.propertyId}`,
        dimensions: dimensions.map(name => ({ name })),
        metrics: metrics.map(name => ({ name })),
        dateRanges: [inputs.dateRange],
        dimensionFilter: Object.keys(dimensionFilter).length > 0 ? dimensionFilter : undefined,
        orderBys: [{
          fieldName: inputs.orderBy || 'screenPageViews',
          desc: (inputs.orderDirection || 'desc') === 'desc',
        }],
        limit: inputs.limit || 100,
      };

      // Run report
      const reportResult = await this.analyticsService.runReport(request, accessToken);

      // Process results
      const pageViews = (reportResult.rows || []).map((row: any) => {
        const dimensionValues = row.dimensionValues || [];
        const metricValues = row.metricValues || [];

        const result: any = {
          pagePath: dimensionValues[0]?.value || '',
          screenPageViews: parseInt(metricValues[0]?.value || '0'),
          sessions: parseInt(metricValues[1]?.value || '0'),
          activeUsers: parseInt(metricValues[2]?.value || '0'),
          bounceRate: parseFloat(metricValues[3]?.value || '0'),
          avgSessionDuration: parseFloat(metricValues[4]?.value || '0'),
        };

        let dimensionIndex = 1;
        if (inputs.includePageTitle) {
          result.pageTitle = dimensionValues[dimensionIndex]?.value || '';
          dimensionIndex++;
        }
        if (inputs.includeSourceMedium) {
          result.sourceMedium = dimensionValues[dimensionIndex]?.value || '';
        }

        return result;
      });

      // Calculate summary
      const summary = {
        totalPageViews: pageViews.reduce((sum, item) => sum + item.screenPageViews, 0),
        totalSessions: pageViews.reduce((sum, item) => sum + item.sessions, 0),
        totalActiveUsers: pageViews.reduce((sum, item) => sum + item.activeUsers, 0),
        avgBounceRate: pageViews.length > 0 ? 
          pageViews.reduce((sum, item) => sum + item.bounceRate, 0) / pageViews.length : 0,
        avgSessionDuration: pageViews.length > 0 ? 
          pageViews.reduce((sum, item) => sum + item.avgSessionDuration, 0) / pageViews.length : 0,
      };

      // Prepare output
      const output: GoogleAnalyticsPageViewsOutput = {
        pageViews,
        summary,
        metadata: {
          propertyId: inputs.propertyId,
          dateRange: inputs.dateRange,
          totalRows: pageViews.length,
          filters: {
            pagePath: inputs.pagePath,
            country: inputs.country,
            deviceCategory: inputs.deviceCategory,
          },
        },
      };

      this.logger.log(`Retrieved ${pageViews.length} page views records`);

      return output;
    });
  }
}
