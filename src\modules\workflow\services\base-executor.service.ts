import { Injectable, Logger } from '@nestjs/common';

/**
 * Base service cho node execution
 * Provides common functionality for all node executors
 */
@Injectable()
export class BaseExecutorService {
  private readonly logger = new Logger(BaseExecutorService.name);

  /**
   * Execute a node with given configuration and input data
   * @param nodeType Type of node to execute
   * @param nodeConfig Node configuration
   * @param inputData Input data for the node
   * @param executionContext Execution context
   * @returns Promise<any> Node execution result
   */
  async executeNode(
    nodeType: string,
    nodeConfig: Record<string, any>,
    inputData: Record<string, any>,
    executionContext: Record<string, any>
  ): Promise<any> {
    this.logger.log(`Executing node: ${nodeType}`);
    
    const startTime = Date.now();
    
    try {
      // TODO: Implement actual node execution logic
      // This will be implemented in WK-006: System Node Executors
      
      // Mock execution for now
      const result = await this.mockNodeExecution(nodeType, nodeConfig, inputData);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      return {
        output: result,
        stats: {
          startTime,
          endTime,
          duration,
          memoryUsage: process.memoryUsage().heapUsed,
        },
      };
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.logger.error(`Node execution failed: ${nodeType}`, error);
      
      throw {
        ...error,
        stats: {
          startTime,
          endTime,
          duration,
          memoryUsage: process.memoryUsage().heapUsed,
        },
      };
    }
  }

  /**
   * Mock node execution for testing
   * @private
   */
  private async mockNodeExecution(
    nodeType: string,
    nodeConfig: Record<string, any>,
    inputData: Record<string, any>
  ): Promise<any> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Mock different node types
    switch (nodeType) {
      case 'system.manual.trigger':
        return {
          data: inputData,
          timestamp: new Date().toISOString(),
          triggeredBy: 'manual',
        };
        
      case 'system.http.request':
        return {
          status: 200,
          data: { message: 'Mock HTTP response', config: nodeConfig },
          headers: { 'content-type': 'application/json' },
        };
        
      case 'system.output':
        return {
          finalOutput: inputData,
          processed: true,
          timestamp: new Date().toISOString(),
        };
        
      default:
        return {
          nodeType,
          config: nodeConfig,
          input: inputData,
          processed: true,
          timestamp: new Date().toISOString(),
        };
    }
  }

  /**
   * Validate node configuration
   * @param nodeType Type of node
   * @param nodeConfig Node configuration
   * @returns Promise<boolean> Validation result
   */
  async validateNodeConfig(
    nodeType: string,
    nodeConfig: Record<string, any>
  ): Promise<boolean> {
    // TODO: Implement actual validation logic
    // For now, just check if config is an object
    return typeof nodeConfig === 'object' && nodeConfig !== null;
  }

  /**
   * Get supported node types
   * @returns string[] Array of supported node types
   */
  getSupportedNodeTypes(): string[] {
    return [
      'system.manual.trigger',
      'system.http.request',
      'system.output',
      // More node types will be added in WK-006
    ];
  }

  /**
   * Check if node type is supported
   * @param nodeType Type of node to check
   * @returns boolean Whether node type is supported
   */
  isNodeTypeSupported(nodeType: string): boolean {
    return this.getSupportedNodeTypes().includes(nodeType);
  }
}
