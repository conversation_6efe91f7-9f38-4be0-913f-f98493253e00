import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để tạo Google Spreadsheet mới
 */
@Injectable()
export class GoogleSheetsCreateExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.create';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Create Google Spreadsheet';
  readonly description = 'Tạo Google Spreadsheet mới với các sheet ban đầu';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node tạo Google Spreadsheet
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const { title, sheets = [], locale = 'vi_VN', timeZone = 'Asia/Ho_Chi_Minh' } = inputs;

      if (!title) {
        throw new Error('Spreadsheet title is required');
      }

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Create spreadsheet using GoogleSheetsService
      const spreadsheet = await this.googleSheetsService.createSpreadsheet(accessToken, {
        title,
        sheets: sheets.map((sheet: any) => ({
          title: sheet.title || 'Sheet1',
          rowCount: sheet.rowCount || 1000,
          columnCount: sheet.columnCount || 26,
        })),
      });

      // Return formatted result
      return {
        spreadsheetId: spreadsheet.spreadsheetId,
        spreadsheetUrl: spreadsheet.url,
        title: spreadsheet.title,
        sheets: spreadsheet.sheets?.map(sheet => ({
          sheetId: sheet.sheetId,
          title: sheet.title,
          index: sheet.index,
        })),
        createdAt: new Date().toISOString(),
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.title || typeof inputs.title !== 'string') {
      throw new Error('Title must be a non-empty string');
    }

    if (inputs.sheets && !Array.isArray(inputs.sheets)) {
      throw new Error('Sheets must be an array');
    }

    if (inputs.sheets) {
      inputs.sheets.forEach((sheet: any, index: number) => {
        if (sheet.title && typeof sheet.title !== 'string') {
          throw new Error(`Sheet ${index} title must be a string`);
        }
        if (sheet.rowCount && (!Number.isInteger(sheet.rowCount) || sheet.rowCount <= 0)) {
          throw new Error(`Sheet ${index} rowCount must be a positive integer`);
        }
        if (sheet.columnCount && (!Number.isInteger(sheet.columnCount) || sheet.columnCount <= 0)) {
          throw new Error(`Sheet ${index} columnCount must be a positive integer`);
        }
      });
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Tên của spreadsheet',
          minLength: 1,
          maxLength: 255,
        },
        sheets: {
          type: 'array',
          description: 'Danh sách các sheet ban đầu',
          items: {
            type: 'object',
            properties: {
              title: {
                type: 'string',
                description: 'Tên của sheet',
                default: 'Sheet1',
              },
              rowCount: {
                type: 'integer',
                description: 'Số hàng',
                minimum: 1,
                maximum: 10000,
                default: 1000,
              },
              columnCount: {
                type: 'integer',
                description: 'Số cột',
                minimum: 1,
                maximum: 1000,
                default: 26,
              },
            },
          },
          default: [{ title: 'Sheet1', rowCount: 1000, columnCount: 26 }],
        },
        locale: {
          type: 'string',
          description: 'Locale của spreadsheet',
          default: 'vi_VN',
        },
        timeZone: {
          type: 'string',
          description: 'Timezone của spreadsheet',
          default: 'Asia/Ho_Chi_Minh',
        },
      },
      required: ['title'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet đã tạo',
        },
        spreadsheetUrl: {
          type: 'string',
          description: 'URL của spreadsheet',
        },
        title: {
          type: 'string',
          description: 'Tên của spreadsheet',
        },
        sheets: {
          type: 'array',
          description: 'Danh sách các sheet đã tạo',
          items: {
            type: 'object',
            properties: {
              sheetId: {
                type: 'number',
                description: 'ID của sheet',
              },
              title: {
                type: 'string',
                description: 'Tên của sheet',
              },
              index: {
                type: 'number',
                description: 'Thứ tự của sheet',
              },
            },
          },
        },
        createdAt: {
          type: 'string',
          description: 'Thời gian tạo',
        },
      },
    };
  }
}
