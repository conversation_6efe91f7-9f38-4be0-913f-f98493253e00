/**
 * Enhanced job payload with webhook data support
 * Định nghĩa structure cho queue jobs giữa BE App và Worker
 */
export interface WorkflowExecutionJob {
  /**
   * ID của lần thực thi workflow
   */
  executionId: string;

  /**
   * ID của workflow cần thực thi
   */
  workflowId: string;

  /**
   * ID của user sở hữu workflow
   */
  userId: number;

  /**
   * Loại trigger kích hoạt workflow
   * Examples: 'webhook.facebook', 'webhook.zalo', 'manual', 'scheduled'
   */
  triggerType: string;

  /**
   * Dữ liệu trigger từ webhook hoặc manual trigger
   * Webhook payload từ BE App hoặc manual input data
   */
  triggerData: any;

  /**
   * Options cho execution
   */
  options?: {
    /**
     * Có enable SSE streaming không
     */
    enableSSE?: boolean;

    /**
     * Timeout cho execution (milliseconds)
     */
    timeout?: number;

    /**
     * Priority của job (1-10, 10 = highest)
     */
    priority?: number;

    /**
     * <PERSON><PERSON> retry khi failed không
     */
    enableRetry?: boolean;

    /**
     * Số lần retry tối đa
     */
    maxRetries?: number;

    /**
     * Delay giữa các retry (milliseconds)
     */
    retryDelay?: number;
  };

  /**
   * Metadata bổ sung cho job
   */
  metadata?: {
    /**
     * Source của job (webhook, api, scheduled)
     */
    source?: string;

    /**
     * IP address của request (nếu có)
     */
    clientIp?: string;

    /**
     * User agent của request (nếu có)
     */
    userAgent?: string;

    /**
     * Additional tracking data
     */
    tracking?: Record<string, any>;
  };
}

/**
 * Job payload cho test single node
 */
export interface NodeTestJob {
  /**
   * ID của node cần test
   */
  nodeId: string;

  /**
   * Type của node
   */
  nodeType: string;

  /**
   * Input data cho node test
   */
  testInput: any;

  /**
   * ID của user thực hiện test
   */
  userId: number;

  /**
   * Mock data cho dependencies
   */
  mockData?: Record<string, any>;

  /**
   * Options cho test
   */
  options?: {
    /**
     * Có enable SSE streaming không
     */
    enableSSE?: boolean;

    /**
     * Timeout cho test (milliseconds)
     */
    timeout?: number;
  };
}

/**
 * Job result cho workflow execution
 */
export interface WorkflowJobResult {
  /**
   * ID của execution
   */
  executionId: string;

  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Status cuối cùng
   */
  status: 'completed' | 'failed' | 'paused';

  /**
   * Thời gian bắt đầu
   */
  startedAt: number;

  /**
   * Thời gian kết thúc
   */
  finishedAt: number;

  /**
   * Output data của workflow
   */
  output?: any;

  /**
   * Error message nếu failed
   */
  error?: string;

  /**
   * Error details cho debugging
   */
  errorDetails?: any;

  /**
   * Execution statistics
   */
  stats?: {
    /**
     * Tổng số nodes đã thực thi
     */
    totalNodes: number;

    /**
     * Số nodes thành công
     */
    successfulNodes: number;

    /**
     * Số nodes failed
     */
    failedNodes: number;

    /**
     * Tổng thời gian thực thi (milliseconds)
     */
    totalExecutionTime: number;

    /**
     * Memory usage peak
     */
    peakMemoryUsage?: number;
  };
}

/**
 * Job result cho node test
 */
export interface NodeTestResult {
  /**
   * ID của node test
   */
  nodeId: string;

  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Output data của node
   */
  output?: any;

  /**
   * Error message nếu failed
   */
  error?: string;

  /**
   * Error details cho debugging
   */
  errorDetails?: any;

  /**
   * Test metadata
   */
  metadata?: {
    /**
     * Thời gian thực thi (milliseconds)
     */
    executionTime: number;

    /**
     * Memory usage
     */
    memoryUsage?: number;

    /**
     * Input data đã sử dụng
     */
    inputData: any;

    /**
     * Validation results
     */
    validation?: {
      inputValid: boolean;
      outputValid: boolean;
      errors?: string[];
    };
  };
}

/**
 * Queue names cho các loại jobs
 */
export enum WorkflowQueueNames {
  WORKFLOW_EXECUTION = 'workflow-execution',
  NODE_TEST = 'node-test',
  WORKFLOW_CLEANUP = 'workflow-cleanup',
}

/**
 * Event types cho SSE streaming
 */
export enum WorkflowEventTypes {
  EXECUTION_STARTED = 'execution.started',
  EXECUTION_COMPLETED = 'execution.completed',
  EXECUTION_FAILED = 'execution.failed',
  EXECUTION_PAUSED = 'execution.paused',
  NODE_STARTED = 'node.started',
  NODE_COMPLETED = 'node.completed',
  NODE_FAILED = 'node.failed',
  NODE_SKIPPED = 'node.skipped',
  TEST_STARTED = 'test.started',
  TEST_COMPLETED = 'test.completed',
  TEST_FAILED = 'test.failed',
}
