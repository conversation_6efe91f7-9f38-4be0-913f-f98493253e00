import { Injectable, Logger } from '@nestjs/common';
import { WorkflowExecutionJobData, WorkflowNodeExecutionJobData } from '../../../queue/queue.types';
import { BaseExecutorService } from './base-executor.service';
import { LoggingService } from './logging.service';

/**
 * Service thực thi workflow và node execution
 * Orchestrates workflow execution và manages node execution flow
 */
@Injectable()
export class WorkflowExecutorService {
  private readonly logger = new Logger(WorkflowExecutorService.name);

  constructor(
    private readonly baseExecutorService: BaseExecutorService,
    private readonly loggingService: LoggingService,
  ) {}

  /**
   * Execute complete workflow
   * @param data Workflow execution job data
   * @returns Promise<any> Workflow execution result
   */
  async executeWorkflow(data: WorkflowExecutionJobData): Promise<any> {
    const { executionId, workflowId, userId, triggerData, triggerType } = data;
    
    this.logger.log(`Starting workflow execution: ${executionId} for workflow: ${workflowId}`);
    
    try {
      // Initialize execution context
      const executionContext = {
        executionId,
        workflowId,
        userId,
        triggerData,
        triggerType,
        startTime: Date.now(),
        variables: {},
        nodeResults: {},
      };

      // Log execution start
      await this.loggingService.logInfo(
        executionId,
        'Workflow execution initialized',
        { 
          workflowId, 
          userId, 
          triggerType,
          triggerData: this.sanitizeLogData(triggerData)
        }
      );

      // TODO: Load workflow definition from database
      // For now, we'll simulate workflow execution
      const workflowDefinition = await this.loadWorkflowDefinition(workflowId);
      
      if (!workflowDefinition) {
        throw new Error(`Workflow definition not found: ${workflowId}`);
      }

      // Execute workflow nodes in sequence
      const result = await this.executeWorkflowNodes(workflowDefinition, executionContext);

      // Log execution completion
      await this.loggingService.logInfo(
        executionId,
        'Workflow execution completed successfully',
        { 
          result: this.sanitizeLogData(result),
          duration: Date.now() - executionContext.startTime,
          nodesExecuted: Object.keys(executionContext.nodeResults).length
        }
      );

      return {
        executionId,
        status: 'completed',
        result,
        stats: {
          startTime: executionContext.startTime,
          endTime: Date.now(),
          duration: Date.now() - executionContext.startTime,
          nodesExecuted: Object.keys(executionContext.nodeResults).length,
          nodesFailed: 0,
        },
      };

    } catch (error) {
      this.logger.error(`Workflow execution failed: ${executionId}`, error);
      
      await this.loggingService.logError(
        executionId,
        'Workflow execution failed',
        { error: error.message, stack: error.stack }
      );

      return {
        executionId,
        status: 'failed',
        error: {
          message: error.message,
          code: 'WORKFLOW_EXECUTION_ERROR',
          details: error.stack,
        },
        stats: {
          startTime: Date.now(),
          endTime: Date.now(),
          duration: 0,
          nodesExecuted: 0,
          nodesFailed: 1,
        },
      };
    }
  }

  /**
   * Execute single node
   * @param data Node execution job data
   * @returns Promise<any> Node execution result
   */
  async executeNode(data: WorkflowNodeExecutionJobData): Promise<any> {
    const { executionId, nodeId, nodeType, nodeConfig, inputData, executionContext } = data;
    
    this.logger.log(`Executing node: ${nodeId} (${nodeType}) for execution: ${executionId}`);
    
    try {
      // Log node execution start
      await this.loggingService.logInfo(
        executionId,
        `Node execution started: ${nodeType}`,
        { 
          nodeId, 
          nodeType,
          inputData: this.sanitizeLogData(inputData)
        }
      );

      // Execute node using base executor
      const result = await this.baseExecutorService.executeNode(
        nodeType,
        nodeConfig,
        inputData,
        executionContext
      );

      // Log node execution completion
      await this.loggingService.logInfo(
        executionId,
        `Node execution completed: ${nodeType}`,
        { 
          nodeId,
          result: this.sanitizeLogData(result),
          duration: result.stats?.duration || 0
        }
      );

      return {
        nodeId,
        nodeType,
        status: 'completed',
        result: result.output,
        stats: result.stats,
      };

    } catch (error) {
      this.logger.error(`Node execution failed: ${nodeId}`, error);
      
      await this.loggingService.logError(
        executionId,
        `Node execution failed: ${nodeType}`,
        { nodeId, error: error.message, stack: error.stack }
      );

      return {
        nodeId,
        nodeType,
        status: 'failed',
        error: {
          message: error.message,
          code: 'NODE_EXECUTION_ERROR',
          details: error.stack,
        },
        stats: {
          startTime: Date.now(),
          endTime: Date.now(),
          duration: 0,
        },
      };
    }
  }

  /**
   * Cleanup execution data
   * @param executionId Execution ID to cleanup
   */
  async cleanupExecution(executionId: string): Promise<void> {
    this.logger.log(`Cleaning up execution: ${executionId}`);
    
    try {
      // TODO: Implement cleanup logic
      // - Remove temporary files
      // - Clear cached data
      // - Update execution status
      
      await this.loggingService.logInfo(
        executionId,
        'Execution cleanup completed',
        { cleanupTime: Date.now() }
      );
      
    } catch (error) {
      this.logger.warn(`Cleanup failed for execution: ${executionId}`, error);
      throw error;
    }
  }

  /**
   * Load workflow definition from database
   * @private
   */
  private async loadWorkflowDefinition(workflowId: string): Promise<any> {
    // TODO: Implement database lookup
    // For now, return a mock workflow definition
    return {
      id: workflowId,
      name: 'Sample Workflow',
      nodes: [
        {
          id: 'node-1',
          type: 'system.manual.trigger',
          config: {},
        },
        {
          id: 'node-2',
          type: 'system.http.request',
          config: {
            url: 'https://api.example.com/test',
            method: 'GET',
          },
        },
        {
          id: 'node-3',
          type: 'system.output',
          config: {},
        },
      ],
      edges: [
        { from: 'node-1', to: 'node-2' },
        { from: 'node-2', to: 'node-3' },
      ],
    };
  }

  /**
   * Execute workflow nodes in sequence
   * @private
   */
  private async executeWorkflowNodes(workflowDefinition: any, executionContext: any): Promise<any> {
    const { nodes, edges } = workflowDefinition;
    
    // Simple sequential execution for now
    // TODO: Implement proper graph execution with parallel nodes
    
    let currentData = executionContext.triggerData;
    
    for (const node of nodes) {
      this.logger.debug(`Executing node: ${node.id} (${node.type})`);
      
      try {
        const nodeResult = await this.baseExecutorService.executeNode(
          node.type,
          node.config,
          currentData,
          executionContext
        );
        
        // Store node result in context
        executionContext.nodeResults[node.id] = nodeResult;
        
        // Use node output as input for next node
        currentData = nodeResult.output || currentData;
        
        await this.loggingService.logInfo(
          executionContext.executionId,
          `Node completed: ${node.id}`,
          { nodeType: node.type, hasOutput: !!nodeResult.output }
        );
        
      } catch (error) {
        this.logger.error(`Node ${node.id} failed:`, error);
        
        await this.loggingService.logError(
          executionContext.executionId,
          `Node failed: ${node.id}`,
          { nodeType: node.type, error: error.message }
        );
        
        throw error;
      }
    }
    
    return currentData;
  }

  /**
   * Sanitize data for logging (remove sensitive information)
   * @private
   */
  private sanitizeLogData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const sanitized = { ...data };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
}
