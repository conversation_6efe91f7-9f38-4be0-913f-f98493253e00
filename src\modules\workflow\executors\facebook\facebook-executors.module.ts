import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { FacebookModule } from '../../../../shared/services/facebook/facebook.module';

// Import Facebook Page Executors
import { FacebookPageCreatePostExecutor } from './page/facebook-page-create-post.executor';
import { FacebookPageGetPostsExecutor } from './page/facebook-page-get-posts.executor';
import { FacebookPageGetInfoExecutor } from './page/facebook-page-get-info.executor';

// Import Facebook Business/Ads Executors - TODO: Fix import paths
// import { FacebookAdsGetCampaignsExecutor } from './business/facebook-ads-get-campaigns.executor';
// import { FacebookAdsCreateCampaignExecutor } from './business/facebook-ads-create-campaign.executor';

/**
 * <PERSON><PERSON><PERSON> chứa tất cả Facebook service executors
 */
@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    FacebookModule, // Import Facebook services
  ],
  providers: [
    // Facebook Page Executors (3 total)
    FacebookPageCreatePostExecutor,
    FacebookPageGetPostsExecutor,
    FacebookPageGetInfoExecutor,

    // Facebook Business/Ads Executors (2 total) - TODO: Enable when imports fixed
    // FacebookAdsGetCampaignsExecutor,
    // FacebookAdsCreateCampaignExecutor,
  ],
  exports: [
    // Facebook Page Executors (3 total)
    FacebookPageCreatePostExecutor,
    FacebookPageGetPostsExecutor,
    FacebookPageGetInfoExecutor,

    // Facebook Business/Ads Executors (2 total) - TODO: Enable when imports fixed
    // FacebookAdsGetCampaignsExecutor,
    // FacebookAdsCreateCampaignExecutor,
  ],
})
export class FacebookExecutorsModule {}
