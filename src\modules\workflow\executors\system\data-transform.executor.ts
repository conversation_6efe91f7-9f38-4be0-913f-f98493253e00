import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Data Transform Node Executor
 * Handles data transformation operations using various methods
 */
@Injectable()
export class DataTransformExecutor extends BaseNodeExecutor {
  readonly type = 'system.transform.data';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Data Transform';
  readonly description = 'Transform data using mapping rules, JavaScript expressions, or templates';

  /**
   * Execute data transformation
   * @param inputs - Transform configuration and data
   * @param context - Execution context
   * @returns Transformed data
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      data,
      transformType = 'mapping',
      mapping = {},
      script,
      template,
      options = {},
    } = inputs;

    this.logger.debug(`Transforming data using method: ${transformType}`);

    try {
      let transformedData: any;

      switch (transformType) {
        case 'mapping':
          transformedData = await this.transformByMapping(data, mapping, options);
          break;
        case 'script':
          transformedData = await this.transformByScript(data, script, options, context);
          break;
        case 'template':
          transformedData = await this.transformByTemplate(data, template, options);
          break;
        case 'filter':
          transformedData = await this.transformByFilter(data, options);
          break;
        case 'aggregate':
          transformedData = await this.transformByAggregation(data, options);
          break;
        default:
          throw new Error(`Unsupported transform type: ${transformType}`);
      }

      return {
        data: transformedData,
        originalData: data,
        transformType,
        transformedAt: new Date().toISOString(),
        metadata: {
          inputSize: this.getDataSize(data),
          outputSize: this.getDataSize(transformedData),
          transformMethod: transformType,
        },
      };

    } catch (error) {
      this.logger.error(`Data transformation failed: ${error.message}`);
      throw new Error(`Data transformation failed: ${error.message}`);
    }
  }

  /**
   * Transform data using field mapping
   * @private
   */
  private async transformByMapping(data: any, mapping: any, options: any): Promise<any> {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object for mapping transformation');
    }

    const result: any = {};

    for (const [targetField, sourceField] of Object.entries(mapping)) {
      if (typeof sourceField === 'string') {
        // Simple field mapping
        result[targetField] = this.getNestedValue(data, sourceField);
      } else if (typeof sourceField === 'object' && sourceField !== null) {
        // Complex mapping with transformation
        const mappingConfig = sourceField as any;
        result[targetField] = await this.applyFieldTransformation(
          data,
          mappingConfig,
          options
        );
      }
    }

    return result;
  }

  /**
   * Transform data using JavaScript script
   * @private
   */
  private async transformByScript(
    data: any,
    script: string,
    options: any,
    context: ExecutionContext
  ): Promise<any> {
    if (!script) {
      throw new Error('Script is required for script transformation');
    }

    // Create safe execution context
    const scriptContext = {
      data,
      options,
      context: {
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
      },
      // Utility functions
      utils: {
        now: () => Date.now(),
        today: () => new Date().toISOString().split('T')[0],
        uuid: () => this.generateUUID(),
        isEmpty: (value: any) => value == null || value === '',
        isArray: (value: any) => Array.isArray(value),
        isObject: (value: any) => value && typeof value === 'object' && !Array.isArray(value),
        clone: (value: any) => JSON.parse(JSON.stringify(value)),
        merge: (...objects: any[]) => Object.assign({}, ...objects),
        pick: (obj: any, keys: string[]) => {
          const result: any = {};
          keys.forEach(key => {
            if (key in obj) result[key] = obj[key];
          });
          return result;
        },
        omit: (obj: any, keys: string[]) => {
          const result = { ...obj };
          keys.forEach(key => delete result[key]);
          return result;
        },
      },
    };

    // Sanitize script
    const sanitizedScript = this.sanitizeScript(script);

    try {
      // Execute script using Function constructor
      const func = new Function(
        ...Object.keys(scriptContext),
        `return (function() { ${sanitizedScript} })()`
      );
      
      return func(...Object.values(scriptContext));
    } catch (error) {
      throw new Error(`Script execution failed: ${error.message}`);
    }
  }

  /**
   * Transform data using template
   * @private
   */
  private async transformByTemplate(data: any, template: any, options: any): Promise<any> {
    if (!template) {
      throw new Error('Template is required for template transformation');
    }

    return this.processTemplate(template, data, options);
  }

  /**
   * Transform data using filter
   * @private
   */
  private async transformByFilter(data: any, options: any): Promise<any> {
    if (!Array.isArray(data)) {
      throw new Error('Data must be an array for filter transformation');
    }

    const { condition, limit, offset = 0 } = options;

    let filtered = data;

    // Apply condition filter
    if (condition) {
      filtered = data.filter((item, index) => {
        return this.evaluateFilterCondition(condition, item, index);
      });
    }

    // Apply pagination
    if (offset > 0) {
      filtered = filtered.slice(offset);
    }

    if (limit && limit > 0) {
      filtered = filtered.slice(0, limit);
    }

    return filtered;
  }

  /**
   * Transform data using aggregation
   * @private
   */
  private async transformByAggregation(data: any, options: any): Promise<any> {
    if (!Array.isArray(data)) {
      throw new Error('Data must be an array for aggregation transformation');
    }

    const { groupBy, aggregations = {} } = options;

    if (groupBy) {
      return this.groupAndAggregate(data, groupBy, aggregations);
    } else {
      return this.simpleAggregate(data, aggregations);
    }
  }

  /**
   * Apply field transformation
   * @private
   */
  private async applyFieldTransformation(data: any, config: any, options: any): Promise<any> {
    const { source, transform, defaultValue } = config;
    
    let value = source ? this.getNestedValue(data, source) : data;

    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (transform) {
      switch (transform.type) {
        case 'uppercase':
          return String(value).toUpperCase();
        case 'lowercase':
          return String(value).toLowerCase();
        case 'trim':
          return String(value).trim();
        case 'number':
          return Number(value);
        case 'boolean':
          return Boolean(value);
        case 'date':
          return new Date(value).toISOString();
        case 'format':
          return this.formatValue(value, transform.format);
        default:
          return value;
      }
    }

    return value;
  }

  /**
   * Process template with data substitution
   * @private
   */
  private processTemplate(template: any, data: any, options: any): any {
    if (typeof template === 'string') {
      return this.substituteVariables(template, data);
    } else if (Array.isArray(template)) {
      return template.map(item => this.processTemplate(item, data, options));
    } else if (template && typeof template === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.processTemplate(value, data, options);
      }
      return result;
    }

    return template;
  }

  /**
   * Substitute variables in string template
   * @private
   */
  private substituteVariables(template: string, data: any): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const value = this.getNestedValue(data, path.trim());
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Get nested value from object using dot notation
   * @private
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Evaluate filter condition
   * @private
   */
  private evaluateFilterCondition(condition: string, item: any, index: number): boolean {
    try {
      const func = new Function('item', 'index', `return ${condition}`);
      return Boolean(func(item, index));
    } catch (error) {
      this.logger.warn(`Filter condition evaluation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Group and aggregate data
   * @private
   */
  private groupAndAggregate(data: any[], groupBy: string, aggregations: any): any {
    const groups: { [key: string]: any[] } = {};

    // Group data
    data.forEach(item => {
      const groupKey = this.getNestedValue(item, groupBy);
      const key = String(groupKey);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
    });

    // Apply aggregations to each group
    const result: any = {};
    for (const [groupKey, groupItems] of Object.entries(groups)) {
      result[groupKey] = this.simpleAggregate(groupItems, aggregations);
    }

    return result;
  }

  /**
   * Simple aggregation without grouping
   * @private
   */
  private simpleAggregate(data: any[], aggregations: any): any {
    const result: any = {};

    for (const [field, operation] of Object.entries(aggregations)) {
      const values = data.map(item => this.getNestedValue(item, field)).filter(v => v != null);
      
      switch (operation) {
        case 'count':
          result[field] = data.length;
          break;
        case 'sum':
          result[field] = values.reduce((sum, val) => sum + Number(val), 0);
          break;
        case 'avg':
          result[field] = values.length > 0 ? values.reduce((sum, val) => sum + Number(val), 0) / values.length : 0;
          break;
        case 'min':
          result[field] = values.length > 0 ? Math.min(...values.map(Number)) : null;
          break;
        case 'max':
          result[field] = values.length > 0 ? Math.max(...values.map(Number)) : null;
          break;
        case 'first':
          result[field] = values.length > 0 ? values[0] : null;
          break;
        case 'last':
          result[field] = values.length > 0 ? values[values.length - 1] : null;
          break;
        default:
          result[field] = values;
      }
    }

    return result;
  }

  /**
   * Format value according to format specification
   * @private
   */
  private formatValue(value: any, format: string): string {
    // Simple format implementation
    if (format.includes('date')) {
      return new Date(value).toLocaleDateString();
    } else if (format.includes('currency')) {
      return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(value));
    } else if (format.includes('percent')) {
      return `${(Number(value) * 100).toFixed(2)}%`;
    }
    
    return String(value);
  }

  /**
   * Get data size for metadata
   * @private
   */
  private getDataSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Sanitize script to prevent dangerous operations
   * @private
   */
  private sanitizeScript(script: string): string {
    const dangerousPatterns = [
      /require\s*\(/g,
      /import\s+/g,
      /eval\s*\(/g,
      /Function\s*\(/g,
      /setTimeout\s*\(/g,
      /setInterval\s*\(/g,
      /process\./g,
      /global\./g,
      /window\./g,
      /document\./g,
    ];

    let sanitized = script;
    dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Generate UUID
   * @private
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['data', 'transformType'],
      properties: {
        data: {
          description: 'Data to transform',
        },
        transformType: {
          type: 'string',
          enum: ['mapping', 'script', 'template', 'filter', 'aggregate'],
          description: 'Type of transformation to apply',
        },
        mapping: {
          type: 'object',
          description: 'Field mapping configuration (for mapping type)',
        },
        script: {
          type: 'string',
          description: 'JavaScript transformation script (for script type)',
        },
        template: {
          description: 'Template for data transformation (for template type)',
        },
        options: {
          type: 'object',
          description: 'Additional transformation options',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        data: {
          description: 'Transformed data',
        },
        originalData: {
          description: 'Original input data',
        },
        transformType: {
          type: 'string',
          description: 'Type of transformation applied',
        },
        transformedAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when transformation was applied',
        },
        metadata: {
          type: 'object',
          properties: {
            inputSize: {
              type: 'number',
              description: 'Size of input data in characters',
            },
            outputSize: {
              type: 'number',
              description: 'Size of output data in characters',
            },
            transformMethod: {
              type: 'string',
              description: 'Transformation method used',
            },
          },
        },
      },
    };
  }
}
