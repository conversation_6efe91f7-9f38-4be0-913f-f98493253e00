import { IsString, IsOptional, IsObject, <PERSON><PERSON><PERSON>y, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Node position DTO for workflow canvas
 */
export class NodePositionDto {
  @ApiProperty({ description: 'X coordinate' })
  @IsOptional()
  x?: number;

  @ApiProperty({ description: 'Y coordinate' })
  @IsOptional()
  y?: number;
}

/**
 * Node data DTO for workflow definition
 */
export class NodeDataDto {
  @ApiProperty({ description: 'Node type' })
  @IsString()
  type: string;

  @ApiPropertyOptional({ description: 'Node configuration' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Node label' })
  @IsOptional()
  @IsString()
  label?: string;
}

/**
 * Workflow node DTO
 */
export class WorkflowNodeDto {
  @ApiProperty({ description: 'Node ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Node data', type: NodeDataDto })
  @ValidateNested()
  @Type(() => NodeDataDto)
  data: NodeDataDto;

  @ApiPropertyOptional({ description: 'Node position', type: NodePositionDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => NodePositionDto)
  position?: NodePositionDto;

  @ApiPropertyOptional({ description: 'Node width' })
  @IsOptional()
  width?: number;

  @ApiPropertyOptional({ description: 'Node height' })
  @IsOptional()
  height?: number;
}

/**
 * Workflow edge DTO
 */
export class WorkflowEdgeDto {
  @ApiProperty({ description: 'Edge ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Source node ID' })
  @IsString()
  source: string;

  @ApiProperty({ description: 'Target node ID' })
  @IsString()
  target: string;

  @ApiPropertyOptional({ description: 'Source handle ID' })
  @IsOptional()
  @IsString()
  sourceHandle?: string;

  @ApiPropertyOptional({ description: 'Target handle ID' })
  @IsOptional()
  @IsString()
  targetHandle?: string;

  @ApiPropertyOptional({ description: 'Edge type' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: 'Edge data' })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Edge condition for conditional flows' })
  @IsOptional()
  @IsString()
  condition?: string;
}
