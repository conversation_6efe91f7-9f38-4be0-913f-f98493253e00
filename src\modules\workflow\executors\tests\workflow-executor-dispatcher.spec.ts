import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowExecutorDispatcher } from '../dispatcher/workflow-executor-dispatcher';
import { NodeExecutorFactory } from '../registry/node-executor-factory';
import { LoggingService } from '../../services/logging.service';
import { EventService } from '../../services/event.service';

describe('WorkflowExecutorDispatcher', () => {
  let dispatcher: WorkflowExecutorDispatcher;
  let nodeExecutorFactory: jest.Mocked<NodeExecutorFactory>;
  let loggingService: jest.Mocked<LoggingService>;
  let eventService: jest.Mocked<EventService>;

  beforeEach(async () => {
    const mockNodeExecutorFactory = {
      createExecutor: jest.fn(),
    };

    const mockLoggingService = {
      logNodeEvent: jest.fn(),
    };

    const mockEventService = {
      emitNodeEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowExecutorDispatcher,
        { provide: NodeExecutorFactory, useValue: mockNodeExecutorFactory },
        { provide: LoggingService, useValue: mockLoggingService },
        { provide: EventService, useValue: mockEventService },
      ],
    }).compile();

    dispatcher = module.get<WorkflowExecutorDispatcher>(WorkflowExecutorDispatcher);
    nodeExecutorFactory = module.get(NodeExecutorFactory);
    loggingService = module.get(LoggingService);
    eventService = module.get(EventService);
  });

  it('should be defined', () => {
    expect(dispatcher).toBeDefined();
  });

  // TODO: Add more tests when LoggingService and EventService methods are implemented
});
