import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionLogger } from '../base/execution-logger';
import { LoggingService } from '../../services/logging.service';
import { EventService } from '../../services/event.service';

describe('ExecutionLogger', () => {
  let logger: ExecutionLogger;
  let loggingService: jest.Mocked<LoggingService>;
  let eventService: jest.Mocked<EventService>;

  beforeEach(async () => {
    const mockLoggingService = {
      logNodeEvent: jest.fn(),
    };

    const mockEventService = {
      emitNodeEvent: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExecutionLogger,
        { provide: LoggingService, useValue: mockLoggingService },
        { provide: EventService, useValue: mockEventService },
      ],
    }).compile();

    logger = module.get<ExecutionLogger>(ExecutionLogger);
    loggingService = module.get(LoggingService);
    eventService = module.get(EventService);
  });

  it('should be defined', () => {
    expect(logger).toBeDefined();
  });

  // TODO: Add more tests when LoggingService and EventService methods are implemented
});
