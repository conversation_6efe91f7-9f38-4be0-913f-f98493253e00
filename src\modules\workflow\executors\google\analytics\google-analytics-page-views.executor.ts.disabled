import { Injectable, Logger } from '@nestjs/common';
import { GoogleAnalyticsService } from '../../../../../shared/services/google/analytics/google-analytics.service';
import { NodeExecutor } from '../../../interfaces/node-executor.interface';
import { ExecutionContext, NodeExecutionResult } from '../../../interfaces/execution-context.interface';

/**
 * Input schema cho Google Analytics Page Views node
 */
export interface GoogleAnalyticsPageViewsInput {
  /**
   * Access token cho Google Analytics API
   */
  accessToken: string;

  /**
   * Property ID của Google Analytics
   */
  propertyId: string;

  /**
   * <PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)
   */
  startDate: string;

  /**
   * <PERSON><PERSON>y kết thúc (YYYY-MM-DD)
   */
  endDate: string;

  /**
   * Limit số lượng pages (mặc định: 50)
   */
  limit?: number;

  /**
   * Có group theo ngày không
   */
  groupByDate?: boolean;

  /**
   * Filter theo page path (optional)
   */
  pagePathFilter?: string;

  /**
   * Filter type cho page path
   */
  pagePathFilterType?: 'EXACT' | 'BEGINS_WITH' | 'ENDS_WITH' | 'CONTAINS';
}

/**
 * Output schema cho Google Analytics Page Views node
 */
export interface GoogleAnalyticsPageViewsOutput {
  /**
   * Tổng page views
   */
  totalPageViews: number;

  /**
   * Tổng unique page views
   */
  totalUniquePageViews: number;

  /**
   * Average time on page (seconds)
   */
  averageTimeOnPage: number;

  /**
   * Bounce rate (%)
   */
  bounceRate: number;

  /**
   * Chi tiết theo page
   */
  pages: Array<{
    pagePath: string;
    pageTitle: string;
    pageViews: number;
    uniquePageViews: number;
    averageTimeOnPage: number;
    bounceRate: number;
    exitRate: number;
  }>;

  /**
   * Chi tiết theo ngày (nếu groupByDate = true)
   */
  dailyData?: Array<{
    date: string;
    pageViews: number;
    uniquePageViews: number;
    averageTimeOnPage: number;
    bounceRate: number;
  }>;

  /**
   * Metadata
   */
  metadata: {
    propertyId: string;
    startDate: string;
    endDate: string;
    totalRows: number;
    samplingLevel?: string;
  };
}

/**
 * Executor để lấy page views report từ Google Analytics
 */
@Injectable()
export class GoogleAnalyticsPageViewsExecutor implements NodeExecutor {
  private readonly logger = new Logger(GoogleAnalyticsPageViewsExecutor.name);

  constructor(private readonly analyticsService: GoogleAnalyticsService) {}

  /**
   * Thực thi node lấy page views report
   */
  async execute(
    inputs: GoogleAnalyticsPageViewsInput,
    context: ExecutionContext,
  ): Promise<NodeExecutionResult> {
    try {
      this.logger.log(`Getting page views report for property: ${inputs.propertyId}`);

      // Validate required inputs
      if (!inputs.accessToken) {
        throw new Error('Access token is required');
      }

      if (!inputs.propertyId) {
        throw new Error('Property ID is required');
      }

      if (!inputs.startDate || !inputs.endDate) {
        throw new Error('Start date and end date are required');
      }

      // Prepare dimensions and metrics
      const dimensions = [
        { name: 'pagePath' },
        { name: 'pageTitle' },
      ];

      if (inputs.groupByDate) {
        dimensions.unshift({ name: 'date' });
      }

      const metrics = [
        { name: 'screenPageViews' },
        { name: 'sessions' },
        { name: 'averageSessionDuration' },
        { name: 'bounceRate' },
        { name: 'exitRate' },
      ];

      // Prepare dimension filter
      let dimensionFilter;
      if (inputs.pagePathFilter) {
        dimensionFilter = {
          filter: {
            fieldName: 'pagePath',
            stringFilter: {
              matchType: inputs.pagePathFilterType || 'CONTAINS',
              value: inputs.pagePathFilter,
              caseSensitive: false,
            },
          },
        };
      }

      // Prepare request
      const request = {
        property: inputs.propertyId,
        dimensions,
        metrics,
        dateRanges: [{
          startDate: inputs.startDate,
          endDate: inputs.endDate,
        }],
        dimensionFilter,
        orderBys: [{
          metric: {
            metricName: 'screenPageViews',
          },
          desc: true,
        }],
        limit: inputs.limit?.toString() || '50',
      };

      // Get report
      const reportResult = await this.analyticsService.runReport(
        request,
        inputs.accessToken,
      );

      // Process data
      let totalPageViews = 0;
      let totalSessions = 0;
      let totalSessionDuration = 0;
      let totalBounceRate = 0;

      const pages: any[] = [];
      const dailyData: any[] = [];

      if (reportResult.rows) {
        for (const row of reportResult.rows) {
          const dimensionValues = row.dimensionValues || [];
          const metricValues = row.metricValues || [];

          if (inputs.groupByDate) {
            // Group by date format
            const date = dimensionValues[0]?.value || '';
            const pagePath = dimensionValues[1]?.value || '';
            const pageTitle = dimensionValues[2]?.value || '';
            
            const pageViews = parseInt(metricValues[0]?.value || '0');
            const sessions = parseInt(metricValues[1]?.value || '0');
            const avgSessionDuration = parseFloat(metricValues[2]?.value || '0');
            const bounceRate = parseFloat(metricValues[3]?.value || '0');
            const exitRate = parseFloat(metricValues[4]?.value || '0');

            // Add to daily data
            const existingDay = dailyData.find(d => d.date === date);
            if (existingDay) {
              existingDay.pageViews += pageViews;
              existingDay.uniquePageViews += sessions;
              existingDay.averageTimeOnPage = (existingDay.averageTimeOnPage + avgSessionDuration) / 2;
              existingDay.bounceRate = (existingDay.bounceRate + bounceRate) / 2;
            } else {
              dailyData.push({
                date,
                pageViews,
                uniquePageViews: sessions,
                averageTimeOnPage: avgSessionDuration,
                bounceRate,
              });
            }
          } else {
            // Regular format
            const pagePath = dimensionValues[0]?.value || '';
            const pageTitle = dimensionValues[1]?.value || '';
            
            const pageViews = parseInt(metricValues[0]?.value || '0');
            const sessions = parseInt(metricValues[1]?.value || '0');
            const avgSessionDuration = parseFloat(metricValues[2]?.value || '0');
            const bounceRate = parseFloat(metricValues[3]?.value || '0');
            const exitRate = parseFloat(metricValues[4]?.value || '0');

            pages.push({
              pagePath,
              pageTitle,
              pageViews,
              uniquePageViews: sessions,
              averageTimeOnPage: avgSessionDuration,
              bounceRate,
              exitRate,
            });
          }

          totalPageViews += parseInt(metricValues[0]?.value || '0');
          totalSessions += parseInt(metricValues[1]?.value || '0');
          totalSessionDuration += parseFloat(metricValues[2]?.value || '0');
          totalBounceRate += parseFloat(metricValues[3]?.value || '0');
        }
      }

      // Calculate averages
      const rowCount = reportResult.rows?.length || 1;
      const averageTimeOnPage = totalSessionDuration / rowCount;
      const bounceRate = totalBounceRate / rowCount;

      // Prepare output
      const output: GoogleAnalyticsPageViewsOutput = {
        totalPageViews,
        totalUniquePageViews: totalSessions,
        averageTimeOnPage,
        bounceRate,
        pages: inputs.groupByDate ? [] : pages,
        dailyData: inputs.groupByDate ? dailyData : undefined,
        metadata: {
          propertyId: inputs.propertyId,
          startDate: inputs.startDate,
          endDate: inputs.endDate,
          totalRows: reportResult.rowCount || 0,
          samplingLevel: reportResult.metadata?.samplingMetadatas?.[0]?.samplingLevel,
        },
      };

      this.logger.log(`Page views report completed: ${totalPageViews} total page views`);

      return {
        success: true,
        output,
        metadata: {
          nodeType: 'google_analytics_page_views',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting page views report: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        errorDetails: error,
        metadata: {
          nodeType: 'google_analytics_page_views',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    }
  }
}
