import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Delay Node Executor
 * Implements wait/delay operations in workflows
 */
@Injectable()
export class DelayExecutor extends BaseNodeExecutor {
  readonly type = 'system.delay';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Delay';
  readonly description = 'Wait for a specified duration before continuing workflow execution';

  /**
   * Execute delay operation
   * @param inputs - Delay configuration
   * @param context - Execution context
   * @returns Delay completion result
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      duration,
      unit = 'milliseconds',
      reason = 'Workflow delay',
      maxDuration = 3600000, // 1 hour max
      interruptible = true,
    } = inputs;

    this.logger.debug(`Starting delay: ${duration} ${unit} (${reason})`);

    // Convert duration to milliseconds
    const delayMs = this.convertToMilliseconds(duration, unit);

    // Validate delay duration
    if (delayMs <= 0) {
      throw new Error(`Invalid delay duration: ${duration} ${unit}`);
    }

    if (delayMs > maxDuration) {
      throw new Error(`Delay duration exceeds maximum allowed: ${delayMs}ms > ${maxDuration}ms`);
    }

    const startTime = Date.now();
    
    try {
      // Execute the delay
      await this.executeDelay(delayMs, interruptible, context);
      
      const actualDuration = Date.now() - startTime;
      
      this.logger.debug(`Delay completed: ${actualDuration}ms`);

      return {
        completed: true,
        requestedDuration: delayMs,
        actualDuration,
        unit,
        reason,
        startTime,
        endTime: Date.now(),
        interrupted: false,
        metadata: {
          delayType: 'standard',
          precision: this.calculatePrecision(delayMs, actualDuration),
        },
      };

    } catch (error) {
      const actualDuration = Date.now() - startTime;
      
      if (error.message === 'DELAY_INTERRUPTED') {
        this.logger.debug(`Delay interrupted after ${actualDuration}ms`);
        
        return {
          completed: false,
          requestedDuration: delayMs,
          actualDuration,
          unit,
          reason,
          startTime,
          endTime: Date.now(),
          interrupted: true,
          metadata: {
            delayType: 'interrupted',
            interruptionReason: 'External interruption',
          },
        };
      }
      
      throw error;
    }
  }

  /**
   * Execute the actual delay with interruption support
   * @private
   */
  private async executeDelay(
    delayMs: number,
    interruptible: boolean,
    context: ExecutionContext
  ): Promise<void> {
    if (delayMs <= 0) {
      return;
    }

    // For very short delays, use simple setTimeout
    if (delayMs < 1000) {
      return new Promise(resolve => setTimeout(resolve, delayMs));
    }

    // For longer delays, implement with progress reporting and interruption support
    const chunkSize = Math.min(1000, delayMs / 10); // Report progress every second or 10% of delay
    let remainingMs = delayMs;
    let progress = 0;

    while (remainingMs > 0) {
      const currentChunk = Math.min(chunkSize, remainingMs);
      
      await new Promise(resolve => setTimeout(resolve, currentChunk));
      
      remainingMs -= currentChunk;
      progress = ((delayMs - remainingMs) / delayMs) * 100;

      // Report progress for long delays
      if (delayMs > 5000) {
        await this.reportProgress(progress, context);
      }

      // Check for interruption if enabled
      if (interruptible && await this.checkForInterruption(context)) {
        throw new Error('DELAY_INTERRUPTED');
      }
    }
  }

  /**
   * Convert duration to milliseconds
   * @private
   */
  private convertToMilliseconds(duration: number, unit: string): number {
    switch (unit.toLowerCase()) {
      case 'milliseconds':
      case 'ms':
        return duration;
      case 'seconds':
      case 's':
        return duration * 1000;
      case 'minutes':
      case 'm':
        return duration * 60 * 1000;
      case 'hours':
      case 'h':
        return duration * 60 * 60 * 1000;
      case 'days':
      case 'd':
        return duration * 24 * 60 * 60 * 1000;
      default:
        throw new Error(`Unsupported time unit: ${unit}`);
    }
  }

  /**
   * Calculate delay precision percentage
   * @private
   */
  private calculatePrecision(requested: number, actual: number): number {
    const difference = Math.abs(actual - requested);
    const precision = ((requested - difference) / requested) * 100;
    return Math.max(0, Math.min(100, precision));
  }

  /**
   * Report delay progress
   * @private
   */
  private async reportProgress(progress: number, context: ExecutionContext): Promise<void> {
    try {
      await this.emitEvent('delay.progress', {
        progress: Math.round(progress),
        executionId: context.executionId,
        nodeType: this.type,
      }, context);
    } catch (error) {
      // Don't fail the delay if progress reporting fails
      this.logger.warn(`Failed to report delay progress: ${error.message}`);
    }
  }

  /**
   * Check if delay should be interrupted
   * @private
   */
  private async checkForInterruption(context: ExecutionContext): Promise<boolean> {
    // This would check for workflow cancellation or other interruption signals
    // For now, return false (no interruption)
    // In a real implementation, this might check Redis or database for cancellation flags
    return false;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['duration'],
      properties: {
        duration: {
          type: 'number',
          minimum: 0,
          description: 'Duration to wait',
          examples: [1000, 30, 5],
        },
        unit: {
          type: 'string',
          enum: ['milliseconds', 'ms', 'seconds', 's', 'minutes', 'm', 'hours', 'h', 'days', 'd'],
          default: 'milliseconds',
          description: 'Time unit for the duration',
        },
        reason: {
          type: 'string',
          default: 'Workflow delay',
          description: 'Reason for the delay (for logging and monitoring)',
        },
        maxDuration: {
          type: 'number',
          minimum: 1000,
          maximum: 86400000, // 24 hours
          default: 3600000, // 1 hour
          description: 'Maximum allowed delay duration in milliseconds',
        },
        interruptible: {
          type: 'boolean',
          default: true,
          description: 'Whether the delay can be interrupted by workflow cancellation',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        completed: {
          type: 'boolean',
          description: 'Whether the delay completed successfully',
        },
        requestedDuration: {
          type: 'number',
          description: 'Requested delay duration in milliseconds',
        },
        actualDuration: {
          type: 'number',
          description: 'Actual delay duration in milliseconds',
        },
        unit: {
          type: 'string',
          description: 'Time unit used for the delay',
        },
        reason: {
          type: 'string',
          description: 'Reason for the delay',
        },
        startTime: {
          type: 'number',
          description: 'Delay start timestamp',
        },
        endTime: {
          type: 'number',
          description: 'Delay end timestamp',
        },
        interrupted: {
          type: 'boolean',
          description: 'Whether the delay was interrupted',
        },
        metadata: {
          type: 'object',
          properties: {
            delayType: {
              type: 'string',
              enum: ['standard', 'interrupted'],
              description: 'Type of delay execution',
            },
            precision: {
              type: 'number',
              description: 'Delay timing precision percentage',
            },
            interruptionReason: {
              type: 'string',
              description: 'Reason for interruption (if applicable)',
            },
          },
        },
      },
    };
  }

  /**
   * Check if node is ready to execute
   */
  async isReady(context: ExecutionContext): Promise<boolean> {
    // Delay nodes are always ready to execute
    return true;
  }

  /**
   * Cleanup method - can be used to cancel ongoing delays
   */
  async cleanup(context: ExecutionContext): Promise<void> {
    // In a real implementation, this might cancel any ongoing delays
    // For now, just log the cleanup
    this.logger.debug(`Delay node cleanup called for execution: ${context.executionId}`);
  }
}
