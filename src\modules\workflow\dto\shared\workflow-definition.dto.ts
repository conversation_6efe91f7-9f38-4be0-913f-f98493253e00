import { 
  IsObject, 
  IsOptional, 
  ValidateNested, 
  IsArray, 
  IsString,
  MaxLength,
  IsNumber,
  IsBoolean 
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Shared DTOs for workflow definition management
 * Synced with BE-003 definition DTOs for consistency
 * Used by <PERSON><PERSON> Worker for execution and validation
 */

/**
 * Shared workflow node DTO - synced với BE-003
 */
export class SharedWorkflowNodeDto {
  @IsString()
  @MaxLength(255)
  id: string;

  @IsString()
  type: string;

  @IsString()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsObject()
  position: {
    x: number;
    y: number;
  };

  @IsOptional()
  @IsObject()
  size?: {
    width: number;
    height: number;
  };

  @IsOptional()
  @IsObject()
  inputs?: Record<string, any>;

  @IsOptional()
  @IsObject()
  outputs?: Record<string, any>;

  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Shared workflow edge DTO - synced với BE-003
 */
export class SharedWorkflowEdgeDto {
  @IsString()
  @MaxLength(255)
  id: string;

  @IsString()
  sourceNodeId: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  sourcePort?: string;

  @IsString()
  targetNodeId: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  targetPort?: string;

  @IsOptional()
  @IsString()
  edgeType?: string;

  @IsOptional()
  @IsObject()
  condition?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Shared workflow definition metadata DTO - synced với BE-003
 */
export class SharedWorkflowDefinitionMetadataDto {
  @IsOptional()
  @IsString()
  version?: string;

  @IsOptional()
  @IsString()
  schemaVersion?: string;

  @IsOptional()
  @IsObject()
  canvas?: Record<string, any>;

  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @IsOptional()
  @IsArray()
  triggers?: Array<Record<string, any>>;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsArray()
  tags?: string[];

  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;

  @IsOptional()
  @IsObject()
  author?: Record<string, any>;

  @IsOptional()
  @IsObject()
  lastModified?: Record<string, any>;
}

/**
 * Shared workflow definition DTO - synced với BE-003
 */
export class SharedWorkflowDefinitionDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SharedWorkflowNodeDto)
  nodes: SharedWorkflowNodeDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SharedWorkflowEdgeDto)
  edges?: SharedWorkflowEdgeDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => SharedWorkflowDefinitionMetadataDto)
  metadata?: SharedWorkflowDefinitionMetadataDto;
}

/**
 * Shared workflow DTO for execution - synced với BE-003
 */
export class SharedWorkflowDto {
  @IsString()
  id: string;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsString()
  @MaxLength(255)
  name: string;

  @IsBoolean()
  isActive: boolean;

  @IsNumber()
  createdAt: number;

  @IsNumber()
  updatedAt: number;

  @ValidateNested()
  @Type(() => SharedWorkflowDefinitionDto)
  definition: SharedWorkflowDefinitionDto;
}

/**
 * Execution context DTO for Worker processing
 */
export class WorkflowExecutionContextDto {
  @IsString()
  workflowId: string;

  @IsString()
  executionId: string;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsString()
  triggerType: string;

  @IsObject()
  triggerData: Record<string, any>;

  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Node execution context DTO for individual node processing
 */
export class NodeExecutionContextDto {
  @IsString()
  nodeId: string;

  @IsString()
  nodeType: string;

  @IsObject()
  inputs: Record<string, any>;

  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @IsOptional()
  @IsObject()
  previousResults?: Record<string, any>;

  @ValidateNested()
  @Type(() => WorkflowExecutionContextDto)
  workflowContext: WorkflowExecutionContextDto;
}

/**
 * Node execution result DTO
 */
export class NodeExecutionResultDto {
  @IsBoolean()
  success: boolean;

  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsObject()
  metadata?: {
    executionTime?: number;
    memoryUsage?: number;
    outputSize?: number;
    warnings?: string[];
  };

  @IsOptional()
  @IsArray()
  logs?: Array<{
    level: string;
    message: string;
    timestamp: number;
    data?: any;
  }>;
}

/**
 * Workflow execution result DTO
 */
export class WorkflowExecutionResultDto {
  @IsString()
  executionId: string;

  @IsString()
  workflowId: string;

  @IsString()
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

  @IsOptional()
  @IsObject()
  result?: Record<string, any>;

  @IsOptional()
  @IsString()
  error?: string;

  @IsNumber()
  startedAt: number;

  @IsOptional()
  @IsNumber()
  completedAt?: number;

  @IsOptional()
  @IsObject()
  metadata?: {
    totalNodes?: number;
    completedNodes?: number;
    failedNodes?: number;
    executionTime?: number;
    memoryUsage?: number;
  };

  @IsOptional()
  @IsArray()
  nodeResults?: Record<string, NodeExecutionResultDto>;
}
