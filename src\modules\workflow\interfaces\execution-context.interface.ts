import { LoggingService } from '../services/logging.service';
import { EventService } from '../services/event.service';

/**
 * Enhanced execution context with webhook data support
 * Provides runtime context for node execution in workflows
 */
export interface ExecutionContext {
  /**
   * ID của lần thực thi workflow hiện tại
   */
  executionId: string;

  /**
   * ID của workflow đang được thực thi
   */
  workflowId: string;

  /**
   * ID của user sở hữu workflow
   */
  userId: number;

  /**
   * Loại trigger kích hoạt workflow
   * Examples: 'webhook.facebook', 'webhook.zalo', 'manual', 'scheduled'
   */
  triggerType: string;

  /**
   * Dữ liệu trigger từ webhook hoặc manual trigger
   * Chứa payload từ webhook hoặc input data từ manual execution
   */
  triggerData: any;

  /**
   * Lấy output của một node đã thực thi
   * @param nodeId - ID của node
   * @returns Output data của node hoặc undefined nếu chưa thực thi
   */
  getNodeOutput(nodeId: string): any;

  /**
   * <PERSON><PERSON>u output của node sau khi thực thi
   * @param nodeId - ID của node
   * @param output - Output data của node
   */
  setNodeOutput(nodeId: string, output: any): void;

  /**
   * Lấy input data cho node hiện tại
   * @param nodeId - ID của node
   * @returns Input data đã được resolve từ previous nodes
   */
  getNodeInput(nodeId: string): any;

  /**
   * Kiểm tra xem node đã được thực thi chưa
   * @param nodeId - ID của node
   * @returns true nếu node đã thực thi
   */
  isNodeExecuted(nodeId: string): boolean;

  /**
   * Lấy danh sách nodes đã thực thi
   * @returns Array of executed node IDs
   */
  getExecutedNodes(): string[];

  /**
   * Service để ghi log execution
   */
  logService: LoggingService;

  /**
   * Service để ghi log execution (alias for compatibility)
   */
  loggingService: LoggingService;

  /**
   * Service để publish events (SSE, webhooks)
   */
  eventService: EventService;

  /**
   * Thời gian bắt đầu execution (timestamp)
   */
  startTime: number;

  /**
   * Variables trong workflow execution
   */
  variables?: Record<string, any>;

  /**
   * Kết quả của các nodes đã thực thi
   */
  nodeResults?: Record<string, any>;

  /**
   * Metadata bổ sung cho execution context
   */
  metadata?: Record<string, any>;

  /**
   * Options cho execution
   */
  options?: {
    /**
     * Có enable SSE streaming không
     */
    enableSSE?: boolean;

    /**
     * Timeout cho execution (milliseconds)
     */
    timeout?: number;

    /**
     * Có retry khi failed không
     */
    enableRetry?: boolean;

    /**
     * Số lần retry tối đa
     */
    maxRetries?: number;
  };
}

/**
 * Context cho việc test single node
 */
export interface NodeTestContext extends Omit<ExecutionContext, 'workflowId' | 'executionId'> {
  /**
   * ID của node đang test
   */
  nodeId: string;

  /**
   * Input data cho node test
   */
  testInput: any;

  /**
   * Mock data cho dependencies
   */
  mockData?: Record<string, any>;
}

/**
 * Result của node execution
 */
export interface NodeExecutionResult {
  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Output data của node
   */
  output?: any;

  /**
   * Error message nếu failed
   */
  error?: string;

  /**
   * Error details cho debugging
   */
  errorDetails?: any;

  /**
   * Execution metadata
   */
  metadata?: {
    /**
     * Node type for tracking
     */
    nodeType?: string;

    /**
     * Thời gian thực thi (milliseconds)
     */
    executionTime?: number;

    /**
     * Memory usage
     */
    memoryUsage?: number;

    /**
     * Timestamp khi execution bắt đầu
     */
    timestamp?: number;

    /**
     * Additional metrics
     */
    metrics?: Record<string, any>;
  };
}

/**
 * Kết quả thực thi workflow
 */
export interface WorkflowExecutionResult {
  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Kết quả output của workflow
   */
  output?: any;

  /**
   * Lỗi nếu có
   */
  error?: string;

  /**
   * Chi tiết lỗi
   */
  errorDetails?: any;

  /**
   * Metadata của execution
   */
  metadata?: {
    /**
     * Thời gian thực thi (milliseconds)
     */
    executionTime?: number;

    /**
     * Số lượng nodes đã thực thi
     */
    executedNodes?: number;

    /**
     * Tổng số nodes trong workflow
     */
    totalNodes?: number;

    /**
     * Số nodes bị skip
     */
    skippedNodes?: number;

    /**
     * Số nodes bị failed
     */
    failedNodes?: number;

    /**
     * Additional metrics
     */
    metrics?: Record<string, any>;
  };
}
