import { Injectable, Logger } from '@nestjs/common';
import { NodeExecutor } from '../../interfaces/node-executor.interface';
import { ExecutionContext, NodeExecutionResult } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';
import { InputResolver } from './input-resolver';
import { ValidationHelper } from './validation-helper';
import { ExecutionLogger } from './execution-logger';

/**
 * Abstract base class cho tất cả node executors
 * Cung cấp common functionality và enforces contract
 * Following existing processor patterns từ BE Worker
 */
@Injectable()
export abstract class BaseNodeExecutor implements NodeExecutor {
  protected readonly logger = new Logger(this.constructor.name);
  
  protected readonly inputResolver = new InputResolver();
  protected readonly validationHelper = new ValidationHelper();
  protected readonly executionLogger = new ExecutionLogger();

  /**
   * Loại node (unique identifier) - must be implemented by subclasses
   */
  abstract readonly type: string;

  /**
   * Category của node - must be implemented by subclasses
   */
  abstract readonly category: NodeCategory;

  /**
   * Phiên bản của node executor
   */
  readonly version: string = '1.0.0';

  /**
   * Tên hiển thị của node - must be implemented by subclasses
   */
  abstract readonly name: string;

  /**
   * Mô tả chức năng của node - must be implemented by subclasses
   */
  abstract readonly description: string;

  /**
   * Main execution method - must be implemented by subclasses
   * @param inputs - Raw input data from workflow definition
   * @param context - Execution context
   * @returns Promise<NodeExecutionResult>
   */
  async execute(inputs: any, context: ExecutionContext): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Log execution start
      await this.executionLogger.logStart(this.type, inputs, context);

      // Resolve input expressions (e.g., {{node1.output}})
      const resolvedInputs = await this.inputResolver.resolveInputs(inputs, context);

      // Validate resolved inputs
      this.validateInputs(resolvedInputs);

      // Check if node is ready to execute
      const isReady = await this.isReady(context);
      if (!isReady) {
        throw new Error(`Node ${this.type} is not ready for execution`);
      }

      // Execute the actual node logic
      const result = await this.executeNode(resolvedInputs, context);

      // Calculate execution time
      const executionTime = Date.now() - startTime;

      // Log successful execution
      await this.executionLogger.logSuccess(this.type, result, executionTime, context);

      return {
        success: true,
        output: result,
        metadata: {
          executionTime,
          nodeType: this.type,
          timestamp: Date.now(),
        },
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Log execution error
      await this.executionLogger.logError(this.type, error, executionTime, context);

      return {
        success: false,
        error: error.message,
        errorDetails: {
          stack: error.stack,
          nodeType: this.type,
          inputs: inputs,
        },
        metadata: {
          executionTime,
          nodeType: this.type,
          timestamp: Date.now(),
        },
      };
    }
  }

  /**
   * Abstract method for actual node execution logic
   * Must be implemented by subclasses
   * @param inputs - Resolved and validated inputs
   * @param context - Execution context
   * @returns Promise<any> - Node output
   */
  protected abstract executeNode(inputs: any, context: ExecutionContext): Promise<any>;

  /**
   * Validate input data trước khi thực thi
   * Uses JSON Schema validation
   * @param inputs - Input data cần validate
   * @returns true nếu valid, throw error nếu invalid
   */
  validateInputs(inputs: any): boolean {
    const schema = this.getInputSchema();
    return this.validationHelper.validateAgainstSchema(inputs, schema);
  }

  /**
   * Abstract method to get JSON Schema cho input validation
   * Must be implemented by subclasses
   * @returns JSON Schema object
   */
  abstract getInputSchema(): Record<string, any>;

  /**
   * Abstract method to get JSON Schema cho output structure
   * Must be implemented by subclasses
   * @returns JSON Schema object
   */
  abstract getOutputSchema(): Record<string, any>;

  /**
   * Kiểm tra node có sẵn sàng thực thi không
   * Default implementation returns true
   * Can be overridden by subclasses for specific checks
   * @param context - Execution context
   * @returns true nếu ready, false nếu chưa ready
   */
  async isReady(context: ExecutionContext): Promise<boolean> {
    return true;
  }

  /**
   * Cleanup resources sau khi thực thi (optional)
   * Default implementation does nothing
   * Can be overridden by subclasses
   * @param context - Execution context
   */
  async cleanup(context: ExecutionContext): Promise<void> {
    // Default: no cleanup needed
  }

  /**
   * Helper method to get node configuration from context
   * @param context - Execution context
   * @returns Node configuration object
   */
  protected getNodeConfig(context: ExecutionContext): any {
    // This would be implemented based on how node configs are stored
    // For now, return empty object
    return {};
  }

  /**
   * Helper method to get user credentials for external services
   * @param context - Execution context
   * @param service - Service name (e.g., 'google', 'facebook')
   * @returns User credentials object
   */
  protected async getUserCredentials(context: ExecutionContext, service: string): Promise<any> {
    // This would be implemented to fetch user credentials from database
    // For now, return empty object
    return {};
  }

  /**
   * Helper method to make HTTP requests with proper error handling
   * @param url - Request URL
   * @param options - Request options
   * @returns Response data
   */
  protected async makeHttpRequest(url: string, options: any = {}): Promise<any> {
    // This would be implemented using axios or similar HTTP client
    // For now, throw not implemented error
    throw new Error('HTTP request functionality not implemented yet');
  }

  /**
   * Helper method to emit events during execution
   * @param eventType - Type of event
   * @param data - Event data
   * @param context - Execution context
   */
  protected async emitEvent(eventType: string, data: any, context: ExecutionContext): Promise<void> {
    // This would be implemented to emit events via EventService
    // For now, just log the event
    this.logger.debug(`Event emitted: ${eventType}`, { data, executionId: context.executionId });
  }
}
