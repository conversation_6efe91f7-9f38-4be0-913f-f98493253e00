import { Injectable, Logger } from '@nestjs/common';
import { StateGraph, START, END } from '@langchain/langgraph';
import { Workflow, WorkflowNode, WorkflowEdge } from '../entities';
import { NodeExecutorRegistry } from '../executors/registry';
import { WorkflowState, WorkflowGraphConfig } from './interfaces/langgraph.interface';

/**
 * Service for building LangGraph workflow graphs from workflow definitions
 * Converts RedAI workflow definitions into executable LangGraph state graphs
 */
@Injectable()
export class WorkflowGraphBuilderService {
  private readonly logger = new Logger(WorkflowGraphBuilderService.name);

  constructor(private readonly nodeExecutorRegistry: NodeExecutorRegistry) {}

  /**
   * Build LangGraph state graph from workflow definition
   * @param workflow - Workflow entity with nodes and edges
   * @param config - Graph configuration options
   * @returns Built StateGraph instance
   */
  async buildGraph(
    workflow: Workflow,
    config: WorkflowGraphConfig = {}
  ): Promise<StateGraph<WorkflowState, Partial<WorkflowState>, string>> {
    this.logger.debug(`Building graph for workflow: ${workflow.id}`);

    try {
      // Create state graph with workflow state schema
      const graph = new StateGraph<WorkflowState, Partial<WorkflowState>, string>({
        channels: this.buildStateChannels(workflow, config),
      });

      // Add nodes to the graph
      await this.addNodesToGraph(graph, workflow.nodes || [], config);

      // Add edges to the graph
      this.addEdgesToGraph(graph, workflow.edges || [], workflow.nodes || [], config);

      // Set entry and exit points
      this.setGraphEntryAndExit(graph, workflow.nodes || [], config);

      this.logger.debug(`Graph built successfully for workflow: ${workflow.id}`);
      return graph;

    } catch (error) {
      this.logger.error(`Failed to build graph for workflow ${workflow.id}:`, error);
      throw new Error(`Graph building failed: ${error.message}`);
    }
  }

  /**
   * Build state channels for the workflow
   * @private
   */
  private buildStateChannels(workflow: Workflow, config: WorkflowGraphConfig): Record<string, any> {
    const channels: Record<string, any> = {
      // Core workflow state
      executionId: {
        value: () => '',
        default: () => '',
      },
      workflowId: {
        value: () => workflow.id,
        default: () => workflow.id,
      },
      userId: {
        value: () => 0,
        default: () => 0,
      },
      status: {
        value: () => 'pending',
        default: () => 'pending',
      },
      startTime: {
        value: () => Date.now(),
        default: () => Date.now(),
      },
      
      // Node execution state
      currentNode: {
        value: () => null,
        default: () => null,
      },
      nodeResults: {
        value: () => ({}),
        default: () => ({}),
      },
      variables: {
        value: () => ({}),
        default: () => ({}),
      },
      
      // Execution flow
      nextNodes: {
        value: () => [],
        default: () => [],
      },
      completedNodes: {
        value: () => [],
        default: () => [],
      },
      failedNodes: {
        value: () => [],
        default: () => [],
      },
      
      // Error handling
      errors: {
        value: () => [],
        default: () => [],
      },
      lastError: {
        value: () => null,
        default: () => null,
      },
      
      // Trigger data
      triggerData: {
        value: () => ({}),
        default: () => ({}),
      },
      triggerType: {
        value: () => 'manual',
        default: () => 'manual',
      },
      
      // Messages for LangGraph compatibility
      messages: {
        value: () => [],
        default: () => [],
      },
    };

    // Add custom channels from config
    if (config.customChannels) {
      Object.assign(channels, config.customChannels);
    }

    return channels;
  }

  /**
   * Add workflow nodes to the graph
   * @private
   */
  private async addNodesToGraph(
    graph: StateGraph<WorkflowState, Partial<WorkflowState>, string>,
    nodes: WorkflowNode[],
    config: WorkflowGraphConfig
  ): Promise<void> {
    for (const node of nodes) {
      try {
        // Get node executor
        const executor = await this.nodeExecutorRegistry.getExecutor(node.nodeType);
        if (!executor) {
          throw new Error(`No executor found for node type: ${node.nodeType}`);
        }

        // Create node function
        const nodeFunction = this.createNodeFunction(node, executor, config);

        // Add node to graph
        graph.addNode(node.id, nodeFunction as any);

        this.logger.debug(`Added node to graph: ${node.id} (${node.nodeType})`);

      } catch (error) {
        this.logger.error(`Failed to add node ${node.id} to graph:`, error);
        throw error;
      }
    }
  }

  /**
   * Create node execution function for LangGraph
   * @private
   */
  private createNodeFunction(
    node: WorkflowNode,
    executor: any,
    config: WorkflowGraphConfig
  ) {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      this.logger.debug(`Executing node: ${node.id} (${node.nodeType})`);

      try {
        // Update current node in state
        const updatedState: Partial<WorkflowState> = {
          currentNode: node.id,
          completedNodes: [...(state.completedNodes || [])],
          failedNodes: [...(state.failedNodes || [])],
          nodeResults: { ...(state.nodeResults || {}) },
          variables: { ...(state.variables || {}) },
          errors: [...(state.errors || [])],
        };

        // Create execution context
        const executionContext = {
          executionId: state.executionId,
          workflowId: state.workflowId,
          userId: state.userId,
          triggerData: state.triggerData,
          triggerType: state.triggerType,
          startTime: state.startTime,
          variables: state.variables,
          nodeResults: state.nodeResults,
          isTest: config.isTest || false,
        };

        // Execute node
        const result = await executor.execute(node.config || {}, executionContext);

        if (result.success) {
          // Node executed successfully
          if (!updatedState.nodeResults) {
            updatedState.nodeResults = {};
          }
          if (!updatedState.completedNodes) {
            updatedState.completedNodes = [];
          }
          updatedState.nodeResults[node.id] = result.output;
          updatedState.completedNodes.push(node.id);
          
          // Update variables if node modified them
          if (executionContext.variables) {
            updatedState.variables = executionContext.variables;
          }

          this.logger.debug(`Node executed successfully: ${node.id}`);

        } else {
          // Node execution failed
          const error = {
            nodeId: node.id,
            nodeType: node.nodeType,
            error: result.error,
            timestamp: Date.now(),
          };

          if (!updatedState.failedNodes) {
            updatedState.failedNodes = [];
          }
          if (!updatedState.errors) {
            updatedState.errors = [];
          }
          updatedState.failedNodes.push(node.id);
          updatedState.errors.push(error);
          updatedState.lastError = error;

          this.logger.error(`Node execution failed: ${node.id}`, result.error);

          // Handle error based on configuration
          if (config.stopOnError !== false) {
            updatedState.status = 'failed';
          }
        }

        return updatedState;

      } catch (error) {
        this.logger.error(`Node execution error: ${node.id}`, error);

        const errorInfo = {
          nodeId: node.id,
          nodeType: node.nodeType,
          error: error.message,
          timestamp: Date.now(),
        };

        return {
          currentNode: node.id,
          failedNodes: [...(state.failedNodes || []), node.id],
          errors: [...(state.errors || []), errorInfo],
          lastError: errorInfo,
          status: config.stopOnError !== false ? 'failed' : state.status,
        };
      }
    };
  }

  /**
   * Add workflow edges to the graph
   * @private
   */
  private addEdgesToGraph(
    graph: StateGraph<WorkflowState, Partial<WorkflowState>, string>,
    edges: WorkflowEdge[],
    nodes: WorkflowNode[],
    config: WorkflowGraphConfig // eslint-disable-line @typescript-eslint/no-unused-vars
  ): void {
    // Group edges by source node
    const edgesBySource = new Map<string, WorkflowEdge[]>();
    edges.forEach(edge => {
      if (!edgesBySource.has(edge.sourceNodeId)) {
        edgesBySource.set(edge.sourceNodeId, []);
      }
      edgesBySource.get(edge.sourceNodeId)!.push(edge);
    });

    // Add edges to graph
    edgesBySource.forEach((nodeEdges, sourceNodeId) => {
      if (nodeEdges.length === 1) {
        // Simple edge
        const edge = nodeEdges[0];
        graph.addEdge(sourceNodeId as any, edge.targetNodeId as any);
        this.logger.debug(`Added edge: ${sourceNodeId} -> ${edge.targetNodeId}`);

      } else {
        // Conditional edges
        const conditionalEdges = this.createConditionalEdges(nodeEdges, nodes);
        graph.addConditionalEdges(sourceNodeId as any, conditionalEdges.router, conditionalEdges.mapping as any);
        this.logger.debug(`Added conditional edges from: ${sourceNodeId}`);
      }
    });
  }

  /**
   * Create conditional edges for nodes with multiple outputs
   * @private
   */
  private createConditionalEdges(edges: WorkflowEdge[], nodes: WorkflowNode[]) { // eslint-disable-line @typescript-eslint/no-unused-vars
    const mapping: Record<string, string> = {};
    
    edges.forEach(edge => {
      const conditionKey = edge.condition ? JSON.stringify(edge.condition) : 'default';
      mapping[conditionKey] = edge.targetNodeId;
    });

    const router = (state: WorkflowState): string => {
      // Default routing logic - can be enhanced based on node results
      const currentNodeResult = state.nodeResults?.[state.currentNode || ''];
      
      // Check for condition-based routing
      for (const edge of edges) {
        if (edge.condition) {
          try {
            // Simple condition evaluation
            if (this.evaluateEdgeCondition(edge.condition, currentNodeResult, state)) {
              return JSON.stringify(edge.condition);
            }
          } catch (error) {
            this.logger.warn(`Edge condition evaluation failed: ${edge.condition}`, error);
          }
        }
      }

      // Default to first edge or 'default'
      return edges[0]?.condition ? JSON.stringify(edges[0].condition) : 'default';
    };

    return { router, mapping };
  }

  /**
   * Evaluate edge condition
   * @private
   */
  private evaluateEdgeCondition(condition: Record<string, any> | string, nodeResult: any, state: WorkflowState): boolean { // eslint-disable-line @typescript-eslint/no-unused-vars
    try {
      // Convert condition to string if it's an object
      const conditionStr = typeof condition === 'string' ? condition : JSON.stringify(condition);

      // Simple condition evaluation - can be enhanced
      if (conditionStr === 'success' && nodeResult) {
        return true;
      }
      if (conditionStr === 'error' && !nodeResult) {
        return true;
      }
      if (conditionStr === 'default') {
        return true;
      }

      // For object conditions, implement more complex logic here
      if (typeof condition === 'object' && condition !== null) {
        // Example: { field: 'status', operator: 'equals', value: 'completed' }
        // This is a placeholder for more sophisticated condition evaluation
        return true;
      }

      return false;
    } catch (error) {
      this.logger.warn(`Condition evaluation failed: ${condition}`, error);
      return false;
    }
  }

  /**
   * Set graph entry and exit points
   * @private
   */
  private setGraphEntryAndExit(
    graph: StateGraph<WorkflowState, Partial<WorkflowState>, string>,
    nodes: WorkflowNode[],
    config: WorkflowGraphConfig
  ): void {
    // Find entry nodes (nodes with no incoming edges or trigger nodes)
    const entryNodes = nodes.filter(node => 
      node.nodeType.includes('trigger') || 
      config.entryNodeId === node.id
    );

    // Find exit nodes (nodes with no outgoing edges or output nodes)
    const exitNodes = nodes.filter(node => 
      node.nodeType.includes('output') || 
      config.exitNodeId === node.id
    );

    // Set entry point
    if (entryNodes.length > 0) {
      graph.addEdge(START, entryNodes[0].id as any);
      this.logger.debug(`Set entry point: ${entryNodes[0].id}`);
    } else if (nodes.length > 0) {
      graph.addEdge(START, nodes[0].id as any);
      this.logger.debug(`Set default entry point: ${nodes[0].id}`);
    }

    // Set exit points
    if (exitNodes.length > 0) {
      exitNodes.forEach(node => {
        graph.addEdge(node.id as any, END);
        this.logger.debug(`Set exit point: ${node.id}`);
      });
    } else if (nodes.length > 0) {
      // Find nodes with no outgoing edges
      const nodesWithoutOutgoing = nodes.filter(node => 
        !nodes.some(otherNode => otherNode.id !== node.id)
      );
      
      if (nodesWithoutOutgoing.length > 0) {
        nodesWithoutOutgoing.forEach(node => {
          graph.addEdge(node.id as any, END);
          this.logger.debug(`Set default exit point: ${node.id}`);
        });
      }
    }
  }

  /**
   * Validate workflow definition for graph building
   * @param workflow - Workflow to validate
   * @returns Validation result
   */
  validateWorkflowForGraph(workflow: Workflow): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if workflow has nodes
    if (!workflow.nodes || workflow.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    // Check for entry nodes
    const entryNodes = workflow.nodes?.filter(node => 
      node.nodeType.includes('trigger')
    ) || [];
    
    if (entryNodes.length === 0) {
      warnings.push('No trigger nodes found - first node will be used as entry point');
    }

    // Check for exit nodes
    const exitNodes = workflow.nodes?.filter(node => 
      node.nodeType.includes('output')
    ) || [];
    
    if (exitNodes.length === 0) {
      warnings.push('No output nodes found - nodes without outgoing edges will be exit points');
    }

    // Check for orphaned nodes
    const nodeIds = new Set(workflow.nodes?.map(n => n.id) || []);
    const connectedNodes = new Set();
    
    workflow.edges?.forEach(edge => {
      connectedNodes.add(edge.sourceNodeId);
      connectedNodes.add(edge.targetNodeId);
    });

    const orphanedNodes = Array.from(nodeIds).filter(id => !connectedNodes.has(id));
    if (orphanedNodes.length > 0) {
      warnings.push(`Orphaned nodes found: ${orphanedNodes.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
