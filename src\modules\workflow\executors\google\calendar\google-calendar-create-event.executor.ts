import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleCalendarService } from '../../../../../shared/services/google/calendar/google-calendar.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Calendar Create Event node
 */
export interface GoogleCalendarCreateEventInput {

  /**
   * ID của calendar (mặc định: 'primary')
   */
  calendarId?: string;

  /**
   * Tiêu đề event
   */
  summary: string;

  /**
   * <PERSON>ô tả event
   */
  description?: string;

  /**
   * Đ<PERSON>a điểm
   */
  location?: string;

  /**
   * Thời gian bắt đầu (ISO string)
   */
  startDateTime: string;

  /**
   * Thời gian kết thúc (ISO string)
   */
  endDateTime: string;

  /**
   * Timezone (mặc định: 'Asia/Ho_Chi_Minh')
   */
  timeZone?: string;

  /**
   * Danh sách email người tham dự
   */
  attendees?: string[];

  /**
   * Có gửi thông báo không
   */
  sendNotifications?: boolean;

  /**
   * Có phải event cả ngày không
   */
  allDay?: boolean;

  /**
   * Màu sắc event (1-11)
   */
  colorId?: string;

  /**
   * Visibility của event
   */
  visibility?: 'default' | 'public' | 'private' | 'confidential';

  /**
   * Recurrence rules (RRULE)
   */
  recurrence?: string[];
}

/**
 * Output schema cho Google Calendar Create Event node
 */
export interface GoogleCalendarCreateEventOutput {
  /**
   * ID của event đã tạo
   */
  eventId: string;

  /**
   * Link HTML để xem event
   */
  htmlLink: string;

  /**
   * Link để join meeting (nếu có)
   */
  meetingLink?: string;

  /**
   * Thông tin event đã tạo
   */
  event: {
    id: string;
    summary: string;
    description?: string;
    location?: string;
    startDateTime: string;
    endDateTime: string;
    attendees?: Array<{
      email: string;
      responseStatus: string;
    }>;
    creator: {
      email: string;
      displayName?: string;
    };
    organizer: {
      email: string;
      displayName?: string;
    };
  };

  /**
   * Metadata
   */
  metadata: {
    calendarId: string;
    created: string;
    updated: string;
  };
}

/**
 * Executor để tạo event trong Google Calendar
 */
@Injectable()
export class GoogleCalendarCreateEventExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.calendar.create_event';
  readonly category = NodeCategory.GOOGLE_CALENDAR;
  readonly name = 'Create Calendar Event';
  readonly description = 'Tạo event mới trong Google Calendar';

  constructor(
    integrationService: IntegrationService,
    private readonly calendarService: GoogleCalendarService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        calendarId: { type: 'string' },
        summary: { type: 'string' },
        description: { type: 'string' },
        location: { type: 'string' },
        startDateTime: { type: 'string' },
        endDateTime: { type: 'string' },
        timeZone: { type: 'string' },
        attendees: { type: 'array', items: { type: 'string' } },
        sendNotifications: { type: 'boolean' },
        allDay: { type: 'boolean' },
        colorId: { type: 'string' },
        visibility: { type: 'string', enum: ['default', 'public', 'private', 'confidential'] },
        recurrence: { type: 'array', items: { type: 'string' } },
      },
      required: ['summary', 'startDateTime', 'endDateTime'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        eventId: { type: 'string' },
        htmlLink: { type: 'string' },
        meetingLink: { type: 'string' },
        event: { type: 'object' },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node tạo event
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Creating calendar event: ${inputs.summary}`);

      // Validate required inputs
      if (!inputs.summary) {
        throw new Error('Event summary is required');
      }

      if (!inputs.startDateTime || !inputs.endDateTime) {
        throw new Error('Start and end date time are required');
      }

      // Prepare event data
      const calendarId = inputs.calendarId || 'primary';
      const timeZone = inputs.timeZone || 'Asia/Ho_Chi_Minh';

      const eventData = {
        summary: inputs.summary,
        description: inputs.description,
        location: inputs.location,
        start: inputs.allDay ? {
          date: inputs.startDateTime.split('T')[0],
          timeZone,
        } : {
          dateTime: inputs.startDateTime,
          timeZone,
        },
        end: inputs.allDay ? {
          date: inputs.endDateTime.split('T')[0],
          timeZone,
        } : {
          dateTime: inputs.endDateTime,
          timeZone,
        },
        attendees: inputs.attendees?.map(email => ({ email })),
        colorId: inputs.colorId,
        visibility: inputs.visibility || 'default',
        recurrence: inputs.recurrence,
      };

      // Prepare request
      const createRequest = {
        calendarId,
        event: eventData,
        sendNotifications: inputs.sendNotifications,
      };

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Create event
      const createdEvent = await this.calendarService.createEvent(
        accessToken,
        createRequest,
      );

      // Prepare output
      const output: GoogleCalendarCreateEventOutput = {
        eventId: createdEvent.id || '',
        htmlLink: createdEvent.htmlLink || '',
        meetingLink: createdEvent.hangoutLink,
        event: {
          id: createdEvent.id || '',
          summary: createdEvent.summary || '',
          description: createdEvent.description,
          location: createdEvent.location,
          startDateTime: createdEvent.start?.dateTime || createdEvent.start?.date || '',
          endDateTime: createdEvent.end?.dateTime || createdEvent.end?.date || '',
          attendees: createdEvent.attendees?.map(attendee => ({
            email: attendee.email || '',
            responseStatus: attendee.responseStatus || 'needsAction',
          })),
          creator: {
            email: createdEvent.creator?.email || '',
            displayName: createdEvent.creator?.displayName,
          },
          organizer: {
            email: createdEvent.organizer?.email || '',
            displayName: createdEvent.organizer?.displayName,
          },
        },
        metadata: {
          calendarId,
          created: createdEvent.created || new Date().toISOString(),
          updated: createdEvent.updated || new Date().toISOString(),
        },
      };

      this.logger.log(`Calendar event created successfully: ${output.eventId}`);

      return output;
    });
  }
}
