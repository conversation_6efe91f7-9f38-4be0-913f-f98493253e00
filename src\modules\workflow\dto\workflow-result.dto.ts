import { IsString, <PERSON><PERSON><PERSON>ber, IsOptional, IsBoolean, IsObject, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho workflow execution statistics
 */
export class WorkflowExecutionStatsDto {
  @IsNumber()
  totalNodes: number;

  @IsNumber()
  successfulNodes: number;

  @IsNumber()
  failedNodes: number;

  @IsNumber()
  totalExecutionTime: number;

  @IsOptional()
  @IsNumber()
  peakMemoryUsage?: number;
}

/**
 * DTO cho workflow execution result
 * Trả về từ Worker cho BE App
 */
export class WorkflowExecutionResultDto {
  @IsString()
  executionId: string;

  @IsBoolean()
  success: boolean;

  @IsEnum(['completed', 'failed', 'paused'])
  status: 'completed' | 'failed' | 'paused';

  @IsNumber()
  startedAt: number;

  @IsNumber()
  finishedAt: number;

  @IsOptional()
  @IsObject()
  output?: any;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsObject()
  errorDetails?: any;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionStatsDto)
  stats?: WorkflowExecutionStatsDto;
}

/**
 * DTO cho node test result metadata
 */
export class NodeTestMetadataDto {
  @IsNumber()
  executionTime: number;

  @IsOptional()
  @IsNumber()
  memoryUsage?: number;

  @IsObject()
  inputData: any;

  @IsOptional()
  @IsObject()
  validation?: {
    inputValid: boolean;
    outputValid: boolean;
    errors?: string[];
  };
}

/**
 * DTO cho node test result
 * Trả về từ Worker cho BE App
 */
export class NodeTestResultDto {
  @IsString()
  nodeId: string;

  @IsBoolean()
  success: boolean;

  @IsOptional()
  @IsObject()
  output?: any;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsObject()
  errorDetails?: any;

  @IsOptional()
  @ValidateNested()
  @Type(() => NodeTestMetadataDto)
  metadata?: NodeTestMetadataDto;
}

/**
 * DTO cho node execution result
 */
export class NodeExecutionResultDto {
  @IsBoolean()
  success: boolean;

  @IsOptional()
  @IsObject()
  output?: any;

  @IsOptional()
  @IsString()
  error?: string;

  @IsOptional()
  @IsObject()
  errorDetails?: any;

  @IsOptional()
  @IsObject()
  metadata?: {
    executionTime?: number;
    memoryUsage?: number;
    metrics?: Record<string, any>;
  };
}

/**
 * DTO cho workflow event (SSE)
 */
export class WorkflowEventDto {
  @IsString()
  executionId: string;

  @IsString()
  eventType: string;

  @IsNumber()
  timestamp: number;

  @IsObject()
  data: any;

  @IsOptional()
  @IsString()
  nodeId?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho workflow status update
 */
export class WorkflowStatusUpdateDto {
  @IsString()
  executionId: string;

  @IsEnum(['queued', 'running', 'completed', 'failed', 'paused'])
  status: 'queued' | 'running' | 'completed' | 'failed' | 'paused';

  @IsOptional()
  @IsString()
  currentNode?: string;

  @IsOptional()
  @IsNumber()
  progress?: number; // 0-100

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
