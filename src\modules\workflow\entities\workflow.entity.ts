import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Unique,
  BeforeUpdate,
  OneToMany
} from 'typeorm';
import { WorkflowNode } from './workflow-node.entity';
import { WorkflowEdge } from './workflow-edge.entity';

/**
 * Entity đại diện cho bảng workflows trong cơ sở dữ liệu
 * Bảng chính chứa định nghĩa của các workflow
 * Synced with BE App for Worker execution
 */
@Entity('workflows')
@Unique(['userId', 'name'])
export class Workflow {
  /**
   * ID định danh duy nhất cho một workflow
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Khóa ngoại, liên kết đến người dùng sở hữu workflow
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * <PERSON>h<PERSON><PERSON> ngoạ<PERSON>, liên kết đến nhân viên chịu trách nhiệm cho workflow
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId: number | null;

  /**
   * Tên của workflow, phải là duy nhất cho mỗi người dùng
   */
  @Column({ type: 'varchar', length: 255 })
  name: string;

  /**
   * Trạng thái kích hoạt của workflow (true = đang chạy, false = đã tắt)
   */
  @Column({ name: 'is_active', type: 'boolean', default: false, nullable: false })
  isActive: boolean;

  /**
   * Thời gian tạo workflow, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật workflow lần cuối, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  /**
   * Lược đồ Workflow Tổng thể - lưu trữ định nghĩa cấu trúc workflow
   */
  @Column({
    name: 'definition',
    type: 'jsonb',
    default: () => "'{}'::jsonb",
    nullable: false
  })
  definition: Record<string, any>;

  /**
   * Relationship to workflow nodes
   */
  @OneToMany(() => WorkflowNode, (node) => node.workflowId)
  nodes?: WorkflowNode[];

  /**
   * Relationship to workflow edges
   */
  @OneToMany(() => WorkflowEdge, (edge) => edge.workflowId)
  edges?: WorkflowEdge[];

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }
}
