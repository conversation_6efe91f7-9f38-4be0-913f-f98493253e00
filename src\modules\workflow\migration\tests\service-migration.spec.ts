import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ServiceMigrationUtility } from '../service-migration.utility';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('ServiceMigrationUtility', () => {
  let service: ServiceMigrationUtility;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ServiceMigrationUtility],
    }).compile();

    service = module.get<ServiceMigrationUtility>(ServiceMigrationUtility);

    // Suppress console logs during testing
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyzeExistingServices', () => {
    it('should analyze Google services correctly', async () => {
      // Mock file system structure
      mockFs.existsSync.mockImplementation((path: any) => {
        return path.includes('google');
      });

      mockFs.readdirSync.mockImplementation((path: any, options?: any) => {
        if (path.includes('google')) {
          return [
            { name: 'ads', isDirectory: () => true },
            { name: 'analytics', isDirectory: () => true },
            { name: 'google-storage.service.ts', isDirectory: () => false },
          ] as any;
        }
        return [];
      });

      mockFs.statSync.mockReturnValue({
        isFile: () => true,
        isDirectory: () => false,
      } as any);

      mockFs.readFileSync.mockReturnValue(`
        import { Injectable } from '@nestjs/common';
        
        @Injectable()
        export class GoogleStorageService {
          async uploadFile() {}
          async downloadFile() {}
        }
      `);

      const result = await service.analyzeExistingServices('/mock/be-app');

      expect(result).toBeDefined();
      expect(result.totalServices).toBeGreaterThan(0);
      expect(result.servicesByCategory.google).toBeDefined();
      expect(result.servicesByCategory.google.services).toHaveLength(3);
      expect(result.migrationCandidates).toBeDefined();
      expect(result.complexityScore).toBeGreaterThan(0);
    });

    it('should handle missing directories gracefully', async () => {
      mockFs.existsSync.mockReturnValue(false);

      const result = await service.analyzeExistingServices('/mock/be-app');

      expect(result).toBeDefined();
      expect(result.totalServices).toBe(0);
      expect(result.servicesByCategory.google.services).toHaveLength(0);
    });

    it('should handle file system errors', async () => {
      mockFs.existsSync.mockImplementation(() => {
        throw new Error('File system error');
      });

      await expect(service.analyzeExistingServices('/mock/be-app')).rejects.toThrow();
    });
  });

  describe('generateMigrationPlan', () => {
    const mockServices = [
      {
        name: 'ads',
        path: '/mock/ads',
        type: 'directory' as const,
        complexity: 8,
        dependencies: ['@google-cloud/ads'],
        migrationPriority: 'high' as const,
      },
      {
        name: 'analytics',
        path: '/mock/analytics',
        type: 'directory' as const,
        complexity: 6,
        dependencies: ['@google-analytics/data'],
        migrationPriority: 'medium' as const,
      },
      {
        name: 'storage',
        path: '/mock/storage',
        type: 'file' as const,
        complexity: 4,
        dependencies: ['@google-cloud/storage'],
        migrationPriority: 'low' as const,
      },
    ];

    it('should generate comprehensive migration plan', () => {
      const plan = service.generateMigrationPlan('google', mockServices);

      expect(plan).toBeDefined();
      expect(plan.category).toBe('google');
      expect(plan.totalServices).toBe(3);
      expect(plan.phases).toHaveLength(5); // Infrastructure + 3 priority phases + Testing
      expect(plan.estimatedHours).toBeGreaterThan(0);
      expect(plan.dependencies).toBeDefined();
      expect(plan.risks).toBeDefined();
      expect(plan.successCriteria).toBeDefined();
    });

    it('should prioritize services correctly', () => {
      const plan = service.generateMigrationPlan('google', mockServices);

      const highPriorityPhase = plan.phases.find(p => p.name.includes('High Priority'));
      const mediumPriorityPhase = plan.phases.find(p => p.name.includes('Medium Priority'));
      const lowPriorityPhase = plan.phases.find(p => p.name.includes('Low Priority'));

      expect(highPriorityPhase).toBeDefined();
      expect(mediumPriorityPhase).toBeDefined();
      expect(lowPriorityPhase).toBeDefined();

      expect(highPriorityPhase!.tasks).toContain('Migrate ads service');
      expect(mediumPriorityPhase!.tasks).toContain('Migrate analytics service');
      expect(lowPriorityPhase!.tasks).toContain('Migrate storage service');
    });

    it('should calculate estimated hours correctly', () => {
      const plan = service.generateMigrationPlan('google', mockServices);

      // Infrastructure (8h) + High priority (16h) + Medium priority (12h) + Low priority (8h) + Testing (12h)
      const expectedHours = 8 + 16 + 12 + 8 + 12;
      expect(plan.estimatedHours).toBe(expectedHours);
    });
  });

  describe('generateExecutorTemplate', () => {
    const mockServiceInfo = {
      name: 'ads',
      path: '/mock/ads',
      type: 'directory' as const,
      complexity: 8,
      dependencies: ['@google-cloud/ads'],
      migrationPriority: 'high' as const,
    };

    it('should generate valid executor template', () => {
      const template = service.generateExecutorTemplate('ads', 'google', mockServiceInfo);

      expect(template).toContain('class AdsExecutor extends BaseNodeExecutor');
      expect(template).toContain("readonly type = 'google.ads'");
      expect(template).toContain('readonly category = NodeCategory.GOOGLE');
      expect(template).toContain('protected async executeNode');
      expect(template).toContain('getInputSchema()');
      expect(template).toContain('getOutputSchema()');
    });

    it('should handle service names with special characters', () => {
      const template = service.generateExecutorTemplate('google-storage', 'google', mockServiceInfo);

      expect(template).toContain('class GoogleStorageExecutor');
      expect(template).toContain("readonly type = 'google.google-storage'");
    });

    it('should include proper imports and decorators', () => {
      const template = service.generateExecutorTemplate('ads', 'google', mockServiceInfo);

      expect(template).toContain("import { Injectable } from '@nestjs/common'");
      expect(template).toContain("import { BaseNodeExecutor } from '../base/base-node-executor'");
      expect(template).toContain('@Injectable()');
    });
  });

  describe('Service Analysis Helper Methods', () => {
    it('should calculate service complexity correctly', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.statSync.mockReturnValue({
        isFile: () => true,
        isDirectory: () => false,
      } as any);

      mockFs.readFileSync.mockReturnValue(`
        export class TestService {
          async method1() {}
          async method2() {}
          async method3() {}
        }
      `);

      // Access private method through any cast for testing
      const complexity = await (service as any).calculateServiceComplexity('/mock/service.ts');

      expect(complexity).toBeGreaterThan(0);
      expect(complexity).toBeLessThanOrEqual(10);
    });

    it('should extract service dependencies correctly', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.statSync.mockReturnValue({
        isFile: () => true,
        isDirectory: () => false,
      } as any);

      mockFs.readFileSync.mockReturnValue(`
        import { Injectable } from '@nestjs/common';
        import { GoogleAuth } from 'google-auth-library';
        import { Storage } from '@google-cloud/storage';
      `);

      const dependencies = await (service as any).extractServiceDependencies('/mock/service.ts');

      expect(dependencies).toContain('@nestjs/common');
      expect(dependencies).toContain('google-auth-library');
      expect(dependencies).toContain('@google-cloud/storage');
    });

    it('should calculate migration priority correctly', () => {
      const highPriority = (service as any).calculateMigrationPriority('google-ads');
      const mediumPriority = (service as any).calculateMigrationPriority('google-drive');
      const lowPriority = (service as any).calculateMigrationPriority('google-vision');

      expect(highPriority).toBe('high');
      expect(mediumPriority).toBe('medium');
      expect(lowPriority).toBe('low');
    });
  });

  describe('Migration Candidates Generation', () => {
    it('should generate migration candidates correctly', () => {
      const servicesByCategory = {
        google: {
          category: 'google',
          services: [
            {
              name: 'ads',
              path: '/mock/ads',
              type: 'directory' as const,
              complexity: 8,
              dependencies: [],
              migrationPriority: 'high' as const,
            },
            {
              name: 'storage',
              path: '/mock/storage',
              type: 'file' as const,
              complexity: 4,
              dependencies: [],
              migrationPriority: 'low' as const,
            },
          ],
          totalComplexity: 12,
          migrationEstimate: 8,
        },
      };

      const candidates = (service as any).generateMigrationCandidates(servicesByCategory);

      expect(candidates).toHaveLength(2);
      expect(candidates[0].priority).toBe('high'); // Should be sorted by priority
      expect(candidates[1].priority).toBe('low');
      expect(candidates[0].serviceName).toBe('ads');
      expect(candidates[1].serviceName).toBe('storage');
    });
  });

  describe('Recommendations Generation', () => {
    it('should generate appropriate recommendations', () => {
      const analysis = {
        totalServices: 25,
        complexityScore: 80,
        servicesByCategory: {},
        migrationCandidates: [],
        dependencies: [],
        recommendations: [],
      };

      const recommendations = (service as any).generateRecommendations(analysis, { valid: true, errors: [], warnings: ['test warning'] });

      expect(recommendations).toContain('Consider phased migration approach due to large number of services');
      expect(recommendations).toContain('High complexity detected - allocate additional testing time');
      expect(recommendations).toContain('Address validation warnings for better reliability');
    });

    it('should handle low complexity scenarios', () => {
      const analysis = {
        totalServices: 5,
        complexityScore: 30,
        servicesByCategory: {},
        migrationCandidates: [],
        dependencies: [],
        recommendations: [],
      };

      const recommendations = (service as any).generateRecommendations(analysis, { valid: true, errors: [], warnings: [] });

      expect(recommendations).toHaveLength(0);
    });
  });
});
