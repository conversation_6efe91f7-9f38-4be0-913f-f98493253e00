import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

@Injectable()
export class GoogleSheetsAddSheetExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.addSheet';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Add Sheet to Google Spreadsheet';
  readonly description = 'Thêm sheet mới vào Google Spreadsheet';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      const { 
        spreadsheetId, 
        title,
        rowCount = 1000,
        columnCount = 26,
        index
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (!title) {
        throw new Error('Sheet title is required');
      }

      const accessToken = await this.getGoogleAccessToken(context);

      const result = await this.googleSheetsService.addSheet(
        accessToken,
        spreadsheetId,
        {
          title,
          rowCount,
          columnCount,
          index,
        }
      );

      return {
        spreadsheetId,
        sheetId: result.sheetId,
        title: result.title,
        index: result.index,
        rowCount: result.rowCount,
        columnCount: result.columnCount,
        addedAt: new Date().toISOString(),
        sheetsResponse: result,
      };
    });
  }

  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (!inputs.title || typeof inputs.title !== 'string') {
      throw new Error('Sheet title must be a non-empty string');
    }

    return true;
  }

  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        title: {
          type: 'string',
          description: 'Tên của sheet mới',
          minLength: 1,
          maxLength: 100,
        },
        rowCount: {
          type: 'integer',
          description: 'Số hàng (mặc định: 1000)',
          default: 1000,
          minimum: 1,
          maximum: 10000,
        },
        columnCount: {
          type: 'integer',
          description: 'Số cột (mặc định: 26)',
          default: 26,
          minimum: 1,
          maximum: 1000,
        },
        index: {
          type: 'integer',
          description: 'Vị trí của sheet (mặc định: cuối cùng)',
          minimum: 0,
        },
      },
      required: ['spreadsheetId', 'title'],
    };
  }

  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: { type: 'string' },
        sheetId: { type: 'integer' },
        title: { type: 'string' },
        index: { type: 'integer' },
        rowCount: { type: 'integer' },
        columnCount: { type: 'integer' },
        addedAt: { type: 'string' },
        sheetsResponse: { type: 'object' },
      },
    };
  }
}
