import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProviderEnum } from '../enums/provider.enum';

/**
 * Entity cho bảng integration_providers
 * Lưu thông tin các nhà cung cấp tích hợp (integration providers)
 */
@Entity('integration_providers')
export class IntegrationProvider {
  /**
   * <PERSON>h<PERSON><PERSON> chính, định danh duy nhất
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Mã định danh duy nhất cho loại tích hợp (ví dụ: GHTK, GHN, AHAMOVE)
   */
  @Column({
    type: 'varchar',
    unique: true,
    nullable: false,
    comment: 'Mã định danh duy nhất cho loại tích hợp (ví dụ: GHTK, GHN)'
  })
  type: ProviderEnum;

  /**
   * Schema dùng cho cấu hình MCP dưới dạng JSON
   */
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'mcp_schema',
    comment: 'Schema dùng cho cấu hình MCP dưới dạng JSON'
  })
  mcpSchema: Record<string, any> | null;

  /**
   * Thời gian tạo (timestamp dạng milliseconds)
   */
  @Column({
    type: 'bigint',
    name: 'created_at',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (timestamp dạng milliseconds)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp dạng milliseconds)
   */
  @Column({
    type: 'bigint',
    name: 'updated_at',
    nullable: true,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật (timestamp dạng milliseconds)'
  })
  updatedAt: number | null;

  /**
   * ID người tạo bản ghi
   */
  @Column({
    type: 'integer',
    name: 'created_by',
    nullable: true,
    comment: 'ID người tạo bản ghi'
  })
  createdBy: number | null;

  /**
   * ID người cập nhật bản ghi
   */
  @Column({
    type: 'integer',
    name: 'updated_by',
    nullable: true,
    comment: 'ID người cập nhật bản ghi'
  })
  updatedBy: number | null;
}
