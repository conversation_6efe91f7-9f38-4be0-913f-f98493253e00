import { Controller, Post, Body, Get } from '@nestjs/common';
import { GoogleSheetsCreateExecutor } from './google-sheets-create.executor';
import { GoogleSheetsAppendRowExecutor } from './google-sheets-update-values.executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';

/**
 * Test controller để verify Google Sheets executors
 * CHỈ DÙNG CHO DEVELOPMENT - XÓA TRƯỚC KHI PRODUCTION
 */
@Controller('test/google-sheets')
export class GoogleSheetsTestController {
  constructor(
    private readonly createExecutor: GoogleSheetsCreateExecutor,
    private readonly updateExecutor: GoogleSheetsAppendRowExecutor,
  ) {}

  /**
   * Test tạo Google Spreadsheet
   */
  @Post('create')
  async testCreateSpreadsheet(@Body() body: any) {
    try {
      // Mock execution context
      const mockContext = {
        executionId: 'test-execution-1',
        workflowId: 'test-workflow-1',
        userId: body.userId || 1, // Test với userId từ request
        triggerType: 'manual',
        triggerData: {},
        getNodeOutput: () => undefined,
        setNodeOutput: () => {},
        variables: {},
        metadata: {},
        getNodeInput: () => ({}),
        isNodeExecuted: () => false,
        getExecutedNodes: () => [],
        logService: { log: () => {}, error: () => {} },
        loggingService: { log: () => {}, error: () => {} },
        eventService: { emit: () => {} },
        startTime: Date.now(),
        setVariable: () => {},
        // getVariable: () => undefined, // Removed - not in ExecutionContext interface
      } as unknown as ExecutionContext;

      const inputs = {
        title: body.title || 'Test Spreadsheet from Workflow',
        sheets: body.sheets || [
          {
            title: 'Sheet1',
            rowCount: 1000,
            columnCount: 26,
          },
        ],
      };

      const result = await this.createExecutor.executeNode(inputs, mockContext);
      
      return {
        success: true,
        message: 'Google Spreadsheet created successfully',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * Test cập nhật Google Sheets values
   */
  @Post('update-values')
  async testUpdateValues(@Body() body: any) {
    try {
      // Mock execution context
      const mockContext: ExecutionContext = {
        executionId: 'test-execution-2',
        workflowId: 'test-workflow-1',
        userId: body.userId || 1,
        triggerType: 'manual',
        triggerData: {},
        getNodeOutput: () => undefined,
        setNodeOutput: () => {},
        getNodeInput: () => undefined,
        isNodeExecuted: () => false,
        getExecutedNodes: () => [],
        logService: {} as any,
        loggingService: {} as any,
        eventService: {} as any,
        startTime: Date.now(),
        variables: {},
        metadata: {},
      };

      const inputs = {
        spreadsheetId: body.spreadsheetId,
        range: body.range || 'Sheet1!A1:C3',
        values: body.values || [
          ['Name', 'Age', 'City'],
          ['John Doe', 30, 'New York'],
          ['Jane Smith', 25, 'Los Angeles'],
        ],
        valueInputOption: body.valueInputOption || 'USER_ENTERED',
        append: body.append || false,
      };

      const result = await this.updateExecutor.executeNode(inputs, mockContext);
      
      return {
        success: true,
        message: 'Google Sheets values updated successfully',
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * Test authentication status
   */
  @Get('auth-status')
  async testAuthStatus() {
    try {
      // Test basic authentication infrastructure
      return {
        success: true,
        message: 'Authentication infrastructure is working',
        executors: {
          create: this.createExecutor.type,
          update: this.updateExecutor.type,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        error: error.stack,
      };
    }
  }

  /**
   * Get input schemas for testing
   */
  @Get('schemas')
  async getSchemas() {
    return {
      create: {
        input: this.createExecutor.getInputSchema(),
        output: this.createExecutor.getOutputSchema(),
      },
      update: {
        input: this.updateExecutor.getInputSchema(),
        output: this.updateExecutor.getOutputSchema(),
      },
    };
  }
}
