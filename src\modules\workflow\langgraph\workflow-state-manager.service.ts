import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../../infra/redis/redis.service';
import {
  WorkflowState,
  WorkflowCheckpoint,
  LangGraphIntegrationConfig,
} from './interfaces/langgraph.interface';

/**
 * Service for managing workflow state and checkpoints in LangGraph integration
 * Handles state persistence, checkpoint management, and state recovery
 */
@Injectable()
export class WorkflowStateManagerService {
  private readonly logger = new Logger(WorkflowStateManagerService.name);
  private readonly config: LangGraphIntegrationConfig;

  constructor(private readonly redisService: RedisService) {
    this.config = {
      enableCheckpoints: true,
      checkpointStorage: 'redis',
      maxCheckpoints: 100,
      maxConcurrentExecutions: 10,
      executionTimeout: 300000, // 5 minutes
      nodeTimeout: 60000, // 1 minute
      enableMetrics: true,
      enableTracing: true,
      logLevel: 'info',
      defaultErrorStrategy: 'stop',
      maxRetryAttempts: 3,
      retryDelay: 1000,
      stateCompression: true,
      statePersistence: true,
      stateCleanupInterval: 3600000, // 1 hour
    };
  }

  /**
   * Save workflow state checkpoint
   * @param executionId - Execution identifier
   * @param state - Current workflow state
   * @param checkpointType - Type of checkpoint
   * @param nodeId - Current node ID (optional)
   * @returns Checkpoint ID
   */
  async saveCheckpoint(
    executionId: string,
    state: WorkflowState,
    checkpointType: 'auto' | 'error' | 'manual' = 'auto',
    nodeId?: string
  ): Promise<string> {
    if (!this.config.enableCheckpoints) {
      return '';
    }

    const checkpointId = this.generateCheckpointId(executionId, checkpointType);
    
    try {
      const checkpoint: WorkflowCheckpoint = {
        id: checkpointId,
        executionId,
        workflowId: state.workflowId,
        timestamp: Date.now(),
        state: this.config.stateCompression ? this.compressState(state) : state,
        nodeId: nodeId || state.currentNode || 'unknown',
        metadata: {
          version: '1.0.0',
          checkpointType,
          description: this.getCheckpointDescription(checkpointType, nodeId),
        },
      };

      // Save to Redis
      const key = this.getCheckpointKey(executionId, checkpointId);
      await this.redisService.set(
        key,
        JSON.stringify(checkpoint),
        this.getCheckpointTTL()
      );

      // Add to checkpoint list
      await this.addToCheckpointList(executionId, checkpointId);

      // Cleanup old checkpoints if needed
      await this.cleanupOldCheckpoints(executionId);

      this.logger.debug(`Saved checkpoint: ${checkpointId} for execution: ${executionId}`);
      return checkpointId;

    } catch (error) {
      this.logger.error(`Failed to save checkpoint for execution ${executionId}:`, error);
      throw new Error(`Checkpoint save failed: ${error.message}`);
    }
  }

  /**
   * Load workflow state from checkpoint
   * @param executionId - Execution identifier
   * @param checkpointId - Checkpoint identifier (optional, loads latest if not provided)
   * @returns Workflow state or null if not found
   */
  async loadCheckpoint(
    executionId: string,
    checkpointId?: string
  ): Promise<WorkflowState | null> {
    try {
      // Get checkpoint ID if not provided
      if (!checkpointId) {
        checkpointId = await this.getLatestCheckpointId(executionId) || undefined;
        if (!checkpointId) {
          this.logger.warn(`No checkpoints found for execution: ${executionId}`);
          return null;
        }
      }

      // Load checkpoint from Redis
      const key = this.getCheckpointKey(executionId, checkpointId);
      const checkpointData = await this.redisService.get(key);

      if (!checkpointData) {
        this.logger.warn(`Checkpoint not found: ${checkpointId} for execution: ${executionId}`);
        return null;
      }

      const checkpoint: WorkflowCheckpoint = JSON.parse(checkpointData);
      
      // Decompress state if needed
      const state = this.config.stateCompression 
        ? this.decompressState(checkpoint.state)
        : checkpoint.state;

      this.logger.debug(`Loaded checkpoint: ${checkpointId} for execution: ${executionId}`);
      return state;

    } catch (error) {
      this.logger.error(`Failed to load checkpoint ${checkpointId} for execution ${executionId}:`, error);
      return null;
    }
  }

  /**
   * Get all checkpoint IDs for an execution
   * @param executionId - Execution identifier
   * @returns Array of checkpoint IDs
   */
  async getCheckpointIds(executionId: string): Promise<string[]> {
    try {
      const listKey = this.getCheckpointListKey(executionId);
      const checkpointIds = await this.redisService.lrange(listKey, 0, -1);
      return checkpointIds || [];
    } catch (error) {
      this.logger.error(`Failed to get checkpoint IDs for execution ${executionId}:`, error);
      return [];
    }
  }

  /**
   * Delete checkpoint
   * @param executionId - Execution identifier
   * @param checkpointId - Checkpoint identifier
   * @returns Success status
   */
  async deleteCheckpoint(executionId: string, checkpointId: string): Promise<boolean> {
    try {
      // Delete checkpoint data
      const key = this.getCheckpointKey(executionId, checkpointId);
      await this.redisService.del(key);

      // Remove from checkpoint list
      const listKey = this.getCheckpointListKey(executionId);
      await this.redisService.lrem(listKey, 0, checkpointId);

      this.logger.debug(`Deleted checkpoint: ${checkpointId} for execution: ${executionId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to delete checkpoint ${checkpointId} for execution ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Delete all checkpoints for an execution
   * @param executionId - Execution identifier
   * @returns Success status
   */
  async deleteAllCheckpoints(executionId: string): Promise<boolean> {
    try {
      const checkpointIds = await this.getCheckpointIds(executionId);
      
      // Delete all checkpoint data
      const deletePromises = checkpointIds.map(checkpointId => {
        const key = this.getCheckpointKey(executionId, checkpointId);
        return this.redisService.del(key);
      });

      await Promise.all(deletePromises);

      // Delete checkpoint list
      const listKey = this.getCheckpointListKey(executionId);
      await this.redisService.del(listKey);

      this.logger.debug(`Deleted all checkpoints for execution: ${executionId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to delete all checkpoints for execution ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Get current workflow state
   * @param executionId - Execution identifier
   * @returns Current workflow state or null
   */
  async getCurrentState(executionId: string): Promise<WorkflowState | null> {
    try {
      const key = this.getCurrentStateKey(executionId);
      const stateData = await this.redisService.get(key);

      if (!stateData) {
        return null;
      }

      const state: WorkflowState = JSON.parse(stateData);
      return this.config.stateCompression ? this.decompressState(state) : state;

    } catch (error) {
      this.logger.error(`Failed to get current state for execution ${executionId}:`, error);
      return null;
    }
  }

  /**
   * Update current workflow state
   * @param executionId - Execution identifier
   * @param state - Updated workflow state
   * @returns Success status
   */
  async updateCurrentState(executionId: string, state: WorkflowState): Promise<boolean> {
    try {
      const key = this.getCurrentStateKey(executionId);
      const stateToStore = this.config.stateCompression ? this.compressState(state) : state;
      
      await this.redisService.set(
        key,
        JSON.stringify(stateToStore),
        this.getStateTTL()
      );

      this.logger.debug(`Updated current state for execution: ${executionId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to update current state for execution ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Generate checkpoint ID
   * @private
   */
  private generateCheckpointId(executionId: string, type: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${executionId}_${type}_${timestamp}_${random}`;
  }

  /**
   * Get checkpoint Redis key
   * @private
   */
  private getCheckpointKey(executionId: string, checkpointId: string): string {
    return `workflow:checkpoint:${executionId}:${checkpointId}`;
  }

  /**
   * Get checkpoint list Redis key
   * @private
   */
  private getCheckpointListKey(executionId: string): string {
    return `workflow:checkpoints:${executionId}`;
  }

  /**
   * Get current state Redis key
   * @private
   */
  private getCurrentStateKey(executionId: string): string {
    return `workflow:state:${executionId}`;
  }

  /**
   * Add checkpoint to list
   * @private
   */
  private async addToCheckpointList(executionId: string, checkpointId: string): Promise<void> {
    const listKey = this.getCheckpointListKey(executionId);
    await this.redisService.lpush(listKey, checkpointId);
    await this.redisService.expire(listKey, this.getCheckpointTTL());
  }

  /**
   * Get latest checkpoint ID
   * @private
   */
  private async getLatestCheckpointId(executionId: string): Promise<string | null> {
    const listKey = this.getCheckpointListKey(executionId);
    const checkpointIds = await this.redisService.lrange(listKey, 0, 0);
    return checkpointIds && checkpointIds.length > 0 ? checkpointIds[0] : null;
  }

  /**
   * Cleanup old checkpoints
   * @private
   */
  private async cleanupOldCheckpoints(executionId: string): Promise<void> {
    try {
      const listKey = this.getCheckpointListKey(executionId);
      const checkpointCount = await this.redisService.llen(listKey);

      if (checkpointCount > this.config.maxCheckpoints) {
        // Get old checkpoints to delete
        const oldCheckpoints = await this.redisService.lrange(
          listKey,
          this.config.maxCheckpoints,
          -1
        );

        // Delete old checkpoint data
        const deletePromises = oldCheckpoints.map(checkpointId => {
          const key = this.getCheckpointKey(executionId, checkpointId);
          return this.redisService.del(key);
        });

        await Promise.all(deletePromises);

        // Trim the list
        await this.redisService.ltrim(listKey, 0, this.config.maxCheckpoints - 1);

        this.logger.debug(`Cleaned up ${oldCheckpoints.length} old checkpoints for execution: ${executionId}`);
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup old checkpoints for execution ${executionId}:`, error);
    }
  }

  /**
   * Compress state for storage
   * @private
   */
  private compressState(state: WorkflowState): WorkflowState {
    // Simple compression - remove large objects or compress JSON
    // In production, could use actual compression algorithms
    const compressed = { ...state };
    
    // Remove or compress large fields
    if (compressed.messages && compressed.messages.length > 10) {
      compressed.messages = compressed.messages.slice(-10); // Keep only last 10 messages
    }

    return compressed;
  }

  /**
   * Decompress state from storage
   * @private
   */
  private decompressState(state: WorkflowState): WorkflowState {
    // Reverse compression if needed
    return state;
  }

  /**
   * Get checkpoint description
   * @private
   */
  private getCheckpointDescription(type: string, nodeId?: string): string {
    switch (type) {
      case 'initial':
        return 'Initial workflow state';
      case 'progress':
        return `Progress checkpoint${nodeId ? ` at node ${nodeId}` : ''}`;
      case 'error':
        return `Error checkpoint${nodeId ? ` at node ${nodeId}` : ''}`;
      case 'manual':
        return `Manual checkpoint${nodeId ? ` at node ${nodeId}` : ''}`;
      default:
        return 'Workflow checkpoint';
    }
  }

  /**
   * Get checkpoint TTL in seconds
   * @private
   */
  private getCheckpointTTL(): number {
    return 24 * 60 * 60; // 24 hours
  }

  /**
   * Get state TTL in seconds
   * @private
   */
  private getStateTTL(): number {
    return 6 * 60 * 60; // 6 hours
  }

  /**
   * Get configuration
   */
  getConfig(): LangGraphIntegrationConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<LangGraphIntegrationConfig>): void {
    Object.assign(this.config, updates);
    this.logger.debug('Updated LangGraph integration configuration');
  }
}
