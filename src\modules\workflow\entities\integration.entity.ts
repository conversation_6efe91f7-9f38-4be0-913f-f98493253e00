import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { OwnedTypeEnum } from '../enums';
import { Metadata } from '../interfaces/metadata.interface';

/**
 * Entity đại diện cho bảng integration trong cơ sở dữ liệu
 * Quản lý thông tin các tích hợp của người dùng
 */
@Entity('integration')
export class Integration {

  /**
   * UUID định danh duy nhất cho tích hợp
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của tích hợp
   */
  @Column({
    name: 'integration_name',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Tên của tích hợp'
  })
  integrationName: string;

  /**
   * ID tham chiếu đến bảng integration_providers
   */
  @Column({
    name: 'type_id',
    type: 'integer',
    nullable: false,
    comment: 'ID tham chiếu đến bảng integration_providers'
  })
  typeId: number;

  /**
   * ID của người dùng (null cho admin integrations)
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của người dùng (null cho admin integrations)'
  })
  userId: number | null;

  /**
   * Loại chủ sở hữu tích hợp
   */
  @Column({
    name: 'owned_type',
    type: 'enum',
    enum: OwnedTypeEnum,
    nullable: true,
    comment: 'Loại chủ sở hữu tích hợp'
  })
  ownedType: OwnedTypeEnum | null;

  /**
   * Thời điểm tạo tích hợp
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    nullable: false,
    comment: 'Thời điểm tạo tích hợp'
  })
  createdAt: number;

  /**
   * ID của nhân viên (nếu được tạo bởi admin)
   */
  @Column({
    name: 'employee_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của nhân viên'
  })
  employeeId: number | null;

  /**
   * Cấu hình được mã hóa
   */
  @Column({
    name: 'encrypted_config',
    type: 'text',
    nullable: true,
    comment: 'Cấu hình được mã hóa'
  })
  encryptedConfig: string | null;

  /**
   * Cấu hình MCP được mã hóa
   */
  @Column({
    name: 'mcp_encrypted_config',
    type: 'text',
    nullable: true,
    comment: 'Cấu hình MCP được mã hóa'
  })
  mcpEncryptedConfig: string | null;

  /**
   * Metadata bổ sung dưới dạng JSONB
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: true,
    comment: 'Metadata bổ sung dưới dạng JSONB'
  })
  metadata: Metadata | null;

  /**
   * Secret key cho webhook (nếu có)
   */
  @Column({ name: 'secret_key', type: 'varchar', length: 255, nullable: true })
  secretKey: string | null;
}