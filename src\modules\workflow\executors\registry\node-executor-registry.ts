import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { NodeCategory } from '../../entities/node-definition.entity';
import { BaseNodeExecutor } from '../base/base-node-executor';
import {
  ExecutorMetadata,
  ExecutorRegistryEntry,
  extractExecutorMetadata,
  validateExecutorMetadata,
} from './executor-metadata';

/**
 * Registry service để manage tất cả node executors
 * Provides discovery, registration, và retrieval của executors
 */
@Injectable()
export class NodeExecutorRegistry implements OnModuleInit {
  private readonly logger = new Logger(NodeExecutorRegistry.name);
  private readonly registry = new Map<string, ExecutorRegistryEntry>();
  private readonly categoryIndex = new Map<NodeCategory, string[]>();
  private isInitialized = false;

  constructor(private readonly moduleRef: ModuleRef) {}

  async onModuleInit() {
    await this.initializeRegistry();
  }

  /**
   * Initialize registry và discover tất cả executors
   */
  private async initializeRegistry(): Promise<void> {
    try {
      this.logger.log('Initializing node executor registry...');
      
      // Auto-discover executors from modules
      await this.discoverExecutors();
      
      // Validate all registered executors
      this.validateRegistry();
      
      // Build category index
      this.buildCategoryIndex();
      
      this.isInitialized = true;
      this.logger.log(`Registry initialized with ${this.registry.size} executors`);
      
      // Log summary by category
      this.logRegistrySummary();
    } catch (error) {
      this.logger.error('Failed to initialize executor registry:', error);
      throw error;
    }
  }

  /**
   * Register một executor manually
   * @param executorClass - Executor class constructor
   * @returns true if registered successfully
   */
  registerExecutor(executorClass: any): boolean {
    try {
      const metadata = extractExecutorMetadata(executorClass);
      if (!metadata) {
        this.logger.warn(`Failed to extract metadata from executor: ${executorClass.name}`);
        return false;
      }

      const validation = validateExecutorMetadata(metadata);
      if (!validation.valid) {
        this.logger.warn(`Invalid executor metadata for ${metadata.type}:`, validation.errors);
        return false;
      }

      // Check for duplicate registration
      if (this.registry.has(metadata.type)) {
        this.logger.warn(`Executor ${metadata.type} is already registered`);
        return false;
      }

      // Create registry entry
      const entry: ExecutorRegistryEntry = {
        metadata,
        executorClass,
        instance: null,
        isLoaded: false,
        errorCount: 0,
      };

      this.registry.set(metadata.type, entry);
      this.addToCategoryIndex(metadata.type, metadata.category);

      this.logger.debug(`Registered executor: ${metadata.type}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to register executor ${executorClass.name}:`, error);
      return false;
    }
  }

  /**
   * Get executor instance by type
   * @param type - Executor type
   * @returns Executor instance or null
   */
  async getExecutor(type: string): Promise<BaseNodeExecutor | null> {
    try {
      const entry = this.registry.get(type);
      if (!entry) {
        this.logger.warn(`Executor not found: ${type}`);
        return null;
      }

      // Return cached instance if available
      if (entry.instance && entry.isLoaded) {
        return entry.instance;
      }

      // Create new instance
      const instance = await this.createExecutorInstance(entry);
      if (instance) {
        entry.instance = instance;
        entry.isLoaded = true;
        entry.loadedAt = Date.now();
      }

      return instance;
    } catch (error) {
      this.logger.error(`Failed to get executor ${type}:`, error);
      this.incrementErrorCount(type);
      return null;
    }
  }

  /**
   * Get all registered executor types
   * @returns Array of executor types
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Get executors by category
   * @param category - Node category
   * @returns Array of executor types
   */
  getExecutorsByCategory(category: NodeCategory): string[] {
    return this.categoryIndex.get(category) || [];
  }

  /**
   * Get executor metadata
   * @param type - Executor type
   * @returns Executor metadata or null
   */
  getExecutorMetadata(type: string): ExecutorMetadata | null {
    const entry = this.registry.get(type);
    return entry ? entry.metadata : null;
  }

  /**
   * Check if executor is registered
   * @param type - Executor type
   * @returns true if registered
   */
  isRegistered(type: string): boolean {
    return this.registry.has(type);
  }

  /**
   * Get registry statistics
   * @returns Registry statistics
   */
  getStatistics(): {
    totalExecutors: number;
    loadedExecutors: number;
    categoryCounts: Record<string, number>;
    errorCounts: Record<string, number>;
  } {
    const stats = {
      totalExecutors: this.registry.size,
      loadedExecutors: 0,
      categoryCounts: {} as Record<string, number>,
      errorCounts: {} as Record<string, number>,
    };

    for (const [type, entry] of this.registry.entries()) {
      if (entry.isLoaded) {
        stats.loadedExecutors++;
      }

      const category = entry.metadata.category;
      stats.categoryCounts[category] = (stats.categoryCounts[category] || 0) + 1;

      if (entry.errorCount > 0) {
        stats.errorCounts[type] = entry.errorCount;
      }
    }

    return stats;
  }

  /**
   * Reload executor (clear cache và recreate instance)
   * @param type - Executor type
   * @returns true if reloaded successfully
   */
  async reloadExecutor(type: string): Promise<boolean> {
    try {
      const entry = this.registry.get(type);
      if (!entry) {
        return false;
      }

      // Clear cached instance
      entry.instance = null;
      entry.isLoaded = false;
      entry.loadedAt = undefined;

      // Create new instance
      const instance = await this.createExecutorInstance(entry);
      if (instance) {
        entry.instance = instance;
        entry.isLoaded = true;
        entry.loadedAt = Date.now();
        this.logger.log(`Reloaded executor: ${type}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to reload executor ${type}:`, error);
      return false;
    }
  }

  /**
   * Auto-discover executors từ modules
   */
  private async discoverExecutors(): Promise<void> {
    // This would be implemented to scan for executor classes
    // For now, we'll register them manually in the module
    this.logger.debug('Auto-discovery not implemented yet - use manual registration');
  }

  /**
   * Create executor instance
   * @param entry - Registry entry
   * @returns Executor instance
   */
  private async createExecutorInstance(entry: ExecutorRegistryEntry): Promise<BaseNodeExecutor | null> {
    try {
      // Try to get instance from DI container first
      try {
        const instance = await this.moduleRef.get(entry.executorClass, { strict: false });
        if (instance) {
          return instance;
        }
      } catch {
        // Fall back to manual instantiation
      }

      // Create instance manually
      const instance = new entry.executorClass();
      
      // Validate instance
      if (!this.validateExecutorInstance(instance, entry.metadata)) {
        throw new Error('Executor instance validation failed');
      }

      return instance;
    } catch (error) {
      this.logger.error(`Failed to create executor instance for ${entry.metadata.type}:`, error);
      this.incrementErrorCount(entry.metadata.type);
      return null;
    }
  }

  /**
   * Validate executor instance
   * @param instance - Executor instance
   * @param metadata - Expected metadata
   * @returns true if valid
   */
  private validateExecutorInstance(instance: any, metadata: ExecutorMetadata): boolean {
    // Check required methods
    const requiredMethods = ['execute', 'getInputSchema', 'getOutputSchema'];
    for (const method of requiredMethods) {
      if (typeof instance[method] !== 'function') {
        this.logger.error(`Executor ${metadata.type} missing required method: ${method}`);
        return false;
      }
    }

    // Check required properties
    if (instance.type !== metadata.type) {
      this.logger.error(`Executor type mismatch: expected ${metadata.type}, got ${instance.type}`);
      return false;
    }

    return true;
  }

  /**
   * Validate entire registry
   */
  private validateRegistry(): void {
    let validCount = 0;
    let invalidCount = 0;

    for (const [type, entry] of this.registry.entries()) {
      const validation = validateExecutorMetadata(entry.metadata);
      if (validation.valid) {
        validCount++;
      } else {
        invalidCount++;
        this.logger.warn(`Invalid executor ${type}:`, validation.errors);
      }
    }

    this.logger.log(`Registry validation: ${validCount} valid, ${invalidCount} invalid executors`);
  }

  /**
   * Build category index
   */
  private buildCategoryIndex(): void {
    this.categoryIndex.clear();
    
    for (const [type, entry] of this.registry.entries()) {
      this.addToCategoryIndex(type, entry.metadata.category);
    }
  }

  /**
   * Add executor to category index
   * @param type - Executor type
   * @param category - Node category
   */
  private addToCategoryIndex(type: string, category: NodeCategory): void {
    if (!this.categoryIndex.has(category)) {
      this.categoryIndex.set(category, []);
    }
    this.categoryIndex.get(category)!.push(type);
  }

  /**
   * Increment error count for executor
   * @param type - Executor type
   */
  private incrementErrorCount(type: string): void {
    const entry = this.registry.get(type);
    if (entry) {
      entry.errorCount++;
      entry.lastError = new Date().toISOString();
    }
  }

  /**
   * Log registry summary
   */
  private logRegistrySummary(): void {
    const stats = this.getStatistics();
    
    this.logger.log('Registry Summary:');
    this.logger.log(`- Total executors: ${stats.totalExecutors}`);
    this.logger.log('- By category:');
    
    for (const [category, count] of Object.entries(stats.categoryCounts)) {
      this.logger.log(`  - ${category}: ${count} executors`);
    }
  }
}
