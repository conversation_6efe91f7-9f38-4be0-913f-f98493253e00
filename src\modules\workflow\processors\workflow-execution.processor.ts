import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, WorkflowExecutionJobName } from '../../../queue/queue-name.enum';
import { WorkflowExecutionJobData, WorkflowNodeExecutionJobData } from '../../../queue/queue.types';
import { WorkflowExecutorService } from '../services/workflow-executor.service';
import { LoggingService } from '../services/logging.service';
import { EventService } from '../services/event.service';

/**
 * Processor xử lý workflow execution jobs
 * Handles workflow và node execution từ queue
 */
@Processor(QueueName.WORKFLOW_EXECUTION)
export class WorkflowExecutionProcessor extends WorkerHost {
  private readonly logger = new Logger(WorkflowExecutionProcessor.name);

  constructor(
    private readonly workflowExecutorService: WorkflowExecutorService,
    private readonly loggingService: LoggingService,
    private readonly eventService: EventService,
  ) {
    super();
  }

  /**
   * Process workflow execution job
   * @param job BullMQ job containing workflow execution data
   */
  async process(job: Job<WorkflowExecutionJobData | WorkflowNodeExecutionJobData>): Promise<any> {
    const { name, data } = job;
    
    this.logger.log(`Processing job: ${name} with ID: ${job.id}`);
    
    try {
      switch (name) {
        case WorkflowExecutionJobName.EXECUTE_WORKFLOW:
          return await this.executeWorkflow(job as Job<WorkflowExecutionJobData>);
          
        case WorkflowExecutionJobName.EXECUTE_NODE:
          return await this.executeNode(job as Job<WorkflowNodeExecutionJobData>);
          
        case WorkflowExecutionJobName.RETRY_WORKFLOW:
          return await this.retryWorkflow(job as Job<WorkflowExecutionJobData>);
          
        case WorkflowExecutionJobName.CLEANUP_EXECUTION:
          return await this.cleanupExecution(job);
          
        default:
          throw new Error(`Unknown job name: ${name}`);
      }
    } catch (error) {
      this.logger.error(`Job ${job.id} failed:`, error);
      
      // Log error to workflow execution logs
      await this.loggingService.logError(
        data.executionId || 'unknown',
        error.message,
        { jobId: job.id, jobName: name, error: error.stack }
      );
      
      throw error;
    }
  }

  /**
   * Execute complete workflow
   * @private
   */
  private async executeWorkflow(job: Job<WorkflowExecutionJobData>): Promise<any> {
    const { data } = job;
    
    this.logger.log(`Executing workflow: ${data.workflowId} for user: ${data.userId}`);
    
    // Update job progress
    await job.updateProgress(10);
    
    try {
      // Log workflow start
      await this.loggingService.logInfo(
        data.executionId,
        'Workflow execution started',
        {
          workflowId: data.workflowId,
          triggerType: data.triggerType,
          userId: data.userId
        }
      );

      // Publish workflow started event
      await this.eventService.publishExecutionStarted(
        data.executionId,
        data.workflowId,
        data.triggerData
      );

      await job.updateProgress(20);
      
      // Execute workflow using executor service
      const result = await this.workflowExecutorService.executeWorkflow(data);
      
      await job.updateProgress(90);
      
      // Log workflow completion
      await this.loggingService.logInfo(
        data.executionId,
        'Workflow execution completed',
        { result, duration: Date.now() - job.timestamp }
      );

      // Publish workflow completed event
      await this.eventService.publishExecutionCompleted(
        data.executionId,
        result
      );

      await job.updateProgress(100);

      this.logger.log(`Workflow ${data.workflowId} completed successfully`);
      return result;
      
    } catch (error) {
      // Log workflow failure
      await this.loggingService.logError(
        data.executionId,
        'Workflow execution failed',
        { error: error.message, stack: error.stack }
      );

      // Publish workflow failed event
      await this.eventService.publishExecutionFailed(
        data.executionId,
        error
      );

      throw error;
    }
  }

  /**
   * Execute single node
   * @private
   */
  private async executeNode(job: Job<WorkflowNodeExecutionJobData>): Promise<any> {
    const { data } = job;
    
    this.logger.log(`Executing node: ${data.nodeType} (${data.nodeId}) for execution: ${data.executionId}`);
    
    await job.updateProgress(10);
    
    try {
      // Log node start
      await this.loggingService.logInfo(
        data.executionId,
        `Node execution started: ${data.nodeType}`,
        { nodeId: data.nodeId, nodeType: data.nodeType }
      );
      
      await job.updateProgress(30);
      
      // Execute node using executor service
      const result = await this.workflowExecutorService.executeNode(data);
      
      await job.updateProgress(90);
      
      // Log node completion
      await this.loggingService.logInfo(
        data.executionId,
        `Node execution completed: ${data.nodeType}`,
        { nodeId: data.nodeId, result }
      );
      
      await job.updateProgress(100);
      
      this.logger.log(`Node ${data.nodeId} completed successfully`);
      return result;
      
    } catch (error) {
      // Log node failure
      await this.loggingService.logError(
        data.executionId,
        `Node execution failed: ${data.nodeType}`,
        { nodeId: data.nodeId, error: error.message }
      );
      
      throw error;
    }
  }

  /**
   * Retry failed workflow
   * @private
   */
  private async retryWorkflow(job: Job<WorkflowExecutionJobData>): Promise<any> {
    const { data } = job;
    
    this.logger.log(`Retrying workflow: ${data.workflowId} for execution: ${data.executionId}`);
    
    // Log retry attempt
    await this.loggingService.logInfo(
      data.executionId,
      'Workflow retry started',
      { attempt: job.attemptsMade, maxAttempts: job.opts.attempts }
    );
    
    // Execute workflow with retry flag
    return await this.executeWorkflow(job);
  }

  /**
   * Cleanup execution data
   * @private
   */
  private async cleanupExecution(job: Job): Promise<void> {
    const { data } = job;
    
    this.logger.log(`Cleaning up execution: ${data.executionId}`);
    
    try {
      // Cleanup using executor service
      await this.workflowExecutorService.cleanupExecution(data.executionId);
      
      this.logger.log(`Cleanup completed for execution: ${data.executionId}`);
    } catch (error) {
      this.logger.warn(`Cleanup failed for execution: ${data.executionId}`, error);
      // Don't throw error for cleanup failures
    }
  }

  /**
   * Handle job completion
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(`Job ${job.id} completed with result:`, result);
  }

  /**
   * Handle job failure
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.id} failed:`, error);
  }

  /**
   * Handle job progress
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.debug(`Job ${job.id} progress: ${progress}%`);
  }

  /**
   * Handle job stalled
   */
  @OnWorkerEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`Job ${job.id} stalled`);
  }
}
