import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  BeforeUpdate,
  ManyToOne,
  JoinColumn
} from 'typeorm';

/**
 * Entity đại diện cho bảng workflow_edges trong cơ sở dữ liệu
 * Lưu trữ các kết nối giữa các nodes trong workflow
 * Synced with BE App for Worker execution
 */
@Entity('workflow_edges')
@Index('idx_workflow_edges_workflow_id', ['workflowId'])
@Index('idx_workflow_edges_source_node', ['sourceNodeId'])
@Index('idx_workflow_edges_target_node', ['targetNodeId'])
export class WorkflowEdge {
  /**
   * ID định danh duy nhất cho một workflow edge
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Khóa ngoại, liên kết đến workflow chứa edge này
   */
  @Column({ name: 'workflow_id', type: 'uuid', nullable: false })
  workflowId: string;

  /**
   * ID của edge trong workflow (unique trong một workflow)
   */
  @Column({ name: 'edge_id', type: 'varchar', length: 255, nullable: false })
  edgeId: string;

  /**
   * ID của source node
   */
  @Column({ name: 'source_node_id', type: 'varchar', length: 255, nullable: false })
  sourceNodeId: string;

  /**
   * Output port của source node (nếu có nhiều outputs)
   */
  @Column({ name: 'source_port', type: 'varchar', length: 100, nullable: true })
  sourcePort: string | null;

  /**
   * ID của target node
   */
  @Column({ name: 'target_node_id', type: 'varchar', length: 255, nullable: false })
  targetNodeId: string;

  /**
   * Input port của target node (nếu có nhiều inputs)
   */
  @Column({ name: 'target_port', type: 'varchar', length: 100, nullable: true })
  targetPort: string | null;

  /**
   * Loại edge (normal, conditional, error, etc.)
   */
  @Column({ name: 'edge_type', type: 'varchar', length: 50, default: 'normal', nullable: false })
  edgeType: string;

  /**
   * Điều kiện để edge được kích hoạt (cho conditional edges)
   */
  @Column({ name: 'condition', type: 'jsonb', nullable: true })
  condition: Record<string, any> | null;

  /**
   * Metadata bổ sung cho edge (UI styling, labels, etc.)
   */
  @Column({ name: 'metadata', type: 'jsonb', default: () => "'{}'::jsonb", nullable: false })
  metadata: Record<string, any>;

  /**
   * Thời gian tạo edge, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật edge lần cuối, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }
}
