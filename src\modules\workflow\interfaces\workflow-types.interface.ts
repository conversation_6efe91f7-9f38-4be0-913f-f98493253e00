import { NodeCategory } from '../entities/node-definition.entity';
import { ExecutionStatus } from '../entities/workflow-execution.entity';

/**
 * Workflow definition structure (synced with BE App)
 */
export interface WorkflowDefinition {
  /**
   * Danh sách nodes trong workflow
   */
  nodes: WorkflowNodeDefinition[];

  /**
   * Danh sách edges kết nối các nodes
   */
  edges: WorkflowEdgeDefinition[];

  /**
   * Metadata cho UI và execution
   */
  metadata?: WorkflowUIMetadata;
}

/**
 * Node definition trong workflow
 */
export interface WorkflowNodeDefinition {
  /**
   * ID unique của node trong workflow
   */
  id: string;

  /**
   * Type của node (reference đến node_definitions.type)
   */
  type: string;

  /**
   * Vị trí trên canvas
   */
  position: { x: number; y: number };

  /**
   * Data của node
   */
  data: {
    /**
     * Label hiển thị
     */
    label: string;

    /**
     * Input configuration
     */
    inputs: Record<string, NodeInputValue>;

    /**
     * Additional config
     */
    config?: Record<string, any>;
  };

  /**
   * Parent node (cho grouped nodes)
   */
  parentNode?: string;

  /**
   * Extent constraints
   */
  extent?: 'parent' | { x: number; y: number; width: number; height: number };
}

/**
 * Edge definition trong workflow
 */
export interface WorkflowEdgeDefinition {
  /**
   * ID unique của edge trong workflow
   */
  id: string;

  /**
   * Source node ID
   */
  source: string;

  /**
   * Target node ID
   */
  target: string;

  /**
   * Source handle (output port)
   */
  sourceHandle: string | null;

  /**
   * Target handle (input port)
   */
  targetHandle: string | null;

  /**
   * Edge type
   */
  type?: string;

  /**
   * Conditional logic
   */
  condition?: Record<string, any>;

  /**
   * Edge styling và metadata
   */
  data?: Record<string, any>;
}

/**
 * UI metadata cho workflow canvas
 */
export interface WorkflowUIMetadata {
  /**
   * Canvas viewport position
   */
  x: number;
  y: number;

  /**
   * Canvas zoom level
   */
  zoom: number;

  /**
   * Canvas dimensions
   */
  width?: number;
  height?: number;

  /**
   * Additional UI state
   */
  uiState?: Record<string, any>;
}

/**
 * Node input value types
 */
export type NodeInputValue = 
  | string 
  | number 
  | boolean 
  | Record<string, any> 
  | Array<any>
  | null;

/**
 * Node output structure
 */
export interface NodeOutput {
  /**
   * Output data
   */
  data: any;

  /**
   * Output metadata
   */
  metadata?: {
    /**
     * Execution time
     */
    executionTime?: number;

    /**
     * Success status
     */
    success: boolean;

    /**
     * Error information
     */
    error?: string;

    /**
     * Additional metrics
     */
    metrics?: Record<string, any>;
  };
}

/**
 * Workflow execution state
 */
export interface WorkflowExecutionState {
  /**
   * Execution ID
   */
  executionId: string;

  /**
   * Current status
   */
  status: ExecutionStatus;

  /**
   * Current node being executed
   */
  currentNode?: string;

  /**
   * Executed nodes với outputs
   */
  nodeOutputs: Record<string, NodeOutput>;

  /**
   * Execution context data
   */
  context: {
    /**
     * Trigger data
     */
    triggerData: any;

    /**
     * User ID
     */
    userId: number;

    /**
     * Workflow ID
     */
    workflowId: string;

    /**
     * Additional metadata
     */
    metadata?: Record<string, any>;
  };

  /**
   * Execution statistics
   */
  stats: {
    /**
     * Start time
     */
    startedAt: number;

    /**
     * Current time
     */
    currentTime: number;

    /**
     * Nodes executed
     */
    nodesExecuted: number;

    /**
     * Total nodes
     */
    totalNodes: number;

    /**
     * Success rate
     */
    successRate: number;
  };
}

/**
 * Node execution plan
 */
export interface NodeExecutionPlan {
  /**
   * Execution order của nodes
   */
  executionOrder: string[];

  /**
   * Dependencies map
   */
  dependencies: Record<string, string[]>;

  /**
   * Parallel execution groups
   */
  parallelGroups: string[][];

  /**
   * Conditional branches
   */
  conditionalBranches: Record<string, {
    condition: Record<string, any>;
    truePath: string[];
    falsePath: string[];
  }>;
}

/**
 * Webhook trigger data structure
 */
export interface WebhookTriggerData {
  /**
   * Webhook source
   */
  source: 'facebook' | 'zalo' | 'google' | 'custom';

  /**
   * Event type
   */
  eventType: string;

  /**
   * Raw payload
   */
  payload: Record<string, any>;

  /**
   * Headers
   */
  headers: Record<string, string>;

  /**
   * Timestamp
   */
  timestamp: number;

  /**
   * Verification status
   */
  verified: boolean;
}

/**
 * Manual trigger data structure
 */
export interface ManualTriggerData {
  /**
   * User input data
   */
  inputData: Record<string, any>;

  /**
   * Trigger source
   */
  source: 'ui' | 'api' | 'test';

  /**
   * User ID who triggered
   */
  triggeredBy: number;

  /**
   * Timestamp
   */
  timestamp: number;
}

/**
 * Scheduled trigger data structure
 */
export interface ScheduledTriggerData {
  /**
   * Schedule ID
   */
  scheduleId: string;

  /**
   * Cron expression
   */
  cronExpression: string;

  /**
   * Scheduled time
   */
  scheduledTime: number;

  /**
   * Actual execution time
   */
  executionTime: number;

  /**
   * Schedule metadata
   */
  metadata?: Record<string, any>;
}
