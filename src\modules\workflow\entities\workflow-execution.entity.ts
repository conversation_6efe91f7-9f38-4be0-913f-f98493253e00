import { Entity, PrimaryGeneratedColumn, Column, Index, Check, ManyToOne, OneToMany, JoinColumn } from 'typeorm';

/**
 * Enum định nghĩa trạng thái thực thi của workflow
 */
export enum ExecutionStatus {
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PAUSED = 'paused',
}

/**
 * Entity đại diện cho bảng workflow_executions trong cơ sở dữ liệu
 * Ghi lại lịch sử mỗi lần một workflow được chạy
 * Synced with BE App for Worker execution
 */
@Entity('workflow_executions')
@Index('idx_workflow_executions_workflow_id', ['workflowId'])
@Check(`status IN ('queued', 'running', 'completed', 'failed', 'paused')`)
export class WorkflowExecution {
  /**
   * ID định danh duy nhất cho một lần thực thi
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * <PERSON>h<PERSON>a ngoại, liên kết đến workflow đã được thực thi
   */
  @Column({ name: 'workflow_id', type: 'uuid', nullable: false })
  workflowId: string;

  /**
   * Trạng thái hiện tại của lần thực thi (e.g., "queued", "running", "completed", "failed", "paused")
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    nullable: false
  })
  status: ExecutionStatus;

  /**
   * Sự kiện kích hoạt workflow (e.g., payload từ webhook, trigger manual)
   */
  @Column({ name: 'trigger_event', type: 'jsonb', nullable: true })
  triggerEvent: Record<string, any> | null;

  /**
   * Thời điểm bắt đầu thực thi, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'started_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  startedAt: number;

  /**
   * Thời điểm kết thúc thực thi, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'finished_at', type: 'bigint', nullable: true })
  finishedAt: number | null;

  /**
   * Relationships for Worker execution
   */
  @ManyToOne('Workflow', 'executions')
  @JoinColumn({ name: 'workflow_id' })
  workflow: any;

  @OneToMany('WorkflowExecutionLog', 'workflowExecution')
  logs: any[];
}
