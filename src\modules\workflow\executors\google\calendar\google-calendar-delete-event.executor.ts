import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleCalendarService } from '../../../../../shared/services/google/calendar/google-calendar.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Calendar Delete Event node
 */
export interface GoogleCalendarDeleteEventInput {

  /**
   * ID của calendar (mặc định: 'primary')
   */
  calendarId?: string;

  /**
   * ID của event cần xóa
   */
  eventId: string;

  /**
   * <PERSON><PERSON> gửi thông báo không
   */
  sendNotifications?: boolean;
}

/**
 * Output schema cho Google Calendar Delete Event node
 */
export interface GoogleCalendarDeleteEventOutput {
  /**
   * ID của event đã xóa
   */
  eventId: string;

  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Metadata
   */
  metadata: {
    calendarId: string;
    deletedAt: string;
  };
}

/**
 * Executor để xóa event trong Google Calendar
 */
@Injectable()
export class GoogleCalendarDeleteEventExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.calendar.delete_event';
  readonly category = NodeCategory.GOOGLE_CALENDAR;
  readonly name = 'Delete Calendar Event';
  readonly description = 'Xóa event trong Google Calendar';

  constructor(
    integrationService: IntegrationService,
    private readonly calendarService: GoogleCalendarService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        calendarId: { type: 'string' },
        eventId: { type: 'string' },
        sendNotifications: { type: 'boolean' },
      },
      required: ['eventId'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        eventId: { type: 'string' },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node xóa event
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Deleting calendar event: ${inputs.eventId}`);

      // Validate required inputs
      if (!inputs.eventId) {
        throw new Error('Event ID is required');
      }

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      const calendarId = inputs.calendarId || 'primary';

      // Delete event
      await this.calendarService.deleteEvent(
        accessToken,
        calendarId,
        inputs.eventId,
        inputs.sendNotifications !== false, // Default to true
      );

      // Prepare output
      const output: GoogleCalendarDeleteEventOutput = {
        eventId: inputs.eventId,
        success: true,
        message: `Event ${inputs.eventId} has been deleted successfully`,
        metadata: {
          calendarId,
          deletedAt: new Date().toISOString(),
        },
      };

      this.logger.log(`Calendar event deleted successfully: ${inputs.eventId}`);

      return output;
    });
  }
}
