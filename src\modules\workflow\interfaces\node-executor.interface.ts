import { ExecutionContext, NodeExecutionResult } from './execution-context.interface';
import { NodeCategory } from '../entities/node-definition.entity';

/**
 * Base interface cho tất cả node executors
 * <PERSON><PERSON><PERSON> nghĩa contract cho việc thực thi các loại node khác nhau
 */
export interface NodeExecutor {
  /**
   * Loại node (unique identifier)
   * Examples: 'google.sheet.getRows', 'facebook.page.post', 'system.condition'
   */
  readonly type: string;

  /**
   * Category của node
   */
  readonly category: NodeCategory;

  /**
   * Phiên bản của node executor
   */
  readonly version: string;

  /**
   * Tên hiển thị của node
   */
  readonly name: string;

  /**
   * Mô tả chức năng của node
   */
  readonly description: string;

  /**
   * Thực thi node với input data và context
   * @param inputs - Input data cho node (đã được validate)
   * @param context - Execution context
   * @returns Promise<NodeExecutionResult>
   */
  execute(inputs: any, context: ExecutionContext): Promise<NodeExecutionResult>;

  /**
   * Validate input data trước khi thực thi
   * @param inputs - Input data cần validate
   * @returns true nếu valid, throw error nếu invalid
   */
  validateInputs(inputs: any): boolean;

  /**
   * Lấy JSON Schema cho input validation
   * @returns JSON Schema object
   */
  getInputSchema(): Record<string, any>;

  /**
   * Lấy JSON Schema cho output structure
   * @returns JSON Schema object
   */
  getOutputSchema(): Record<string, any>;

  /**
   * Kiểm tra node có sẵn sàng thực thi không
   * @param context - Execution context
   * @returns true nếu ready, false nếu chưa ready
   */
  isReady(context: ExecutionContext): Promise<boolean>;

  /**
   * Cleanup resources sau khi thực thi (optional)
   * @param context - Execution context
   */
  cleanup?(context: ExecutionContext): Promise<void>;
}

/**
 * Interface cho System nodes (condition, loop, delay, etc.)
 */
export interface SystemNodeExecutor extends NodeExecutor {
  readonly category: NodeCategory.SYSTEM;
}

/**
 * Interface cho Google Service nodes
 */
export interface GoogleServiceNodeExecutor extends NodeExecutor {
  readonly category: 
    | NodeCategory.GOOGLE_SHEET 
    | NodeCategory.GOOGLE_DOCS 
    | NodeCategory.GOOGLE_GMAIL
    | NodeCategory.GOOGLE_ADS
    | NodeCategory.GOOGLE_DRIVE
    | NodeCategory.GOOGLE_CALENDAR;

  /**
   * Google API credentials cần thiết
   */
  readonly requiredScopes: string[];
}

/**
 * Interface cho Facebook Service nodes
 */
export interface FacebookServiceNodeExecutor extends NodeExecutor {
  readonly category: 
    | NodeCategory.FACEBOOK_PAGE 
    | NodeCategory.FACEBOOK_ADS 
    | NodeCategory.FACEBOOK_MESSENGER
    | NodeCategory.INSTAGRAM;

  /**
   * Facebook permissions cần thiết
   */
  readonly requiredPermissions: string[];
}

/**
 * Interface cho Zalo Service nodes
 */
export interface ZaloServiceNodeExecutor extends NodeExecutor {
  readonly category: 
    | NodeCategory.ZALO_OA 
    | NodeCategory.ZALO_ZNS 
    | NodeCategory.ZALO;

  /**
   * Zalo API credentials cần thiết
   */
  readonly requiredCredentials: string[];
}

/**
 * Factory interface cho tạo node executors
 */
export interface NodeExecutorFactory {
  /**
   * Tạo executor cho node type
   * @param nodeType - Type của node
   * @returns NodeExecutor instance hoặc null nếu không support
   */
  createExecutor(nodeType: string): NodeExecutor | null;

  /**
   * Lấy danh sách tất cả supported node types
   * @returns Array of supported node types
   */
  getSupportedTypes(): string[];

  /**
   * Kiểm tra node type có được support không
   * @param nodeType - Type của node
   * @returns true nếu supported
   */
  isSupported(nodeType: string): boolean;

  /**
   * Register executor cho node type
   * @param nodeType - Type của node
   * @param executorClass - Class của executor
   */
  registerExecutor(nodeType: string, executorClass: new () => NodeExecutor): void;
}

/**
 * Registry interface cho quản lý node executors
 */
export interface NodeExecutorRegistry {
  /**
   * Lấy executor cho node type
   * @param nodeType - Type của node
   * @returns NodeExecutor instance
   * @throws Error nếu không tìm thấy executor
   */
  getExecutor(nodeType: string): NodeExecutor;

  /**
   * Lấy tất cả executors theo category
   * @param category - Category của nodes
   * @returns Array of NodeExecutor instances
   */
  getExecutorsByCategory(category: NodeCategory): NodeExecutor[];

  /**
   * Lấy metadata của tất cả executors
   * @returns Array of executor metadata
   */
  getAllExecutorMetadata(): Array<{
    type: string;
    category: NodeCategory;
    version: string;
    name: string;
    description: string;
  }>;
}
