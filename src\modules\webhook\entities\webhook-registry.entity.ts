import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

@Entity('webhook_registry')
@Index(['webhookPath', 'method'], { unique: true })
@Index('idx_webhook_registry_lookup', ['method', 'pathLength'])
export class WebhookRegistry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'webhook_path', type: 'varchar', length: 255 })
  webhookPath: string;

  @Column({ type: 'varchar', length: 10 })
  method: string;

  @Column({ name: 'node_name', type: 'varchar', length: 255 })
  nodeName: string;

  @Column({ name: 'path_length', type: 'int', nullable: true })
  pathLength: number;
}
