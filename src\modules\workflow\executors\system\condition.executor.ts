import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Condition Node Executor
 * Evaluates conditional expressions for workflow branching
 */
@Injectable()
export class ConditionExecutor extends BaseNodeExecutor {
  readonly type = 'system.if.condition';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Condition';
  readonly description = 'Evaluate conditional expressions for workflow branching';

  /**
   * Execute condition evaluation
   * @param inputs - Condition configuration and variables
   * @param context - Execution context
   * @returns Condition evaluation result
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      condition,
      variables = {},
      operator = 'javascript',
      trueValue = true,
      falseValue = false,
      defaultValue = false,
    } = inputs;

    this.logger.debug(`Evaluating condition: ${condition}`);

    try {
      let result: boolean;

      switch (operator) {
        case 'javascript':
          result = await this.evaluateJavaScriptCondition(condition, variables, context);
          break;
        case 'simple':
          result = await this.evaluateSimpleCondition(condition, variables);
          break;
        case 'jsonpath':
          result = await this.evaluateJsonPathCondition(condition, variables);
          break;
        default:
          throw new Error(`Unsupported condition operator: ${operator}`);
      }

      const output = {
        result,
        condition,
        variables,
        operator,
        value: result ? trueValue : falseValue,
        evaluatedAt: new Date().toISOString(),
        metadata: {
          executionPath: result ? 'true' : 'false',
          conditionType: operator,
        },
      };

      this.logger.debug(`Condition evaluated to: ${result}`);
      return output;

    } catch (error) {
      this.logger.error(`Condition evaluation failed: ${error.message}`);
      
      return {
        result: defaultValue,
        condition,
        variables,
        operator,
        value: defaultValue ? trueValue : falseValue,
        error: error.message,
        evaluatedAt: new Date().toISOString(),
        metadata: {
          executionPath: 'error',
          conditionType: operator,
          fallbackUsed: true,
        },
      };
    }
  }

  /**
   * Evaluate JavaScript-based condition
   * @private
   */
  private async evaluateJavaScriptCondition(
    condition: string,
    variables: any,
    context: ExecutionContext
  ): Promise<boolean> {
    // Create safe evaluation context
    const evalContext = {
      ...variables,
      // Add context variables
      execution: {
        id: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
        startTime: context.startTime,
      },
      // Add utility functions
      utils: {
        now: () => Date.now(),
        today: () => new Date().toISOString().split('T')[0],
        isEmpty: (value: any) => value == null || value === '' || (Array.isArray(value) && value.length === 0),
        isNotEmpty: (value: any) => !this.isEmpty(value),
        contains: (str: string, substr: string) => str && str.includes(substr),
        startsWith: (str: string, prefix: string) => str && str.startsWith(prefix),
        endsWith: (str: string, suffix: string) => str && str.endsWith(suffix),
        length: (value: any) => value?.length || 0,
        type: (value: any) => typeof value,
      },
    };

    // Sanitize condition to prevent dangerous operations
    const sanitizedCondition = this.sanitizeJavaScriptCondition(condition);

    try {
      // Use Function constructor for safer evaluation than eval()
      const func = new Function(...Object.keys(evalContext), `return (${sanitizedCondition});`);
      const result = func(...Object.values(evalContext));
      
      return Boolean(result);
    } catch (error) {
      throw new Error(`JavaScript condition evaluation failed: ${error.message}`);
    }
  }

  /**
   * Evaluate simple comparison condition
   * @private
   */
  private async evaluateSimpleCondition(condition: string, variables: any): Promise<boolean> {
    // Parse simple conditions like "user.age > 18" or "status == 'active'"
    const operators = ['>=', '<=', '!=', '==', '>', '<', 'contains', 'startsWith', 'endsWith'];
    
    let operator: string | undefined;
    let left: string | undefined;
    let right: string | undefined;

    // Find the operator
    for (const op of operators) {
      if (condition.includes(op)) {
        const parts = condition.split(op).map(p => p.trim());
        if (parts.length === 2) {
          operator = op;
          left = parts[0];
          right = parts[1];
          break;
        }
      }
    }

    if (!operator) {
      throw new Error(`No valid operator found in condition: ${condition}`);
    }

    // Resolve values
    const leftValue = this.resolveValue(left, variables);
    const rightValue = this.resolveValue(right, variables);

    // Evaluate based on operator
    switch (operator) {
      case '==':
        return leftValue == rightValue;
      case '!=':
        return leftValue != rightValue;
      case '>':
        return Number(leftValue) > Number(rightValue);
      case '<':
        return Number(leftValue) < Number(rightValue);
      case '>=':
        return Number(leftValue) >= Number(rightValue);
      case '<=':
        return Number(leftValue) <= Number(rightValue);
      case 'contains':
        return String(leftValue).includes(String(rightValue));
      case 'startsWith':
        return String(leftValue).startsWith(String(rightValue));
      case 'endsWith':
        return String(leftValue).endsWith(String(rightValue));
      default:
        throw new Error(`Unsupported operator: ${operator}`);
    }
  }

  /**
   * Evaluate JSONPath-based condition
   * @private
   */
  private async evaluateJsonPathCondition(condition: string, variables: any): Promise<boolean> {
    // Simple JSONPath evaluation (basic implementation)
    // For production, consider using a proper JSONPath library
    try {
      const path = condition.replace(/^\$\./, '');
      const value = this.getNestedValue(variables, path);
      return Boolean(value);
    } catch (error) {
      throw new Error(`JSONPath condition evaluation failed: ${error.message}`);
    }
  }

  /**
   * Resolve variable value from path
   * @private
   */
  private resolveValue(path: string | undefined, variables: any): any {
    // If path is undefined, return null
    if (path === undefined) {
      return null;
    }

    // Remove quotes if present
    if ((path.startsWith('"') && path.endsWith('"')) || (path.startsWith("'") && path.endsWith("'"))) {
      return path.slice(1, -1);
    }

    // Check if it's a number
    if (!isNaN(Number(path))) {
      return Number(path);
    }

    // Check if it's a boolean
    if (path === 'true') return true;
    if (path === 'false') return false;
    if (path === 'null') return null;

    // Resolve from variables
    return this.getNestedValue(variables, path);
  }

  /**
   * Get nested value from object using dot notation
   * @private
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Sanitize JavaScript condition to prevent dangerous operations
   * @private
   */
  private sanitizeJavaScriptCondition(condition: string): string {
    // Remove potentially dangerous patterns
    const dangerousPatterns = [
      /require\s*\(/g,
      /import\s+/g,
      /eval\s*\(/g,
      /Function\s*\(/g,
      /setTimeout\s*\(/g,
      /setInterval\s*\(/g,
      /process\./g,
      /global\./g,
      /window\./g,
      /document\./g,
      /__proto__/g,
      /constructor/g,
    ];

    let sanitized = condition;
    dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Check if value is empty
   * @private
   */
  private isEmpty(value: any): boolean {
    return value == null || value === '' || (Array.isArray(value) && value.length === 0);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['condition'],
      properties: {
        condition: {
          type: 'string',
          description: 'Condition expression to evaluate',
          examples: [
            'user.age > 18',
            'status == "active"',
            'items.length >= 1',
            'user.plan == "premium" && user.verified == true',
          ],
        },
        variables: {
          type: 'object',
          description: 'Variables available for condition evaluation',
          default: {},
        },
        operator: {
          type: 'string',
          enum: ['javascript', 'simple', 'jsonpath'],
          default: 'javascript',
          description: 'Condition evaluation method',
        },
        trueValue: {
          description: 'Value to return when condition is true',
          default: true,
        },
        falseValue: {
          description: 'Value to return when condition is false',
          default: false,
        },
        defaultValue: {
          type: 'boolean',
          default: false,
          description: 'Default value when condition evaluation fails',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        result: {
          type: 'boolean',
          description: 'Condition evaluation result',
        },
        condition: {
          type: 'string',
          description: 'Original condition expression',
        },
        variables: {
          type: 'object',
          description: 'Variables used in evaluation',
        },
        operator: {
          type: 'string',
          description: 'Evaluation method used',
        },
        value: {
          description: 'Resolved value based on condition result',
        },
        evaluatedAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when condition was evaluated',
        },
        error: {
          type: 'string',
          description: 'Error message if evaluation failed',
        },
        metadata: {
          type: 'object',
          properties: {
            executionPath: {
              type: 'string',
              enum: ['true', 'false', 'error'],
              description: 'Which execution path was taken',
            },
            conditionType: {
              type: 'string',
              description: 'Type of condition evaluation used',
            },
            fallbackUsed: {
              type: 'boolean',
              description: 'Whether fallback value was used due to error',
            },
          },
        },
      },
    };
  }
}
