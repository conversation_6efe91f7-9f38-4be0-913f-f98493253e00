import { IsBoolean, IsArray, IsString, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Shared validation DTOs between BE App and BE Worker
 * Synced with BE-003 validation service for consistency
 */

/**
 * Validation error DTO - synced với BE-003
 */
export class ValidationErrorDto {
  @IsString()
  code: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsString()
  severity: 'error' | 'warning';

  @IsOptional()
  details?: any;
}

/**
 * Validation warning DTO - synced với BE-003
 */
export class ValidationWarningDto {
  @IsString()
  code: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsString()
  suggestion?: string;
}

/**
 * Validation result DTO - synced với BE-003
 */
export class ValidationResultDto {
  @IsBoolean()
  isValid: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationErrorDto)
  errors: ValidationErrorDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidationWarningDto)
  warnings: ValidationWarningDto[];
}

/**
 * Node validation request DTO for Worker
 */
export class NodeValidationRequestDto {
  @IsString()
  nodeType: string;

  @IsString()
  nodeId: string;

  @IsOptional()
  inputs?: Record<string, any>;

  @IsOptional()
  config?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  validateInputs?: boolean;

  @IsOptional()
  @IsBoolean()
  validateConfig?: boolean;
}

/**
 * Workflow validation request DTO for Worker
 */
export class WorkflowValidationRequestDto {
  @IsString()
  workflowId: string;

  @IsOptional()
  definition?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  validateNodes?: boolean;

  @IsOptional()
  @IsBoolean()
  validateEdges?: boolean;

  @IsOptional()
  @IsBoolean()
  validateExecution?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  nodeTypesToValidate?: string[];
}

/**
 * Bulk validation request DTO for Worker
 */
export class BulkValidationRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NodeValidationRequestDto)
  nodeValidations?: NodeValidationRequestDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowValidationRequestDto)
  workflowValidations?: WorkflowValidationRequestDto[];

  @IsOptional()
  @IsBoolean()
  stopOnFirstError?: boolean;

  @IsOptional()
  @IsBoolean()
  includeWarnings?: boolean;
}

/**
 * Validation performance metrics DTO
 */
export class ValidationPerformanceDto {
  @IsString()
  validationType: string;

  @IsString()
  targetId: string;

  @IsString()
  startTime: number;

  @IsString()
  endTime: number;

  @IsString()
  duration: number;

  @IsOptional()
  @IsString()
  memoryUsage?: number;

  @IsOptional()
  @IsString()
  cacheHit?: boolean;

  @IsOptional()
  metadata?: {
    nodeCount?: number;
    edgeCount?: number;
    validationRules?: number;
    complexityScore?: number;
  };
}

/**
 * Comprehensive validation result với performance metrics
 */
export class ComprehensiveValidationResultDto extends ValidationResultDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => ValidationPerformanceDto)
  performance?: ValidationPerformanceDto;

  @IsOptional()
  @IsArray()
  validatedItems?: Array<{
    type: string;
    id: string;
    isValid: boolean;
    errorCount: number;
    warningCount: number;
  }>;

  @IsOptional()
  summary?: {
    totalItems: number;
    validItems: number;
    invalidItems: number;
    totalErrors: number;
    totalWarnings: number;
    validationTime: number;
  };
}
