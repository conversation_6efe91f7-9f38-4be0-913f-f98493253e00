import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleAnalyticsService } from '../../../../../shared/services/google/analytics/google-analytics.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Analytics Run Report node
 */
export interface GoogleAnalyticsRunReportInput {
  /**
   * Access token cho Google Analytics API
   */
  accessToken: string;

  /**
   * Property ID của Google Analytics
   */
  propertyId: string;

  /**
   * Dimensions để group data
   */
  dimensions?: Array<{
    name: string;
  }>;

  /**
   * Metrics để đo lường
   */
  metrics: Array<{
    name: string;
  }>;

  /**
   * Date ranges
   */
  dateRanges: Array<{
    startDate: string;
    endDate: string;
    name?: string;
  }>;

  /**
   * Dimension filter
   */
  dimensionFilter?: {
    filter?: {
      fieldName: string;
      stringFilter?: {
        matchType: 'EXACT' | 'BEGINS_WITH' | 'ENDS_WITH' | 'CONTAINS' | 'FULL_REGEXP' | 'PARTIAL_REGEXP';
        value: string;
        caseSensitive?: boolean;
      };
      inListFilter?: {
        values: string[];
        caseSensitive?: boolean;
      };
      numericFilter?: {
        operation: 'EQUAL' | 'LESS_THAN' | 'LESS_THAN_OR_EQUAL' | 'GREATER_THAN' | 'GREATER_THAN_OR_EQUAL' | 'BETWEEN';
        value: {
          int64Value?: string;
          doubleValue?: number;
        };
      };
    };
    andGroup?: {
      expressions: any[];
    };
    orGroup?: {
      expressions: any[];
    };
    notExpression?: any;
  };

  /**
   * Metric filter
   */
  metricFilter?: any;

  /**
   * Order by
   */
  orderBys?: Array<{
    metric?: {
      metricName: string;
    };
    dimension?: {
      dimensionName: string;
      orderType?: 'ALPHANUMERIC' | 'CASE_INSENSITIVE_ALPHANUMERIC' | 'NUMERIC';
    };
    desc?: boolean;
  }>;

  /**
   * Limit số lượng rows
   */
  limit?: number;

  /**
   * Offset
   */
  offset?: number;

  /**
   * Currency code
   */
  currencyCode?: string;

  /**
   * Keep empty rows
   */
  keepEmptyRows?: boolean;
}

/**
 * Output schema cho Google Analytics Run Report node
 */
export interface GoogleAnalyticsRunReportOutput {
  /**
   * Dimension headers
   */
  dimensionHeaders: Array<{
    name: string;
  }>;

  /**
   * Metric headers
   */
  metricHeaders: Array<{
    name: string;
    type: string;
  }>;

  /**
   * Rows data
   */
  rows: Array<{
    dimensionValues: Array<{
      value: string;
    }>;
    metricValues: Array<{
      value: string;
    }>;
  }>;

  /**
   * Row count
   */
  rowCount: number;

  /**
   * Metadata
   */
  metadata: {
    propertyId: string;
    dateRanges: Array<{
      startDate: string;
      endDate: string;
      name?: string;
    }>;
    currencyCode?: string;
    timeZone?: string;
    samplingMetadatas?: any[];
  };

  /**
   * Totals
   */
  totals?: Array<{
    dimensionValues: Array<{
      value: string;
    }>;
    metricValues: Array<{
      value: string;
    }>;
  }>;

  /**
   * Maximums
   */
  maximums?: Array<{
    dimensionValues: Array<{
      value: string;
    }>;
    metricValues: Array<{
      value: string;
    }>;
  }>;

  /**
   * Minimums
   */
  minimums?: Array<{
    dimensionValues: Array<{
      value: string;
    }>;
    metricValues: Array<{
      value: string;
    }>;
  }>;
}

/**
 * Executor để chạy Google Analytics report
 */
@Injectable()
export class GoogleAnalyticsRunReportExecutor implements NodeExecutor {
  private readonly logger = new Logger(GoogleAnalyticsRunReportExecutor.name);

  constructor(private readonly analyticsService: GoogleAnalyticsService) {}

  /**
   * Thực thi node chạy Analytics report
   */
  async execute(
    inputs: GoogleAnalyticsRunReportInput,
    context: ExecutionContext,
  ): Promise<NodeExecutionResult> {
    try {
      this.logger.log(`Running Analytics report for property: ${inputs.propertyId}`);

      // Validate required inputs
      if (!inputs.accessToken) {
        throw new Error('Access token is required');
      }

      if (!inputs.propertyId) {
        throw new Error('Property ID is required');
      }

      if (!inputs.metrics || inputs.metrics.length === 0) {
        throw new Error('At least one metric is required');
      }

      if (!inputs.dateRanges || inputs.dateRanges.length === 0) {
        throw new Error('At least one date range is required');
      }

      // Prepare request
      const request = {
        property: inputs.propertyId,
        dimensions: inputs.dimensions,
        metrics: inputs.metrics,
        dateRanges: inputs.dateRanges,
        dimensionFilter: inputs.dimensionFilter,
        metricFilter: inputs.metricFilter,
        orderBys: inputs.orderBys,
        limit: inputs.limit?.toString(),
        offset: inputs.offset?.toString(),
        currencyCode: inputs.currencyCode,
        keepEmptyRows: inputs.keepEmptyRows,
      };

      // Run report
      const reportResult = await this.analyticsService.runReport(
        request,
        inputs.accessToken,
      );

      // Prepare output
      const output: GoogleAnalyticsRunReportOutput = {
        dimensionHeaders: reportResult.dimensionHeaders || [],
        metricHeaders: reportResult.metricHeaders || [],
        rows: reportResult.rows || [],
        rowCount: reportResult.rowCount || 0,
        metadata: {
          propertyId: inputs.propertyId,
          dateRanges: inputs.dateRanges,
          currencyCode: inputs.currencyCode,
          timeZone: reportResult.metadata?.timeZone,
          samplingMetadatas: reportResult.metadata?.samplingMetadatas,
        },
        totals: reportResult.totals,
        maximums: reportResult.maximums,
        minimums: reportResult.minimums,
      };

      this.logger.log(`Analytics report completed with ${output.rowCount} rows`);

      return {
        success: true,
        output,
        metadata: {
          nodeType: 'google_analytics_run_report',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(`Error running Analytics report: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        errorDetails: error,
        metadata: {
          nodeType: 'google_analytics_run_report',
          executionTime: Date.now() - context.startTime,
          timestamp: Date.now(),
        },
      };
    }
  }
}
