import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để cập nhật dữ liệu trong Google Sheets
 */
@Injectable()
export class GoogleSheetsAppendRowExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.appendRow';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Append Row to Google Sheets';
  readonly description = 'Thêm hàng mới vào Google Sheets';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node cập nhật dữ liệu Google Sheets
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const { 
        spreadsheetId, 
        range, 
        values, 
        valueInputOption = 'USER_ENTERED',
        append = false 
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (!range) {
        throw new Error('Range is required');
      }

      if (!values || !Array.isArray(values)) {
        throw new Error('Values must be an array');
      }

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Update or append data using GoogleSheetsService
      let result;
      if (append) {
        result = await this.googleSheetsService.appendData(
          accessToken,
          spreadsheetId,
          range,
          values,
          valueInputOption as 'RAW' | 'USER_ENTERED'
        );
      } else {
        result = await this.googleSheetsService.writeData(
          accessToken,
          spreadsheetId,
          range,
          values,
          valueInputOption as 'RAW' | 'USER_ENTERED'
        );
      }

      // Return formatted result
      return {
        spreadsheetId,
        range: result.updatedRange,
        updatedRows: result.updatedRows,
        updatedColumns: result.updatedColumns,
        updatedCells: result.updatedCells,
        operation: append ? 'append' : 'update',
        valueInputOption,
        updatedAt: new Date().toISOString(),
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (!inputs.range || typeof inputs.range !== 'string') {
      throw new Error('Range must be a non-empty string');
    }

    if (!inputs.values || !Array.isArray(inputs.values)) {
      throw new Error('Values must be an array');
    }

    // Validate values array structure
    inputs.values.forEach((row: any, rowIndex: number) => {
      if (!Array.isArray(row)) {
        throw new Error(`Row ${rowIndex} must be an array`);
      }
    });

    if (inputs.valueInputOption && !['RAW', 'USER_ENTERED'].includes(inputs.valueInputOption)) {
      throw new Error('Value input option must be either "RAW" or "USER_ENTERED"');
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        range: {
          type: 'string',
          description: 'Phạm vi cells để cập nhật (ví dụ: "Sheet1!A1:C3")',
          pattern: '^[^!]+![A-Z]+[0-9]+:[A-Z]+[0-9]+$',
        },
        values: {
          type: 'array',
          description: 'Dữ liệu để ghi vào cells (mảng 2 chiều)',
          items: {
            type: 'array',
            items: {
              oneOf: [
                { type: 'string' },
                { type: 'number' },
                { type: 'boolean' },
              ],
            },
          },
          minItems: 1,
        },
        valueInputOption: {
          type: 'string',
          description: 'Cách xử lý dữ liệu đầu vào',
          enum: ['RAW', 'USER_ENTERED'],
          default: 'USER_ENTERED',
        },
        append: {
          type: 'boolean',
          description: 'Thêm dữ liệu vào cuối thay vì ghi đè',
          default: false,
        },
      },
      required: ['spreadsheetId', 'range', 'values'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet',
        },
        range: {
          type: 'string',
          description: 'Phạm vi đã được cập nhật',
        },
        updatedRows: {
          type: 'number',
          description: 'Số hàng đã cập nhật',
        },
        updatedColumns: {
          type: 'number',
          description: 'Số cột đã cập nhật',
        },
        updatedCells: {
          type: 'number',
          description: 'Số cells đã cập nhật',
        },
        operation: {
          type: 'string',
          description: 'Loại thao tác đã thực hiện',
          enum: ['update', 'append'],
        },
        valueInputOption: {
          type: 'string',
          description: 'Cách xử lý dữ liệu đã sử dụng',
        },
        updatedAt: {
          type: 'string',
          description: 'Thời gian cập nhật',
        },
      },
    };
  }
}
