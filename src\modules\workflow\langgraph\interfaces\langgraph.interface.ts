import { BaseMessage } from '@langchain/core/messages';

/**
 * Workflow state interface for LangGraph
 * Represents the complete state of a workflow execution
 */
export interface WorkflowState {
  // Core execution identifiers
  executionId: string;
  workflowId: string;
  userId: number;

  // Execution status
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  endTime?: number;

  // Current execution state
  currentNode: string | null;
  nextNodes: string[];
  completedNodes: string[];
  failedNodes: string[];

  // Data and variables
  nodeResults: Record<string, any>;
  variables: Record<string, any>;
  triggerData: any;
  triggerType: 'manual' | 'webhook' | 'schedule' | 'api';

  // Error handling
  errors: WorkflowError[];
  lastError: WorkflowError | null;

  // LangGraph compatibility
  messages: BaseMessage[];

  // Metadata
  metadata?: Record<string, any>;
}

/**
 * Workflow error interface
 */
export interface WorkflowError {
  nodeId: string;
  nodeType: string;
  error: string;
  timestamp: number;
  stack?: string;
  recoverable?: boolean;
}

/**
 * Configuration for workflow graph building
 */
export interface WorkflowGraphConfig {
  // Entry and exit configuration
  entryNodeId?: string;
  exitNodeId?: string;

  // Execution behavior
  stopOnError?: boolean;
  maxExecutionTime?: number;
  enableParallelExecution?: boolean;

  // State management
  customChannels?: Record<string, any>;
  persistState?: boolean;
  stateCheckpoints?: boolean;

  // Testing and debugging
  isTest?: boolean;
  enableDebugLogging?: boolean;
  captureIntermediateStates?: boolean;

  // Performance
  maxConcurrentNodes?: number;
  nodeTimeout?: number;

  // Event handling
  enableEvents?: boolean;
  eventChannels?: string[];
}

/**
 * Workflow execution options
 */
export interface WorkflowExecutionOptions {
  // Input data
  triggerData?: any;
  triggerType?: 'manual' | 'webhook' | 'schedule' | 'api';
  userId?: number;
  variables?: Record<string, any>;

  // Execution control
  timeout?: number;
  stopOnError?: boolean;
  enableParallel?: boolean;

  // State management
  checkpointId?: string;
  resumeFromCheckpoint?: boolean;
  saveCheckpoints?: boolean;

  // Monitoring
  enableSSE?: boolean;
  progressCallback?: (state: WorkflowState) => void;
  errorCallback?: (error: WorkflowError) => void;

  // Testing
  isTest?: boolean;
  mockNodes?: string[];
  dryRun?: boolean;
}

/**
 * Workflow execution result
 */
export interface WorkflowExecutionResult {
  // Execution metadata
  executionId: string;
  workflowId: string;
  status: 'completed' | 'failed' | 'cancelled';
  
  // Timing
  startTime: number;
  endTime: number;
  duration: number;

  // Results
  output: any;
  nodeResults: Record<string, any>;
  finalState: WorkflowState;

  // Statistics
  stats: {
    totalNodes: number;
    completedNodes: number;
    failedNodes: number;
    skippedNodes: number;
    executionTime: number;
    memoryUsage?: number;
  };

  // Errors
  errors: WorkflowError[];
  lastError?: WorkflowError;

  // Metadata
  metadata: {
    version: string;
    environment: string;
    checkpoints?: string[];
    events?: any[];
  };
}

/**
 * Node execution context for LangGraph integration
 */
export interface LangGraphNodeContext {
  // Node information
  nodeId: string;
  nodeType: string;
  nodeConfig: any;

  // Execution state
  state: WorkflowState;
  previousState?: WorkflowState;

  // Utilities
  logger: any;
  eventEmitter?: any;
  checkpointManager?: any;

  // Execution control
  shouldStop: () => boolean;
  reportProgress: (progress: number) => void;
  emitEvent: (event: string, data: any) => void;
}

/**
 * Graph checkpoint interface
 */
export interface WorkflowCheckpoint {
  id: string;
  executionId: string;
  workflowId: string;
  timestamp: number;
  state: WorkflowState;
  nodeId: string;
  metadata: {
    version: string;
    checkpointType: 'auto' | 'manual' | 'error';
    description?: string;
  };
}

/**
 * Graph execution statistics
 */
export interface GraphExecutionStats {
  // Timing
  totalExecutionTime: number;
  averageNodeTime: number;
  slowestNode: {
    nodeId: string;
    duration: number;
  };
  fastestNode: {
    nodeId: string;
    duration: number;
  };

  // Performance
  memoryUsage: {
    peak: number;
    average: number;
    final: number;
  };
  
  // Execution flow
  nodeExecutionOrder: string[];
  parallelExecutions: number;
  retryAttempts: number;

  // Error statistics
  errorRate: number;
  errorsByType: Record<string, number>;
  recoveryAttempts: number;

  // State management
  stateSize: number;
  checkpointCount: number;
  stateUpdates: number;
}

/**
 * LangGraph integration configuration
 */
export interface LangGraphIntegrationConfig {
  // Core settings
  enableCheckpoints: boolean;
  checkpointStorage: 'memory' | 'redis' | 'postgres';
  maxCheckpoints: number;

  // Performance
  maxConcurrentExecutions: number;
  executionTimeout: number;
  nodeTimeout: number;

  // Monitoring
  enableMetrics: boolean;
  enableTracing: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';

  // Error handling
  defaultErrorStrategy: 'stop' | 'continue' | 'retry';
  maxRetryAttempts: number;
  retryDelay: number;

  // State management
  stateCompression: boolean;
  statePersistence: boolean;
  stateCleanupInterval: number;
}

/**
 * Workflow graph metadata
 */
export interface WorkflowGraphMetadata {
  // Graph structure
  nodeCount: number;
  edgeCount: number;
  entryNodes: string[];
  exitNodes: string[];
  
  // Complexity metrics
  cyclomaticComplexity: number;
  maxDepth: number;
  parallelPaths: number;
  
  // Validation
  isValid: boolean;
  validationErrors: string[];
  validationWarnings: string[];
  
  // Performance estimates
  estimatedExecutionTime: number;
  estimatedMemoryUsage: number;
  
  // Dependencies
  requiredExecutors: string[];
  externalDependencies: string[];
  
  // Versioning
  version: string;
  lastModified: number;
  checksum: string;
}

/**
 * Node execution result for LangGraph
 */
export interface LangGraphNodeResult {
  // Execution status
  success: boolean;
  nodeId: string;
  nodeType: string;
  
  // Output data
  output: any;
  
  // Timing
  startTime: number;
  endTime: number;
  duration: number;
  
  // State changes
  stateUpdates: Partial<WorkflowState>;
  variableUpdates: Record<string, any>;
  
  // Error information
  error?: {
    message: string;
    code: string;
    stack?: string;
    recoverable: boolean;
  };
  
  // Metadata
  metadata: {
    executorVersion: string;
    memoryUsage: number;
    retryAttempt?: number;
    checkpointCreated?: boolean;
  };
}

/**
 * Workflow event for LangGraph integration
 */
export interface WorkflowEvent {
  // Event identification
  id: string;
  type: string;
  timestamp: number;
  
  // Context
  executionId: string;
  workflowId: string;
  nodeId?: string;
  
  // Event data
  data: any;
  
  // Metadata
  metadata: {
    source: 'langgraph' | 'executor' | 'system';
    version: string;
    correlationId?: string;
  };
}
