/**
 * @file Shared Condition Evaluation Logic
 * 
 * Interface và logic chung cho condition evaluation được sử dụng bởi:
 * - If Condition node
 * - Switch node  
 * - Và các logic nodes khác
 * 
 * Tránh duplicate code và đảm bảo consistent behavior
 */

// =================================================================
// SECTION 1: SHARED ENUMS & TYPES
// =================================================================

/**
 * Comparison operators - Shared giữa If và Switch nodes
 */
export enum EComparisonOperator {
    // Universal operators (áp dụng cho tất cả data types)
    EQUALS = 'equals',
    NOT_EQUALS = 'not_equals',
    IS_NULL = 'is_null',
    IS_NOT_NULL = 'is_not_null',
    
    // Number & Date operators
    GREATER_THAN = 'greater_than',
    GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
    LESS_THAN = 'less_than',
    LESS_THAN_OR_EQUAL = 'less_than_or_equal',
    BETWEEN = 'between',
    NOT_BETWEEN = 'not_between',
    
    // String operators
    CONTAINS = 'contains',
    NOT_CONTAINS = 'not_contains',
    STARTS_WITH = 'starts_with',
    ENDS_WITH = 'ends_with',
    REGEX_MATCH = 'regex_match',
    LENGTH_EQUALS = 'length_equals',
    LENGTH_GREATER_THAN = 'length_greater_than',
    LENGTH_LESS_THAN = 'length_less_than',
    
    // Boolean operators
    IS_TRUE = 'is_true',
    IS_FALSE = 'is_false',
    
    // Array operators
    ARRAY_CONTAINS = 'array_contains',
    ARRAY_NOT_CONTAINS = 'array_not_contains',
    ARRAY_LENGTH_EQUALS = 'array_length_equals',
    ARRAY_LENGTH_GREATER_THAN = 'array_length_greater_than',
    ARRAY_LENGTH_LESS_THAN = 'array_length_less_than',
    ARRAY_ALL_MATCH = 'array_all_match',
    ARRAY_ANY_MATCH = 'array_any_match',
    IN_ARRAY = 'in_array', // Value có trong array values
    
    // Object operators
    HAS_PROPERTY = 'has_property',
    NOT_HAS_PROPERTY = 'not_has_property',
    PROPERTY_EQUALS = 'property_equals',
    PROPERTY_NOT_EQUALS = 'property_not_equals',
    
    // Date specific operators
    DATE_IS_BEFORE = 'date_is_before',
    DATE_IS_AFTER = 'date_is_after',
    DATE_IS_TODAY = 'date_is_today',
    DATE_IS_YESTERDAY = 'date_is_yesterday',
    DATE_IS_TOMORROW = 'date_is_tomorrow',
    DATE_IS_THIS_WEEK = 'date_is_this_week',
    DATE_IS_THIS_MONTH = 'date_is_this_month',
    DATE_IS_THIS_YEAR = 'date_is_this_year',
    DATE_DAYS_AGO = 'date_days_ago',
    DATE_DAYS_FROM_NOW = 'date_days_from_now',
    
    // Empty/Length checks (universal)
    IS_EMPTY = 'is_empty',
    IS_NOT_EMPTY = 'is_not_empty',
}

/**
 * Data types cho comparison - Shared giữa các nodes
 */
export enum EDataType {
    STRING = 'string',
    NUMBER = 'number',
    BOOLEAN = 'boolean',
    DATE = 'date',
    DATETIME = 'datetime',
    TIME = 'time',
    ARRAY = 'array',
    OBJECT = 'object'
}

// =================================================================
// SECTION 2: SHARED CONDITION INTERFACE
// =================================================================

/**
 * Single condition - Shared interface cho tất cả logic nodes
 */
export interface ICondition {
    /** Tên field để so sánh (ví dụ: "data.user.status", "variables.count") */
    field: string;
    
    /** Operator so sánh */
    operator: EComparisonOperator;
    
    /** Giá trị để so sánh (single value) */
    value?: any;
    
    /** Multiple values (cho BETWEEN, IN_ARRAY operators) */
    values?: any[];
    
    /** Data type của field */
    data_type: EDataType;
    
    /** Có case sensitive không (cho string) */
    case_sensitive?: boolean;
    
    /** Additional parameters cho specific operators */
    additional_params?: {
        /** Property name cho object operators */
        property_name?: string;
        
        /** Pattern cho array matching */
        match_pattern?: any;
        
        /** Number of days cho date operators */
        days_count?: number;
        
        /** Date format cho date parsing */
        date_format?: string;
        
        /** Regex flags cho regex matching */
        regex_flags?: string;
    };
}

/**
 * Condition evaluation result
 */
export interface IConditionResult {
    /** Kết quả evaluation (true/false) */
    result: boolean;
    
    /** Field value thực tế được so sánh */
    actual_value: any;
    
    /** Expected value từ condition */
    expected_value: any;
    
    /** Thời gian evaluation (milliseconds) */
    evaluation_time: number;
    
    /** Error message nếu có lỗi */
    error?: string;
}

// =================================================================
// SECTION 3: SHARED HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để lấy operators phù hợp cho từng data type
 */
export function getOperatorsForDataType(dataType: EDataType): EComparisonOperator[] {
    const universalOperators = [
        EComparisonOperator.EQUALS,
        EComparisonOperator.NOT_EQUALS,
        EComparisonOperator.IS_NULL,
        EComparisonOperator.IS_NOT_NULL,
        EComparisonOperator.IS_EMPTY,
        EComparisonOperator.IS_NOT_EMPTY
    ];

    switch (dataType) {
        case EDataType.STRING:
            return [
                ...universalOperators,
                EComparisonOperator.CONTAINS,
                EComparisonOperator.NOT_CONTAINS,
                EComparisonOperator.STARTS_WITH,
                EComparisonOperator.ENDS_WITH,
                EComparisonOperator.REGEX_MATCH,
                EComparisonOperator.LENGTH_EQUALS,
                EComparisonOperator.LENGTH_GREATER_THAN,
                EComparisonOperator.LENGTH_LESS_THAN
            ];

        case EDataType.NUMBER:
            return [
                ...universalOperators,
                EComparisonOperator.GREATER_THAN,
                EComparisonOperator.GREATER_THAN_OR_EQUAL,
                EComparisonOperator.LESS_THAN,
                EComparisonOperator.LESS_THAN_OR_EQUAL,
                EComparisonOperator.BETWEEN,
                EComparisonOperator.NOT_BETWEEN
            ];

        case EDataType.BOOLEAN:
            return [
                EComparisonOperator.EQUALS,
                EComparisonOperator.NOT_EQUALS,
                EComparisonOperator.IS_TRUE,
                EComparisonOperator.IS_FALSE,
                EComparisonOperator.IS_NULL,
                EComparisonOperator.IS_NOT_NULL
            ];

        case EDataType.DATE:
        case EDataType.DATETIME:
        case EDataType.TIME:
            return [
                ...universalOperators,
                EComparisonOperator.GREATER_THAN,
                EComparisonOperator.GREATER_THAN_OR_EQUAL,
                EComparisonOperator.LESS_THAN,
                EComparisonOperator.LESS_THAN_OR_EQUAL,
                EComparisonOperator.BETWEEN,
                EComparisonOperator.NOT_BETWEEN,
                EComparisonOperator.DATE_IS_BEFORE,
                EComparisonOperator.DATE_IS_AFTER,
                EComparisonOperator.DATE_IS_TODAY,
                EComparisonOperator.DATE_IS_YESTERDAY,
                EComparisonOperator.DATE_IS_TOMORROW,
                EComparisonOperator.DATE_IS_THIS_WEEK,
                EComparisonOperator.DATE_IS_THIS_MONTH,
                EComparisonOperator.DATE_IS_THIS_YEAR,
                EComparisonOperator.DATE_DAYS_AGO,
                EComparisonOperator.DATE_DAYS_FROM_NOW
            ];

        case EDataType.ARRAY:
            return [
                ...universalOperators,
                EComparisonOperator.ARRAY_CONTAINS,
                EComparisonOperator.ARRAY_NOT_CONTAINS,
                EComparisonOperator.ARRAY_LENGTH_EQUALS,
                EComparisonOperator.ARRAY_LENGTH_GREATER_THAN,
                EComparisonOperator.ARRAY_LENGTH_LESS_THAN,
                EComparisonOperator.ARRAY_ALL_MATCH,
                EComparisonOperator.ARRAY_ANY_MATCH,
                EComparisonOperator.IN_ARRAY
            ];

        case EDataType.OBJECT:
            return [
                ...universalOperators,
                EComparisonOperator.HAS_PROPERTY,
                EComparisonOperator.NOT_HAS_PROPERTY,
                EComparisonOperator.PROPERTY_EQUALS,
                EComparisonOperator.PROPERTY_NOT_EQUALS
            ];

        default:
            return universalOperators;
    }
}

/**
 * Helper function để kiểm tra operator có cần value input không
 */
export function operatorNeedsValue(operator: EComparisonOperator): boolean {
    const noValueOperators = [
        EComparisonOperator.IS_NULL,
        EComparisonOperator.IS_NOT_NULL,
        EComparisonOperator.IS_EMPTY,
        EComparisonOperator.IS_NOT_EMPTY,
        EComparisonOperator.IS_TRUE,
        EComparisonOperator.IS_FALSE,
        EComparisonOperator.DATE_IS_TODAY,
        EComparisonOperator.DATE_IS_YESTERDAY,
        EComparisonOperator.DATE_IS_TOMORROW,
        EComparisonOperator.DATE_IS_THIS_WEEK,
        EComparisonOperator.DATE_IS_THIS_MONTH,
        EComparisonOperator.DATE_IS_THIS_YEAR
    ];
    
    return !noValueOperators.includes(operator);
}

/**
 * Helper function để kiểm tra operator có cần multiple values không
 */
export function operatorNeedsMultipleValues(operator: EComparisonOperator): boolean {
    const multiValueOperators = [
        EComparisonOperator.BETWEEN,
        EComparisonOperator.NOT_BETWEEN,
        EComparisonOperator.IN_ARRAY
    ];
    
    return multiValueOperators.includes(operator);
}

/**
 * Helper function để tạo operator options cho loadOptions API
 */
export function createOperatorOptions(dataType: EDataType): Array<{ name: string; value: EComparisonOperator; parameters?: Record<string, any> }> {
    const operators = getOperatorsForDataType(dataType);
    
    const operatorLabels: Record<EComparisonOperator, string> = {
        // Universal
        [EComparisonOperator.EQUALS]: 'Equals (=)',
        [EComparisonOperator.NOT_EQUALS]: 'Not Equals (≠)',
        [EComparisonOperator.IS_NULL]: 'Is Null',
        [EComparisonOperator.IS_NOT_NULL]: 'Is Not Null',
        [EComparisonOperator.IS_EMPTY]: 'Is Empty',
        [EComparisonOperator.IS_NOT_EMPTY]: 'Is Not Empty',
        
        // Number & Date
        [EComparisonOperator.GREATER_THAN]: 'Greater Than (>)',
        [EComparisonOperator.GREATER_THAN_OR_EQUAL]: 'Greater Than or Equal (≥)',
        [EComparisonOperator.LESS_THAN]: 'Less Than (<)',
        [EComparisonOperator.LESS_THAN_OR_EQUAL]: 'Less Than or Equal (≤)',
        [EComparisonOperator.BETWEEN]: 'Between',
        [EComparisonOperator.NOT_BETWEEN]: 'Not Between',
        
        // String
        [EComparisonOperator.CONTAINS]: 'Contains',
        [EComparisonOperator.NOT_CONTAINS]: 'Not Contains',
        [EComparisonOperator.STARTS_WITH]: 'Starts With',
        [EComparisonOperator.ENDS_WITH]: 'Ends With',
        [EComparisonOperator.REGEX_MATCH]: 'Regex Match',
        [EComparisonOperator.LENGTH_EQUALS]: 'Length Equals',
        [EComparisonOperator.LENGTH_GREATER_THAN]: 'Length Greater Than',
        [EComparisonOperator.LENGTH_LESS_THAN]: 'Length Less Than',
        
        // Boolean
        [EComparisonOperator.IS_TRUE]: 'Is True',
        [EComparisonOperator.IS_FALSE]: 'Is False',
        
        // Array
        [EComparisonOperator.ARRAY_CONTAINS]: 'Array Contains',
        [EComparisonOperator.ARRAY_NOT_CONTAINS]: 'Array Not Contains',
        [EComparisonOperator.ARRAY_LENGTH_EQUALS]: 'Array Length Equals',
        [EComparisonOperator.ARRAY_LENGTH_GREATER_THAN]: 'Array Length Greater Than',
        [EComparisonOperator.ARRAY_LENGTH_LESS_THAN]: 'Array Length Less Than',
        [EComparisonOperator.ARRAY_ALL_MATCH]: 'All Elements Match',
        [EComparisonOperator.ARRAY_ANY_MATCH]: 'Any Element Matches',
        [EComparisonOperator.IN_ARRAY]: 'In Array',
        
        // Object
        [EComparisonOperator.HAS_PROPERTY]: 'Has Property',
        [EComparisonOperator.NOT_HAS_PROPERTY]: 'Not Has Property',
        [EComparisonOperator.PROPERTY_EQUALS]: 'Property Equals',
        [EComparisonOperator.PROPERTY_NOT_EQUALS]: 'Property Not Equals',
        
        // Date specific
        [EComparisonOperator.DATE_IS_BEFORE]: 'Is Before Date',
        [EComparisonOperator.DATE_IS_AFTER]: 'Is After Date',
        [EComparisonOperator.DATE_IS_TODAY]: 'Is Today',
        [EComparisonOperator.DATE_IS_YESTERDAY]: 'Is Yesterday',
        [EComparisonOperator.DATE_IS_TOMORROW]: 'Is Tomorrow',
        [EComparisonOperator.DATE_IS_THIS_WEEK]: 'Is This Week',
        [EComparisonOperator.DATE_IS_THIS_MONTH]: 'Is This Month',
        [EComparisonOperator.DATE_IS_THIS_YEAR]: 'Is This Year',
        [EComparisonOperator.DATE_DAYS_AGO]: 'Days Ago',
        [EComparisonOperator.DATE_DAYS_FROM_NOW]: 'Days From Now'
    };
    
    return operators.map(op => ({
        name: operatorLabels[op] || op,
        value: op,
        parameters: {
            needsValue: operatorNeedsValue(op),
            needsMultipleValues: operatorNeedsMultipleValues(op),
            dataType: dataType
        }
    }));
}
