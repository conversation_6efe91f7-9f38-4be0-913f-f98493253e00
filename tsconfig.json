{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@common/*": ["src/common/*"], "@be-app/*": ["../redai-v201-be-app/src/*"], "@external-services/*": ["src/shared/services/external/*"], "@shared-services/*": ["../shared/services/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "useDefineForClassFields": false}, "exclude": ["src/modules/workflow/executors/google/ads.disabled/**/*", "src/modules/workflow/executors/google/drive.disabled/**/*"]}