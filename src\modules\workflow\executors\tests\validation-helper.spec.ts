import { Test, TestingModule } from '@nestjs/testing';
import { ValidationHelper } from '../base/validation-helper';

describe('ValidationHelper', () => {
  let helper: ValidationHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationHelper],
    }).compile();

    helper = module.get<ValidationHelper>(ValidationHelper);
  });

  describe('validateAgainstSchema()', () => {
    it('should validate correct data against schema', () => {
      const schema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number', minimum: 0 },
          email: { type: 'string', format: 'email' },
        },
        required: ['name', 'email'],
      };

      const validData = {
        name: '<PERSON>',
        age: 30,
        email: '<EMAIL>',
      };

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
    });

    it('should reject data with missing required fields', () => {
      const schema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          email: { type: 'string' },
        },
        required: ['name', 'email'],
      };

      const invalidData = {
        name: 'John Doe',
        // Missing email
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow('Missing required field: email');
    });

    it('should reject data with wrong types', () => {
      const schema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' },
        },
      };

      const invalidData = {
        name: 'John Doe',
        age: 'thirty', // Should be number
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow("Field '/age' should be number");
    });

    it('should validate format constraints', () => {
      const schema = {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          url: { type: 'string', format: 'uri' },
        },
      };

      const invalidData = {
        email: 'not-an-email',
        url: 'not-a-url',
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate numeric constraints', () => {
      const schema = {
        type: 'object',
        properties: {
          age: { type: 'number', minimum: 0, maximum: 150 },
          score: { type: 'number', minimum: 0 },
        },
      };

      const invalidData = {
        age: -5, // Below minimum
        score: 200, // Above maximum (if we add maximum to score)
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate string length constraints', () => {
      const schema = {
        type: 'object',
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 20 },
          password: { type: 'string', minLength: 8 },
        },
      };

      const invalidData = {
        username: 'ab', // Too short
        password: '123', // Too short
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate enum constraints', () => {
      const schema = {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['active', 'inactive', 'pending'] },
          priority: { type: 'number', enum: [1, 2, 3, 4, 5] },
        },
      };

      const invalidData = {
        status: 'unknown', // Not in enum
        priority: 10, // Not in enum
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate pattern constraints', () => {
      const schema = {
        type: 'object',
        properties: {
          phoneNumber: { type: 'string', pattern: '^\\+?[1-9]\\d{1,14}$' },
        },
      };

      const invalidData = {
        phoneNumber: 'abc123', // Doesn't match pattern
      };

      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });
  });

  describe('validateWithDetails()', () => {
    it('should return detailed validation results for valid data', () => {
      const schema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' },
        },
      };

      const validData = {
        name: 'John Doe',
        age: 30,
      };

      const result = helper.validateWithDetails(validData, schema);

      expect(result.valid).toBe(true);
      expect(result.errors).toEqual([]);
      expect(result.data).toEqual(validData);
    });

    it('should return detailed validation results for invalid data', () => {
      const schema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'number' },
        },
        required: ['name'],
      };

      const invalidData = {
        age: 'thirty', // Wrong type
        // Missing name
      };

      const result = helper.validateWithDetails(invalidData, schema);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('Missing required field: name');
      expect(result.errors.some(error => error.includes('should be number'))).toBe(true);
    });

    it('should handle schema compilation errors', () => {
      const invalidSchema = {
        type: 'invalid-type', // Invalid schema
      };

      const data = { test: 'value' };

      const result = helper.validateWithDetails(data, invalidSchema);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('Schema validation error');
    });
  });

  describe('Custom Formats', () => {
    it('should validate nodeId format', () => {
      const schema = {
        type: 'object',
        properties: {
          nodeId: { type: 'string', format: 'nodeId' },
        },
      };

      const validData = { nodeId: 'node_123' };
      const invalidData = { nodeId: 'node@123' }; // Contains invalid character

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate workflowId format (UUID)', () => {
      const schema = {
        type: 'object',
        properties: {
          workflowId: { type: 'string', format: 'workflowId' },
        },
      };

      const validData = { workflowId: '123e4567-e89b-12d3-a456-************' };
      const invalidData = { workflowId: 'not-a-uuid' };

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate expression format', () => {
      const schema = {
        type: 'object',
        properties: {
          expression: { type: 'string', format: 'expression' },
        },
      };

      const validData1 = { expression: '{{node.output}}' };
      const validData2 = { expression: 'static string' };

      expect(() => helper.validateAgainstSchema(validData1, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(validData2, schema)).not.toThrow();
    });

    it('should validate JSON format', () => {
      const schema = {
        type: 'object',
        properties: {
          jsonString: { type: 'string', format: 'json' },
        },
      };

      const validData = { jsonString: '{"key": "value"}' };
      const invalidData = { jsonString: '{invalid json}' };

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });
  });

  describe('Schema Creation Helpers', () => {
    it('should create basic schema', () => {
      const schema = helper.createBasicSchema('string', true, { minLength: 5 });

      expect(schema).toEqual({
        type: 'string',
        required: true,
        minLength: 5,
      });
    });

    it('should create object schema', () => {
      const properties = {
        name: { type: 'string' },
        age: { type: 'number' },
      };

      const schema = helper.createObjectSchema(properties, ['name'], false);

      expect(schema).toEqual({
        type: 'object',
        properties,
        required: ['name'],
        additionalProperties: false,
      });
    });

    it('should create array schema', () => {
      const itemSchema = { type: 'string' };
      const schema = helper.createArraySchema(itemSchema, 1, 10);

      expect(schema).toEqual({
        type: 'array',
        items: itemSchema,
        minItems: 1,
        maxItems: 10,
      });
    });
  });

  describe('Type Validation', () => {
    it('should validate allowed types', () => {
      expect(helper.validateType('string', ['string', 'number'])).toBe(true);
      expect(helper.validateType(42, ['string', 'number'])).toBe(true);
      expect(helper.validateType([], ['array'])).toBe(true);
      expect(helper.validateType({}, ['object'])).toBe(true);

      expect(helper.validateType('string', ['number'])).toBe(false);
      expect(helper.validateType([], ['object'])).toBe(false);
    });
  });

  describe('Cache Management', () => {
    it('should clear schema cache', () => {
      const schema = { type: 'string' };
      
      // Validate to populate cache
      helper.validateWithDetails('test', schema);
      
      // Clear cache
      helper.clearCache();
      
      // Should still work after cache clear
      const result = helper.validateWithDetails('test', schema);
      expect(result.valid).toBe(true);
    });
  });

  describe('Complex Schemas', () => {
    it('should validate nested object schemas', () => {
      const schema = {
        type: 'object',
        properties: {
          user: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              contact: {
                type: 'object',
                properties: {
                  email: { type: 'string', format: 'email' },
                  phone: { type: 'string' },
                },
                required: ['email'],
              },
            },
            required: ['name', 'contact'],
          },
        },
        required: ['user'],
      };

      const validData = {
        user: {
          name: 'John Doe',
          contact: {
            email: '<EMAIL>',
            phone: '+1234567890',
          },
        },
      };

      const invalidData = {
        user: {
          name: 'John Doe',
          contact: {
            // Missing email
            phone: '+1234567890',
          },
        },
      };

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });

    it('should validate array of objects', () => {
      const schema = {
        type: 'object',
        properties: {
          users: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                age: { type: 'number', minimum: 0 },
              },
              required: ['name'],
            },
            minItems: 1,
          },
        },
      };

      const validData = {
        users: [
          { name: 'John', age: 30 },
          { name: 'Jane', age: 25 },
        ],
      };

      const invalidData = {
        users: [
          { name: 'John', age: 30 },
          { age: 25 }, // Missing name
        ],
      };

      expect(() => helper.validateAgainstSchema(validData, schema)).not.toThrow();
      expect(() => helper.validateAgainstSchema(invalidData, schema)).toThrow();
    });
  });
});
