import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { FacebookPageService } from '../../../../../shared/services/facebook/page/facebook-page.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Facebook Page Create Post node
 */
export interface FacebookPageCreatePostInput {
  /**
   * ID của Facebook Page
   */
  pageId: string;

  /**
   * Nội dung bài viết
   */
  message?: string;

  /**
   * Link đính kèm
   */
  link?: string;

  /**
   * Ảnh đ<PERSON>h kèm (URL)
   */
  picture?: string;

  /**
   * Tiêu đề của link
   */
  name?: string;

  /**
   * Caption cho ảnh/video
   */
  caption?: string;

  /**
   * <PERSON>ô tả cho link
   */
  description?: string;

  /**
   * Có publish ngay không (mặc định: true)
   */
  published?: boolean;

  /**
   * Thời gian schedule publish (Unix timestamp)
   */
  scheduled_publish_time?: number;
}

/**
 * Output schema cho Facebook Page Create Post node
 */
export interface FacebookPageCreatePostOutput {
  /**
   * ID của post đã tạo
   */
  postId: string;

  /**
   * ID đầy đủ của post (pageId_postId)
   */
  fullPostId?: string;

  /**
   * Thông tin post đã tạo
   */
  post: {
    id: string;
    message?: string;
    link?: string;
    picture?: string;
    name?: string;
    caption?: string;
    description?: string;
    published: boolean;
    scheduled_publish_time?: number;
  };

  /**
   * Metadata
   */
  metadata: {
    pageId: string;
    created: string;
    status: 'published' | 'scheduled' | 'draft';
  };
}

/**
 * Executor để tạo post trên Facebook Page
 */
@Injectable()
export class FacebookPageCreatePostExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'facebook.page.create_post';
  readonly category = NodeCategory.FACEBOOK_PAGE;
  readonly name = 'Create Page Post';
  readonly description = 'Tạo bài viết mới trên Facebook Page';

  constructor(
    integrationService: IntegrationService,
    private readonly facebookPageService: FacebookPageService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        pageId: { type: 'string' },
        message: { type: 'string' },
        link: { type: 'string', format: 'uri' },
        picture: { type: 'string', format: 'uri' },
        name: { type: 'string' },
        caption: { type: 'string' },
        description: { type: 'string' },
        published: { type: 'boolean' },
        scheduled_publish_time: { type: 'number' },
      },
      required: ['pageId'],
      anyOf: [
        { required: ['message'] },
        { required: ['link'] },
        { required: ['picture'] },
      ],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        postId: { type: 'string' },
        fullPostId: { type: 'string' },
        post: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            message: { type: 'string' },
            link: { type: 'string' },
            picture: { type: 'string' },
            name: { type: 'string' },
            caption: { type: 'string' },
            description: { type: 'string' },
            published: { type: 'boolean' },
            scheduled_publish_time: { type: 'number' },
          },
        },
        metadata: {
          type: 'object',
          properties: {
            pageId: { type: 'string' },
            created: { type: 'string' },
            status: { type: 'string', enum: ['published', 'scheduled', 'draft'] },
          },
        },
      },
      required: ['postId', 'post', 'metadata'],
    };
  }

  /**
   * Get provider for authentication
   */
  protected getProvider(): ProviderEnum {
    return ProviderEnum.FACEBOOK_PAGE;
  }

  /**
   * Execute the Facebook Page Create Post node
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.FACEBOOK_PAGE, async () => {
      this.logger.log(`Creating Facebook Page post for page: ${inputs.pageId}`);

      // Validate inputs
      if (!inputs.message && !inputs.link && !inputs.picture) {
        throw new Error('At least one of message, link, or picture must be provided');
      }

      // Prepare post data
      const postData = {
        message: inputs.message,
        link: inputs.link,
        picture: inputs.picture,
        name: inputs.name,
        caption: inputs.caption,
        description: inputs.description,
        published: inputs.published !== false, // Default to true
        scheduled_publish_time: inputs.scheduled_publish_time,
      };

      // Remove undefined values
      Object.keys(postData).forEach(key => {
        if (postData[key] === undefined) {
          delete postData[key];
        }
      });

      // Get access token
      const accessToken = await this.getFacebookPageAccessToken(context);

      // Create post
      const createdPost = await this.facebookPageService.createPost(
        inputs.pageId,
        accessToken,
        postData,
      );

      // Determine status
      let status: 'published' | 'scheduled' | 'draft' = 'published';
      if (inputs.scheduled_publish_time) {
        status = 'scheduled';
      } else if (inputs.published === false) {
        status = 'draft';
      }

      // Prepare output
      const output: FacebookPageCreatePostOutput = {
        postId: createdPost.id,
        fullPostId: createdPost.post_id || createdPost.id,
        post: {
          id: createdPost.id,
          message: inputs.message,
          link: inputs.link,
          picture: inputs.picture,
          name: inputs.name,
          caption: inputs.caption,
          description: inputs.description,
          published: inputs.published !== false,
          scheduled_publish_time: inputs.scheduled_publish_time,
        },
        metadata: {
          pageId: inputs.pageId,
          created: new Date().toISOString(),
          status,
        },
      };

      this.logger.log(`Facebook Page post created successfully: ${output.postId}`);

      return output;
    });
  }


}
