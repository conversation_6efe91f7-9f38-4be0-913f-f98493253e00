import { Injectable, Logger } from '@nestjs/common';
import Ajv, { ValidateFunction } from 'ajv';
import addFormats from 'ajv-formats';
import { 
  ValidationResultDto, 
  ValidationErrorDto, 
  ValidationWarningDto,
  NodeValidationRequestDto,
  WorkflowValidationRequestDto,
  ComprehensiveValidationResultDto,
  ValidationPerformanceDto
} from '../dto/shared/validation-result.dto';
import { SharedWorkflowDefinitionDto } from '../dto/shared/workflow-definition.dto';

/**
 * Shared validation service for BE Worker
 * Synced with BE-003 validation service for consistency
 * Provides validation capabilities for workflow execution
 */
@Injectable()
export class SharedValidationService {
  private readonly logger = new Logger(SharedValidationService.name);
  private readonly ajv: Ajv;
  private readonly validationCache = new Map<string, ValidationResultDto>();

  constructor() {
    // Initialize AJV with same configuration as BE-003
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: false,
      useDefaults: true,
      coerceTypes: true,
    });

    // Add format validators
    // addFormats(this.ajv); // Temporarily disabled due to version conflict
    this.addCustomFormats();
  }

  /**
   * Validate workflow definition for execution
   * @param definition - Workflow definition to validate
   * @param options - Validation options
   * @returns Comprehensive validation result
   */
  async validateWorkflowForExecution(
    definition: SharedWorkflowDefinitionDto,
    options: {
      validateNodes?: boolean;
      validateEdges?: boolean;
      validateExecution?: boolean;
      enableCache?: boolean;
    } = {}
  ): Promise<ComprehensiveValidationResultDto> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey('workflow', definition);

    // Check cache if enabled
    if (options.enableCache && this.validationCache.has(cacheKey)) {
      this.logger.debug('Validation cache hit for workflow definition');
      return this.validationCache.get(cacheKey) as ComprehensiveValidationResultDto;
    }

    const result: ComprehensiveValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: [],
      validatedItems: [],
      summary: {
        totalItems: 0,
        validItems: 0,
        invalidItems: 0,
        totalErrors: 0,
        totalWarnings: 0,
        validationTime: 0
      }
    };

    try {
      // 1. Basic structure validation
      const structureValidation = this.validateDefinitionStructure(definition);
      this.mergeValidationResults(result, structureValidation);

      // 2. Node validation
      if (options.validateNodes !== false) {
        const nodeValidation = await this.validateNodes(definition.nodes);
        this.mergeValidationResults(result, nodeValidation);
      }

      // 3. Edge validation
      if (options.validateEdges !== false && definition.edges) {
        const edgeValidation = this.validateEdges(definition.edges, definition.nodes);
        this.mergeValidationResults(result, edgeValidation);
      }

      // 4. Execution readiness validation
      if (options.validateExecution) {
        const executionValidation = this.validateExecutionReadiness(definition);
        this.mergeValidationResults(result, executionValidation);
      }

      // 5. Performance analysis
      const performanceValidation = this.analyzePerformance(definition);
      this.mergeValidationResults(result, performanceValidation);

      // Update summary
      const endTime = Date.now();
      result.summary!.validationTime = endTime - startTime;
      result.summary!.totalErrors = result.errors.length;
      result.summary!.totalWarnings = result.warnings.length;
      result.isValid = result.errors.length === 0;

      // Add performance metrics
      result.performance = {
        validationType: 'workflow_execution',
        targetId: this.generateDefinitionId(definition),
        startTime,
        endTime,
        duration: endTime - startTime,
        cacheHit: false,
        metadata: {
          nodeCount: definition.nodes.length,
          edgeCount: definition.edges?.length || 0,
          validationRules: this.getValidationRuleCount(),
          complexityScore: this.calculateComplexityScore(definition)
        }
      };

      // Cache result if enabled
      if (options.enableCache) {
        this.validationCache.set(cacheKey, result);
      }

      this.logger.debug(`Workflow validation completed: ${result.isValid ? 'VALID' : 'INVALID'}`);

    } catch (error) {
      this.logger.error('Error during workflow validation:', error);
      result.isValid = false;
      result.errors.push({
        code: 'VALIDATION_ERROR',
        message: `Validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate individual node for execution
   * @param request - Node validation request
   * @returns Validation result
   */
  async validateNodeForExecution(request: NodeValidationRequestDto): Promise<ValidationResultDto> {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 1. Node type validation
      if (!this.isValidNodeType(request.nodeType)) {
        result.errors.push({
          code: 'INVALID_NODE_TYPE',
          message: `Node type '${request.nodeType}' is not supported for execution`,
          path: 'nodeType',
          severity: 'error'
        });
      }

      // 2. Input validation
      if (request.validateInputs && request.inputs) {
        const inputValidation = await this.validateNodeInputs(request.nodeType, request.inputs);
        result.errors.push(...inputValidation.errors);
        result.warnings.push(...inputValidation.warnings);
      }

      // 3. Configuration validation
      if (request.validateConfig && request.config) {
        const configValidation = await this.validateNodeConfig(request.nodeType, request.config);
        result.errors.push(...configValidation.errors);
        result.warnings.push(...configValidation.warnings);
      }

      result.isValid = result.errors.length === 0;

    } catch (error) {
      this.logger.error('Error validating node for execution:', error);
      result.isValid = false;
      result.errors.push({
        code: 'NODE_VALIDATION_ERROR',
        message: `Node validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate definition structure
   * @param definition - Workflow definition
   * @returns Validation result
   */
  private validateDefinitionStructure(definition: SharedWorkflowDefinitionDto): ValidationResultDto {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Check required fields
    if (!definition.nodes || !Array.isArray(definition.nodes)) {
      result.errors.push({
        code: 'MISSING_NODES',
        message: 'Workflow definition must have nodes array',
        severity: 'error'
      });
    }

    if (definition.nodes && definition.nodes.length === 0) {
      result.errors.push({
        code: 'EMPTY_WORKFLOW',
        message: 'Workflow must have at least one node',
        severity: 'error'
      });
    }

    // Check node ID uniqueness
    if (definition.nodes) {
      const nodeIds = definition.nodes.map(node => node.id);
      const uniqueIds = new Set(nodeIds);
      if (uniqueIds.size !== nodeIds.length) {
        result.errors.push({
          code: 'DUPLICATE_NODE_IDS',
          message: 'All node IDs must be unique within workflow',
          severity: 'error'
        });
      }
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Validate nodes for execution
   * @param nodes - Array of nodes
   * @returns Validation result
   */
  private async validateNodes(nodes: any[]): Promise<ValidationResultDto> {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    for (const node of nodes) {
      // Validate node structure
      if (!node.id || !node.type || !node.name) {
        result.errors.push({
          code: 'INVALID_NODE_STRUCTURE',
          message: `Node must have id, type, and name. Node: ${node.id || 'unknown'}`,
          path: `nodes[${node.id}]`,
          severity: 'error'
        });
        continue;
      }

      // Validate node type for execution
      if (!this.isExecutableNodeType(node.type)) {
        result.warnings.push({
          code: 'NON_EXECUTABLE_NODE',
          message: `Node type '${node.type}' may not be executable`,
          path: `nodes[${node.id}].type`,
          suggestion: 'Verify node type is supported by executor framework'
        });
      }

      // Validate position for execution context
      if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
        result.warnings.push({
          code: 'INVALID_NODE_POSITION',
          message: `Node '${node.id}' has invalid position`,
          path: `nodes[${node.id}].position`,
          suggestion: 'Position is required for execution visualization'
        });
      }
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Validate edges for execution
   * @param edges - Array of edges
   * @param nodes - Array of nodes for reference validation
   * @returns Validation result
   */
  private validateEdges(edges: any[], nodes: any[]): ValidationResultDto {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const nodeIds = new Set(nodes.map(node => node.id));

    for (const edge of edges) {
      // Validate edge structure
      if (!edge.id || !edge.sourceNodeId || !edge.targetNodeId) {
        result.errors.push({
          code: 'INVALID_EDGE_STRUCTURE',
          message: `Edge must have id, sourceNodeId, and targetNodeId. Edge: ${edge.id || 'unknown'}`,
          severity: 'error'
        });
        continue;
      }

      // Validate node references
      if (!nodeIds.has(edge.sourceNodeId)) {
        result.errors.push({
          code: 'INVALID_SOURCE_NODE',
          message: `Source node '${edge.sourceNodeId}' does not exist`,
          path: `edges[${edge.id}].sourceNodeId`,
          severity: 'error'
        });
      }

      if (!nodeIds.has(edge.targetNodeId)) {
        result.errors.push({
          code: 'INVALID_TARGET_NODE',
          message: `Target node '${edge.targetNodeId}' does not exist`,
          path: `edges[${edge.id}].targetNodeId`,
          severity: 'error'
        });
      }

      // Validate conditional edges
      if (edge.edgeType === 'conditional' && !edge.condition) {
        result.warnings.push({
          code: 'MISSING_CONDITION',
          message: `Conditional edge '${edge.id}' should have condition defined`,
          path: `edges[${edge.id}].condition`,
          suggestion: 'Add condition for proper execution flow'
        });
      }
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Validate execution readiness
   * @param definition - Workflow definition
   * @returns Validation result
   */
  private validateExecutionReadiness(definition: SharedWorkflowDefinitionDto): ValidationResultDto {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const nodes = definition.nodes;
    const edges = definition.edges || [];

    // Check for start nodes (nodes with no incoming edges)
    const targetNodes = new Set(edges.map(edge => edge.targetNodeId));
    const startNodes = nodes.filter(node => !targetNodes.has(node.id));

    if (startNodes.length === 0) {
      result.warnings.push({
        code: 'NO_START_NODE',
        message: 'Workflow should have at least one start node for execution',
        suggestion: 'Add a trigger or start node to begin execution'
      });
    }

    // Check for end nodes (nodes with no outgoing edges)
    const sourceNodes = new Set(edges.map(edge => edge.sourceNodeId));
    const endNodes = nodes.filter(node => !sourceNodes.has(node.id));

    if (endNodes.length === 0) {
      result.warnings.push({
        code: 'NO_END_NODE',
        message: 'Workflow should have at least one end node for execution',
        suggestion: 'Add an end node or action to complete execution'
      });
    }

    // Check for isolated nodes
    const connectedNodes = new Set([...sourceNodes, ...targetNodes]);
    const isolatedNodes = nodes.filter(node => !connectedNodes.has(node.id));

    if (isolatedNodes.length > 0) {
      result.warnings.push({
        code: 'ISOLATED_NODES',
        message: `Found ${isolatedNodes.length} isolated nodes that won't execute`,
        suggestion: 'Connect isolated nodes to the workflow or remove them'
      });
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Analyze performance characteristics
   * @param definition - Workflow definition
   * @returns Validation result with performance warnings
   */
  private analyzePerformance(definition: SharedWorkflowDefinitionDto): ValidationResultDto {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const nodeCount = definition.nodes.length;
    const edgeCount = definition.edges?.length || 0;

    // Check workflow size
    if (nodeCount > 50) {
      result.warnings.push({
        code: 'LARGE_WORKFLOW',
        message: `Workflow has ${nodeCount} nodes, which may impact execution performance`,
        suggestion: 'Consider breaking large workflows into smaller sub-workflows'
      });
    }

    if (edgeCount > 100) {
      result.warnings.push({
        code: 'MANY_CONNECTIONS',
        message: `Workflow has ${edgeCount} edges, which may impact execution performance`,
        suggestion: 'Review workflow complexity and consider simplification'
      });
    }

    // Check for potential execution bottlenecks
    const nodeConnections = new Map<string, number>();
    definition.edges?.forEach(edge => {
      nodeConnections.set(edge.targetNodeId, (nodeConnections.get(edge.targetNodeId) || 0) + 1);
    });

    for (const [nodeId, connectionCount] of nodeConnections.entries()) {
      if (connectionCount > 5) {
        result.warnings.push({
          code: 'HIGH_FAN_IN',
          message: `Node '${nodeId}' has ${connectionCount} incoming connections`,
          suggestion: 'High fan-in nodes may create execution bottlenecks'
        });
      }
    }

    return result;
  }

  /**
   * Validate node inputs for execution
   * @param nodeType - Type of node
   * @param inputs - Node inputs
   * @returns Validation result
   */
  private async validateNodeInputs(nodeType: string, inputs: any): Promise<ValidationResultDto> {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // This would integrate with WK-002 executor framework
    // For now, basic validation
    if (typeof inputs !== 'object') {
      result.errors.push({
        code: 'INVALID_INPUTS_TYPE',
        message: 'Node inputs must be an object',
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate node configuration for execution
   * @param nodeType - Type of node
   * @param config - Node configuration
   * @returns Validation result
   */
  private async validateNodeConfig(nodeType: string, config: any): Promise<ValidationResultDto> {
    const result: ValidationResultDto = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // This would integrate with WK-002 executor framework
    // For now, basic validation
    if (typeof config !== 'object') {
      result.errors.push({
        code: 'INVALID_CONFIG_TYPE',
        message: 'Node config must be an object',
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Check if node type is valid
   * @param nodeType - Node type to check
   * @returns true if valid
   */
  private isValidNodeType(nodeType: string): boolean {
    // This would integrate with WK-002 executor registry
    const validPatterns = [
      /^system\./,
      /^google\./,
      /^facebook\./,
      /^zalo\./,
      /^webhook\./,
      /^http\./,
      /^email\./,
      /^sms\./
    ];

    return validPatterns.some(pattern => pattern.test(nodeType));
  }

  /**
   * Check if node type is executable
   * @param nodeType - Node type to check
   * @returns true if executable
   */
  private isExecutableNodeType(nodeType: string): boolean {
    // This would integrate with WK-002 executor registry
    return this.isValidNodeType(nodeType);
  }

  /**
   * Merge validation results
   * @param target - Target result to merge into
   * @param source - Source result to merge from
   */
  private mergeValidationResults(target: ValidationResultDto, source: ValidationResultDto): void {
    target.errors.push(...source.errors);
    target.warnings.push(...source.warnings);
    target.isValid = target.isValid && source.isValid;
  }

  /**
   * Generate cache key for validation
   * @param type - Validation type
   * @param data - Data to generate key from
   * @returns Cache key
   */
  private generateCacheKey(type: string, data: any): string {
    const hash = require('crypto').createHash('md5').update(JSON.stringify(data)).digest('hex');
    return `${type}_${hash}`;
  }

  /**
   * Generate definition ID for tracking
   * @param definition - Workflow definition
   * @returns Definition ID
   */
  private generateDefinitionId(definition: SharedWorkflowDefinitionDto): string {
    const nodeIds = definition.nodes.map(n => n.id).sort().join(',');
    const edgeIds = definition.edges?.map(e => e.id).sort().join(',') || '';
    return require('crypto').createHash('md5').update(`${nodeIds}_${edgeIds}`).digest('hex');
  }

  /**
   * Get validation rule count
   * @returns Number of validation rules
   */
  private getValidationRuleCount(): number {
    // This would return actual rule count from validation schemas
    return 25; // Placeholder
  }

  /**
   * Calculate complexity score
   * @param definition - Workflow definition
   * @returns Complexity score
   */
  private calculateComplexityScore(definition: SharedWorkflowDefinitionDto): number {
    const nodeCount = definition.nodes.length;
    const edgeCount = definition.edges?.length || 0;
    const conditionalEdges = definition.edges?.filter(e => e.edgeType === 'conditional').length || 0;
    
    return Math.round((nodeCount * 1) + (edgeCount * 0.5) + (conditionalEdges * 2));
  }

  /**
   * Add custom format validators
   */
  private addCustomFormats(): void {
    // Node ID format
    this.ajv.addFormat('nodeId', {
      type: 'string',
      validate: (data: string) => /^[a-zA-Z0-9_-]+$/.test(data)
    });

    // Node type format
    this.ajv.addFormat('nodeType', {
      type: 'string',
      validate: (data: string) => /^[a-z]+(\.[a-z]+)*$/.test(data)
    });
  }
}
