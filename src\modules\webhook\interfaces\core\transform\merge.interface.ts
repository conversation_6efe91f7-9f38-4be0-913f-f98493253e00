/**
 * @file Interface cho Merge node
 * 
 * Đ<PERSON>nh nghĩa type-safe interface cho node Merge bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Multiple merge strategies
 * - Conflict resolution policies
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    EDataType,
    ICondition
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Merge node
// =================================================================

/**
 * Merge types - các loại merge được hỗ trợ
 */
export enum EMergeType {
    /** Merge objects together */
    OBJECT_MERGE = 'object_merge',

    /** Concatenate arrays */
    ARRAY_CONCAT = 'array_concat',

    /** Merge arrays với deduplication */
    ARRAY_MERGE = 'array_merge',

    /** Merge based on conditions */
    CONDITIONAL = 'conditional',

    /** Custom merge logic với expressions */
    CUSTOM = 'custom'
}

/**
 * Conflict resolution policies khi có key trùng nhau
 */
export enum EConflictResolution {
    /** Later values overwrite earlier ones */
    OVERWRITE = 'overwrite',

    /** Keep first value, ignore later ones */
    KEEP_FIRST = 'keep_first',

    /** Convert conflicts to arrays */
    MERGE_ARRAYS = 'merge_arrays',

    /** Throw error on conflicts */
    ERROR = 'error',

    /** Custom resolution logic */
    CUSTOM = 'custom'
}

/**
 * Array merge strategies
 */
export enum EArrayMergeStrategy {
    /** Simple concatenation */
    CONCAT = 'concat',

    /** Merge by unique key */
    MERGE_BY_KEY = 'merge_by_key',

    /** Union (remove duplicates) */
    UNION = 'union',

    /** Intersection (keep common items) */
    INTERSECTION = 'intersection'
}

/**
 * Input requirement types
 */
export enum EInputRequirement {
    /** All inputs must be present */
    ALL_REQUIRED = 'all_required',

    /** At least one input required */
    ANY_REQUIRED = 'any_required',

    /** Specific inputs required */
    SPECIFIC_REQUIRED = 'specific_required'
}

// =================================================================
// SECTION 2: MERGE CONFIGURATION STRUCTURES
// =================================================================

/**
 * Configuration cho conditional merge
 */
export interface IConditionalMergeConfig {
    /** Condition để evaluate */
    condition: ICondition;

    /** Input source khi condition = true */
    true_source: string;

    /** Input source khi condition = false */
    false_source: string;

    /** Có enabled không */
    enabled: boolean;
}

/**
 * Configuration cho array merge
 */
export interface IArrayMergeConfig {
    /** Merge strategy */
    strategy: EArrayMergeStrategy;

    /** Key để merge by (cho MERGE_BY_KEY strategy) */
    merge_key?: string;

    /** Có preserve order không */
    preserve_order?: boolean;

    /** Custom comparison function (cho advanced merging) */
    comparison_expression?: string;
}

/**
 * Input source configuration
 */
export interface IInputSource {
    /** Input handle name */
    name: string;

    /** Có required không */
    required: boolean;

    /** Data type expected */
    expected_type?: EDataType;

    /** Default value nếu input không có */
    default_value?: any;

    /** Transformation expression cho input này */
    transform_expression?: string;
}

/**
 * Post-processing configuration
 */
export interface IPostProcessing {
    /** Có enabled không */
    enabled: boolean;

    /** Transformation expression */
    expression?: string;

    /** Validation rules */
    validation_rules?: ICondition[];

    /** Output format */
    output_format?: 'object' | 'array' | 'primitive';
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Merge node
 */
export interface IMergeParameters {
    /** Merge type */
    merge_type: EMergeType;

    /** Input sources configuration */
    input_sources: IInputSource[];

    /** Input requirement policy */
    input_requirement: EInputRequirement;

    /** Conflict resolution policy */
    conflict_resolution: EConflictResolution;

    /** Custom conflict resolution expression */
    custom_conflict_resolution?: string;

    /** Array merge configuration */
    array_merge_config?: IArrayMergeConfig;

    /** Conditional merge configurations */
    conditional_configs?: IConditionalMergeConfig[];

    /** Custom merge expression */
    custom_expression?: string;

    /** Post-processing configuration */
    post_processing?: IPostProcessing;

    /** Có validate input types không */
    validate_input_types?: boolean;

    /** Có preserve metadata không */
    preserve_metadata?: boolean;

    /** Custom variables có thể sử dụng trong expressions */
    variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Merge node
 * Merge node có multiple inputs với dynamic keys
 */
export interface IMergeInput extends IBaseNodeInput {
    /** Dynamic inputs từ multiple sources */
    [inputName: string]: any;
}

/**
 * Interface cho output data của Merge node
 */
export interface IMergeOutput extends IBaseNodeOutput {
    /** Merged result data */
    merged_data: any;

    /** Input sources đã được sử dụng */
    input_sources: string[];

    /** Merge metadata */
    merge_metadata: {
        /** Merge type đã sử dụng */
        merge_type: EMergeType;

        /** Số inputs đã process */
        inputs_processed: number;

        /** Conflicts encountered và cách resolve */
        conflicts_resolved: Array<{
            key: string;
            conflict_type: string;
            resolution: string;
            original_values: any[];
            resolved_value: any;
        }>;

        /** Validation results */
        validation_results?: {
            passed: boolean;
            errors: string[];
        };

        /** Processing time */
        processing_time: number;

        /** Input type validation results */
        input_validations?: Record<string, {
            expected_type?: EDataType;
            actual_type: string;
            valid: boolean;
        }>;
    };

    /** Original input data (nếu preserve_metadata = true) */
    original_inputs?: Record<string, any>;
}

/**
 * Type-safe node execution cho Merge
 */
export type IMergeNodeExecution = ITypedNodeExecution<
    IMergeInput,
    IMergeOutput,
    IMergeParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để generate input handles từ input sources
 */
export function generateInputHandles(inputSources: IInputSource[]): string[] {
    return inputSources.map(source => source.name);
}

/**
 * Helper function để validate input requirements
 */
export function validateInputRequirements(
    inputs: Record<string, any>,
    inputSources: IInputSource[],
    requirement: EInputRequirement
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const availableInputs = Object.keys(inputs);
    const requiredInputs = inputSources.filter(source => source.required).map(source => source.name);

    switch (requirement) {
        case EInputRequirement.ALL_REQUIRED:
            const missingRequired = requiredInputs.filter(name => !availableInputs.includes(name));
            if (missingRequired.length > 0) {
                errors.push(`Missing required inputs: ${missingRequired.join(', ')}`);
            }
            break;

        case EInputRequirement.ANY_REQUIRED:
            if (availableInputs.length === 0) {
                errors.push('At least one input is required');
            }
            break;

        case EInputRequirement.SPECIFIC_REQUIRED:
            const missingSpecific = requiredInputs.filter(name => !availableInputs.includes(name));
            if (missingSpecific.length > 0) {
                errors.push(`Missing specific required inputs: ${missingSpecific.join(', ')}`);
            }
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Helper function để detect data type
 */
export function detectDataType(value: any): EDataType {
    if (value === null || value === undefined) return EDataType.OBJECT;
    if (typeof value === 'string') return EDataType.STRING;
    if (typeof value === 'number') return EDataType.NUMBER;
    if (typeof value === 'boolean') return EDataType.BOOLEAN;
    if (Array.isArray(value)) return EDataType.ARRAY;
    if (typeof value === 'object') return EDataType.OBJECT;
    return EDataType.OBJECT;
}

/**
 * Helper function để validate merge parameters
 */
export function validateMergeParameters(params: Partial<IMergeParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.merge_type) {
        errors.push('Merge type is required');
    }

    if (!params.input_sources || params.input_sources.length === 0) {
        errors.push('At least one input source is required');
    } else {
        // Validate input source names are unique
        const names = params.input_sources.map(source => source.name);
        const duplicates = names.filter((name, index) => names.indexOf(name) !== index);
        if (duplicates.length > 0) {
            errors.push(`Duplicate input source names: ${duplicates.join(', ')}`);
        }

        // Validate required fields
        params.input_sources.forEach((source, index) => {
            if (!source.name) {
                errors.push(`Input source ${index + 1}: name is required`);
            }
        });
    }

    if (!params.input_requirement) {
        errors.push('Input requirement policy is required');
    }

    if (!params.conflict_resolution) {
        errors.push('Conflict resolution policy is required');
    }

    // Validate merge type specific requirements
    switch (params.merge_type) {
        case EMergeType.ARRAY_MERGE:
            if (params.array_merge_config?.strategy === EArrayMergeStrategy.MERGE_BY_KEY) {
                if (!params.array_merge_config.merge_key) {
                    errors.push('Merge key is required for MERGE_BY_KEY strategy');
                }
            }
            break;

        case EMergeType.CONDITIONAL:
            if (!params.conditional_configs || params.conditional_configs.length === 0) {
                errors.push('Conditional configurations are required for CONDITIONAL merge');
            }
            break;

        case EMergeType.CUSTOM:
            if (!params.custom_expression) {
                errors.push('Custom expression is required for CUSTOM merge');
            }
            break;
    }

    if (params.conflict_resolution === EConflictResolution.CUSTOM) {
        if (!params.custom_conflict_resolution) {
            errors.push('Custom conflict resolution expression is required');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}