import { Test, TestingModule } from '@nestjs/testing';
import { NodeExecutorRegistry } from '../registry/node-executor-registry';

describe('NodeExecutorRegistry', () => {
  let registry: NodeExecutorRegistry;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NodeExecutorRegistry],
    }).compile();

    registry = module.get<NodeExecutorRegistry>(NodeExecutorRegistry);
  });

  it('should be defined', () => {
    expect(registry).toBeDefined();
  });

  // TODO: Add more tests for registry functionality
});
