import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để format cells trong Google Sheets
 */
@Injectable()
export class GoogleSheetsFormatCellsExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.formatCells';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Format Google Sheets Cells';
  readonly description = 'Format cells trong Google Sheets (màu sắc, font, border, etc.)';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      const { 
        spreadsheetId, 
        sheetId = 0,
        range,
        format = {}
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (!range) {
        throw new Error('Range is required');
      }

      const accessToken = await this.getGoogleAccessToken(context);

      // Format cells using GoogleSheetsService
      const result = await this.googleSheetsService.formatCells(
        accessToken,
        spreadsheetId,
        sheetId,
        range,
        format
      );

      return {
        spreadsheetId,
        sheetId,
        range,
        format,
        success: true,
        formattedAt: new Date().toISOString(),
        sheetsResponse: result,
      };
    });
  }

  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (!inputs.range || typeof inputs.range !== 'string') {
      throw new Error('Range must be a non-empty string');
    }

    return true;
  }

  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        sheetId: {
          type: 'integer',
          description: 'ID của sheet (mặc định: 0)',
          default: 0,
        },
        range: {
          type: 'string',
          description: 'Phạm vi cells để format (ví dụ: "A1:C3")',
        },
        format: {
          type: 'object',
          description: 'Định dạng áp dụng',
          properties: {
            backgroundColor: {
              type: 'object',
              properties: {
                red: { type: 'number', minimum: 0, maximum: 1 },
                green: { type: 'number', minimum: 0, maximum: 1 },
                blue: { type: 'number', minimum: 0, maximum: 1 },
              },
            },
            textFormat: {
              type: 'object',
              properties: {
                bold: { type: 'boolean' },
                italic: { type: 'boolean' },
                fontSize: { type: 'integer', minimum: 6, maximum: 400 },
                fontFamily: { type: 'string' },
              },
            },
            borders: {
              type: 'object',
              properties: {
                top: { type: 'object' },
                bottom: { type: 'object' },
                left: { type: 'object' },
                right: { type: 'object' },
              },
            },
          },
        },
      },
      required: ['spreadsheetId', 'range'],
    };
  }

  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: { type: 'string' },
        sheetId: { type: 'integer' },
        range: { type: 'string' },
        format: { type: 'object' },
        success: { type: 'boolean' },
        formattedAt: { type: 'string' },
        sheetsResponse: { type: 'object' },
      },
    };
  }
}
