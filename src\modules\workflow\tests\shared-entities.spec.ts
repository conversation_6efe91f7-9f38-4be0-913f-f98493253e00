import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import {
  Workflow,
  WorkflowNode,
  WorkflowEdge,
  NodeDefinition,
  WorkflowExecution,
  WorkflowExecutionLog,
  NodeCategory,
  ExecutionStatus
} from '../entities';

describe('Shared Entities Consistency Tests', () => {
  let module: TestingModule;
  let dataSource: DataSource;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432'),
          username: process.env.DB_USERNAME || 'test',
          password: process.env.DB_PASSWORD || 'test',
          database: process.env.DB_NAME || 'test_workflow',
          entities: [
            Workflow,
            WorkflowNode,
            WorkflowEdge,
            NodeDefinition,
            WorkflowExecution,
            WorkflowExecutionLog
          ],
          synchronize: true,
          dropSchema: true,
        }),
      ],
    }).compile();

    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  describe('Entity Structure Validation', () => {
    it('should have all required entities', () => {
      const entities = dataSource.entityMetadatas.map(meta => meta.name);
      
      expect(entities).toContain('Workflow');
      expect(entities).toContain('WorkflowNode');
      expect(entities).toContain('WorkflowEdge');
      expect(entities).toContain('NodeDefinition');
      expect(entities).toContain('WorkflowExecution');
      expect(entities).toContain('WorkflowExecutionLog');
    });

    it('should have correct table names', () => {
      const tableNames = dataSource.entityMetadatas.map(meta => meta.tableName);
      
      expect(tableNames).toContain('workflows');
      expect(tableNames).toContain('workflow_nodes');
      expect(tableNames).toContain('workflow_edges');
      expect(tableNames).toContain('node_definitions');
      expect(tableNames).toContain('workflow_executions');
      expect(tableNames).toContain('workflow_execution_logs');
    });

    it('should have correct primary key types', () => {
      const workflowMeta = dataSource.getMetadata(Workflow);
      const nodeMeta = dataSource.getMetadata(WorkflowNode);
      const edgeMeta = dataSource.getMetadata(WorkflowEdge);
      const nodeDefMeta = dataSource.getMetadata(NodeDefinition);
      const executionMeta = dataSource.getMetadata(WorkflowExecution);
      const logMeta = dataSource.getMetadata(WorkflowExecutionLog);

      expect(workflowMeta.primaryColumns[0].type).toBe('uuid');
      expect(nodeMeta.primaryColumns[0].type).toBe('uuid');
      expect(edgeMeta.primaryColumns[0].type).toBe('uuid');
      expect(nodeDefMeta.primaryColumns[0].type).toBe('varchar');
      expect(executionMeta.primaryColumns[0].type).toBe('uuid');
      expect(logMeta.primaryColumns[0].type).toBe('bigint');
    });
  });

  describe('Entity Relationships', () => {
    it('should have correct foreign key relationships', () => {
      const nodeMeta = dataSource.getMetadata(WorkflowNode);
      const edgeMeta = dataSource.getMetadata(WorkflowEdge);
      const executionMeta = dataSource.getMetadata(WorkflowExecution);
      const logMeta = dataSource.getMetadata(WorkflowExecutionLog);

      // WorkflowNode relationships
      const nodeWorkflowRelation = nodeMeta.relations.find(r => r.propertyName === 'workflow');
      expect(nodeWorkflowRelation).toBeDefined();
      expect(nodeWorkflowRelation?.type).toBe(Workflow);

      const nodeDefinitionRelation = nodeMeta.relations.find(r => r.propertyName === 'nodeDefinition');
      expect(nodeDefinitionRelation).toBeDefined();
      expect(nodeDefinitionRelation?.type).toBe(NodeDefinition);

      // WorkflowEdge relationships
      const edgeWorkflowRelation = edgeMeta.relations.find(r => r.propertyName === 'workflow');
      expect(edgeWorkflowRelation).toBeDefined();
      expect(edgeWorkflowRelation?.type).toBe(Workflow);

      // WorkflowExecution relationships
      const executionWorkflowRelation = executionMeta.relations.find(r => r.propertyName === 'workflow');
      expect(executionWorkflowRelation).toBeDefined();
      expect(executionWorkflowRelation?.type).toBe(Workflow);

      // WorkflowExecutionLog relationships
      const logExecutionRelation = logMeta.relations.find(r => r.propertyName === 'workflowExecution');
      expect(logExecutionRelation).toBeDefined();
      expect(logExecutionRelation?.type).toBe(WorkflowExecution);
    });
  });

  describe('Entity Creation and Persistence', () => {
    let workflow: Workflow;
    let nodeDefinition: NodeDefinition;

    beforeEach(async () => {
      // Create test node definition
      nodeDefinition = dataSource.getRepository(NodeDefinition).create({
        type: 'test.node.sample',
        name: 'Test Node',
        description: 'A test node for validation',
        category: NodeCategory.SYSTEM,
        inputSchema: { type: 'object', properties: {} },
        outputSchema: { type: 'object', properties: {} },
        version: '1.0.0'
      });
      await dataSource.getRepository(NodeDefinition).save(nodeDefinition);

      // Create test workflow
      workflow = dataSource.getRepository(Workflow).create({
        userId: 1,
        employeeId: 1,
        name: 'Test Workflow',
        isActive: true,
        definition: { nodes: [], edges: [] }
      });
      await dataSource.getRepository(Workflow).save(workflow);
    });

    afterEach(async () => {
      // Cleanup
      await dataSource.getRepository(WorkflowExecutionLog).delete({});
      await dataSource.getRepository(WorkflowExecution).delete({});
      await dataSource.getRepository(WorkflowEdge).delete({});
      await dataSource.getRepository(WorkflowNode).delete({});
      await dataSource.getRepository(Workflow).delete({});
      await dataSource.getRepository(NodeDefinition).delete({});
    });

    it('should create and persist workflow with nodes and edges', async () => {
      // Create workflow node
      const workflowNode = dataSource.getRepository(WorkflowNode).create({
        workflowId: workflow.id,
        nodeId: 'node-1',
        nodeType: nodeDefinition.type,
        name: 'Test Node Instance',
        config: { param1: 'value1' },
        position: { x: 100, y: 200 },
        metadata: { ui: 'data' }
      });
      await dataSource.getRepository(WorkflowNode).save(workflowNode);

      // Create another node for edge testing
      const workflowNode2 = dataSource.getRepository(WorkflowNode).create({
        workflowId: workflow.id,
        nodeId: 'node-2',
        nodeType: nodeDefinition.type,
        name: 'Test Node Instance 2',
        config: { param2: 'value2' },
        position: { x: 300, y: 200 },
        metadata: {}
      });
      await dataSource.getRepository(WorkflowNode).save(workflowNode2);

      // Create workflow edge
      const workflowEdge = dataSource.getRepository(WorkflowEdge).create({
        workflowId: workflow.id,
        edgeId: 'edge-1',
        sourceNodeId: 'node-1',
        targetNodeId: 'node-2',
        edgeType: 'normal',
        metadata: { style: 'solid' }
      });
      await dataSource.getRepository(WorkflowEdge).save(workflowEdge);

      // Verify persistence
      const savedNodes = await dataSource.getRepository(WorkflowNode).find({
        where: { workflowId: workflow.id }
      });
      expect(savedNodes).toHaveLength(2);

      const savedEdges = await dataSource.getRepository(WorkflowEdge).find({
        where: { workflowId: workflow.id }
      });
      expect(savedEdges).toHaveLength(1);
    });

    it('should create and persist workflow execution with logs', async () => {
      // Create workflow execution
      const execution = dataSource.getRepository(WorkflowExecution).create({
        workflowId: workflow.id,
        status: ExecutionStatus.RUNNING,
        triggerEvent: { type: 'manual', data: {} },
        startedAt: Date.now()
      });
      await dataSource.getRepository(WorkflowExecution).save(execution);

      // Create execution logs
      const log1 = dataSource.getRepository(WorkflowExecutionLog).create({
        workflowExecutionId: execution.id,
        nodeId: 'node-1',
        eventType: 'node.start',
        payload: { input: 'test' },
        timestamp: Date.now()
      });

      const log2 = dataSource.getRepository(WorkflowExecutionLog).create({
        workflowExecutionId: execution.id,
        nodeId: 'node-1',
        eventType: 'node.success',
        payload: { output: 'result' },
        timestamp: Date.now() + 1000
      });

      await dataSource.getRepository(WorkflowExecutionLog).save([log1, log2]);

      // Verify persistence
      const savedExecution = await dataSource.getRepository(WorkflowExecution).findOne({
        where: { id: execution.id },
        relations: ['logs']
      });

      expect(savedExecution).toBeDefined();
      expect(savedExecution?.logs).toHaveLength(2);
    });
  });

  describe('Enum Validation', () => {
    it('should validate NodeCategory enum values', () => {
      const validCategories = Object.values(NodeCategory);
      
      expect(validCategories).toContain('system');
      expect(validCategories).toContain('google_sheet');
      expect(validCategories).toContain('facebook_page');
      expect(validCategories).toContain('zalo_oa');
    });

    it('should validate ExecutionStatus enum values', () => {
      const validStatuses = Object.values(ExecutionStatus);
      
      expect(validStatuses).toContain('queued');
      expect(validStatuses).toContain('running');
      expect(validStatuses).toContain('completed');
      expect(validStatuses).toContain('failed');
      expect(validStatuses).toContain('paused');
    });
  });
});
