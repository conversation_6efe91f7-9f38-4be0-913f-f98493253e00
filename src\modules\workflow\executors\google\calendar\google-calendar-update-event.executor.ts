import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleCalendarService } from '../../../../../shared/services/google/calendar/google-calendar.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Input schema cho Google Calendar Update Event node
 */
export interface GoogleCalendarUpdateEventInput {
  /**
   * Calendar ID (mặc định: 'primary')
   */
  calendarId?: string;

  /**
   * Event ID cần update
   */
  eventId: string;

  /**
   * Tiêu đề event (optional)
   */
  summary?: string;

  /**
   * <PERSON>ô tả event (optional)
   */
  description?: string;

  /**
   * Đ<PERSON><PERSON> điểm (optional)
   */
  location?: string;

  /**
   * Thời gian bắt đầu (ISO 8601) (optional)
   */
  startDateTime?: string;

  /**
   * Thời gian kết thúc (ISO 8601) (optional)
   */
  endDateTime?: string;

  /**
   * Timezone (optional)
   */
  timeZone?: string;

  /**
   * Danh sách email người tham dự (optional)
   */
  attendees?: string[];

  /**
   * Có gửi thông báo không (optional)
   */
  sendNotifications?: boolean;

  /**
   * Event cả ngày (optional)
   */
  allDay?: boolean;

  /**
   * Color ID (optional)
   */
  colorId?: string;

  /**
   * Visibility (optional)
   */
  visibility?: 'default' | 'public' | 'private' | 'confidential';

  /**
   * Recurrence rules (optional)
   */
  recurrence?: string[];
}

/**
 * Output schema cho Google Calendar Update Event node
 */
export interface GoogleCalendarUpdateEventOutput {
  /**
   * Event ID
   */
  eventId: string;

  /**
   * HTML link to event
   */
  htmlLink: string;

  /**
   * Google Meet link (nếu có)
   */
  meetingLink?: string;

  /**
   * Updated event object
   */
  event: any;

  /**
   * Metadata
   */
  metadata: {
    calendarId: string;
    updatedAt: string;
    fieldsUpdated: string[];
  };
}

/**
 * Executor để update event trong Google Calendar
 */
@Injectable()
export class GoogleCalendarUpdateEventExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.calendar.update_event';
  readonly category = NodeCategory.GOOGLE_CALENDAR;
  readonly name = 'Update Calendar Event';
  readonly description = 'Cập nhật event trong Google Calendar';

  constructor(
    integrationService: IntegrationService,
    private readonly calendarService: GoogleCalendarService,
  ) {
    super(integrationService);
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        calendarId: { type: 'string' },
        eventId: { type: 'string' },
        summary: { type: 'string' },
        description: { type: 'string' },
        location: { type: 'string' },
        startDateTime: { type: 'string' },
        endDateTime: { type: 'string' },
        timeZone: { type: 'string' },
        attendees: { type: 'array', items: { type: 'string' } },
        sendNotifications: { type: 'boolean' },
        allDay: { type: 'boolean' },
        colorId: { type: 'string' },
        visibility: { type: 'string', enum: ['default', 'public', 'private', 'confidential'] },
        recurrence: { type: 'array', items: { type: 'string' } },
      },
      required: ['eventId'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        eventId: { type: 'string' },
        htmlLink: { type: 'string' },
        meetingLink: { type: 'string' },
        event: { type: 'object' },
        metadata: { type: 'object' },
      },
    };
  }

  /**
   * Thực thi node update event
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Updating calendar event: ${inputs.eventId}`);

      // Validate required inputs
      if (!inputs.eventId) {
        throw new Error('Event ID is required');
      }

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Prepare update data - only include fields that are provided
      const updateData: any = {};
      const fieldsUpdated: string[] = [];

      if (inputs.summary !== undefined) {
        updateData.summary = inputs.summary;
        fieldsUpdated.push('summary');
      }

      if (inputs.description !== undefined) {
        updateData.description = inputs.description;
        fieldsUpdated.push('description');
      }

      if (inputs.location !== undefined) {
        updateData.location = inputs.location;
        fieldsUpdated.push('location');
      }

      // Handle date/time updates
      if (inputs.startDateTime || inputs.endDateTime) {
        if (inputs.allDay) {
          // All day event
          if (inputs.startDateTime) {
            updateData.start = {
              date: inputs.startDateTime.split('T')[0], // Extract date part
              timeZone: inputs.timeZone || 'Asia/Ho_Chi_Minh',
            };
          }
          if (inputs.endDateTime) {
            updateData.end = {
              date: inputs.endDateTime.split('T')[0], // Extract date part
              timeZone: inputs.timeZone || 'Asia/Ho_Chi_Minh',
            };
          }
        } else {
          // Timed event
          if (inputs.startDateTime) {
            updateData.start = {
              dateTime: inputs.startDateTime,
              timeZone: inputs.timeZone || 'Asia/Ho_Chi_Minh',
            };
          }
          if (inputs.endDateTime) {
            updateData.end = {
              dateTime: inputs.endDateTime,
              timeZone: inputs.timeZone || 'Asia/Ho_Chi_Minh',
            };
          }
        }
        fieldsUpdated.push('dateTime');
      }

      // Handle attendees
      if (inputs.attendees) {
        updateData.attendees = inputs.attendees.map(email => ({ email }));
        fieldsUpdated.push('attendees');
      }

      // Handle other optional fields
      if (inputs.colorId !== undefined) {
        updateData.colorId = inputs.colorId;
        fieldsUpdated.push('colorId');
      }

      if (inputs.visibility !== undefined) {
        updateData.visibility = inputs.visibility;
        fieldsUpdated.push('visibility');
      }

      if (inputs.recurrence) {
        updateData.recurrence = inputs.recurrence;
        fieldsUpdated.push('recurrence');
      }

      // Prepare update request
      const calendarId = inputs.calendarId || 'primary';
      const updateRequest = {
        calendarId,
        eventId: inputs.eventId,
        event: updateData,
        sendNotifications: inputs.sendNotifications !== false, // Default to true
      };

      // Update event
      const updatedEvent = await this.calendarService.updateEvent(
        accessToken,
        updateRequest,
      );

      // Prepare output
      const output: GoogleCalendarUpdateEventOutput = {
        eventId: updatedEvent.id || inputs.eventId,
        htmlLink: updatedEvent.htmlLink || '',
        meetingLink: updatedEvent.hangoutLink || updatedEvent.conferenceData?.entryPoints?.[0]?.uri,
        event: updatedEvent,
        metadata: {
          calendarId,
          updatedAt: new Date().toISOString(),
          fieldsUpdated,
        },
      };

      this.logger.log(`Calendar event updated successfully: ${output.eventId}`);

      return output;
    });
  }
}
