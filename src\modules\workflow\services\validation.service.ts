import { Injectable } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

/**
 * Service để validate data theo JSON Schema và class validators
 * Hỗ trợ validation cho workflow, nodes, và job data
 */
@Injectable()
export class ValidationService {
  private readonly ajv: Ajv;

  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    // addFormats(this.ajv); // Temporarily disabled due to version conflict
  }

  /**
   * Validate data theo JSON Schema
   * @param data - Data cần validate
   * @param schema - JSON Schema
   * @returns Validation result
   */
  async validateSchema(
    data: any,
    schema: Record<string, any>
  ): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const validate = this.ajv.compile(schema);
      const valid = validate(data);

      if (!valid && validate.errors) {
        const errors = validate.errors.map(error => {
          const path = error.instancePath || error.schemaPath;
          return `${path}: ${error.message}`;
        });
        return { valid: false, errors };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        errors: [`Schema validation error: ${error.message}`]
      };
    }
  }

  /**
   * Validate DTO using class-validator
   * @param dtoClass - DTO class
   * @param data - Data cần validate
   * @returns Validation result
   */
  async validateDto<T>(
    dtoClass: new () => T,
    data: any
  ): Promise<{ valid: boolean; errors?: string[]; dto?: T }> {
    try {
      const dto = plainToClass(dtoClass, data);
      const errors = await validate(dto as any);

      if (errors.length > 0) {
        const errorMessages = errors.map(error => {
          const constraints = error.constraints || {};
          return Object.values(constraints).join(', ');
        });
        return { valid: false, errors: errorMessages };
      }

      return { valid: true, dto };
    } catch (error) {
      return {
        valid: false,
        errors: [`DTO validation error: ${error.message}`]
      };
    }
  }

  /**
   * Validate node input data
   * @param nodeType - Type của node
   * @param inputData - Input data
   * @param inputSchema - Input JSON Schema
   * @returns Validation result
   */
  async validateNodeInput(
    nodeType: string,
    inputData: any,
    inputSchema: Record<string, any>
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const result = await this.validateSchema(inputData, inputSchema);
    
    if (!result.valid) {
      return {
        valid: false,
        errors: result.errors?.map(error => `Node ${nodeType} input: ${error}`)
      };
    }

    return { valid: true };
  }

  /**
   * Validate node output data
   * @param nodeType - Type của node
   * @param outputData - Output data
   * @param outputSchema - Output JSON Schema
   * @returns Validation result
   */
  async validateNodeOutput(
    nodeType: string,
    outputData: any,
    outputSchema: Record<string, any>
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const result = await this.validateSchema(outputData, outputSchema);
    
    if (!result.valid) {
      return {
        valid: false,
        errors: result.errors?.map(error => `Node ${nodeType} output: ${error}`)
      };
    }

    return { valid: true };
  }

  /**
   * Validate workflow definition
   * @param definition - Workflow definition
   * @returns Validation result
   */
  async validateWorkflowDefinition(
    definition: any
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = [];

    // Check required fields
    if (!definition.nodes || !Array.isArray(definition.nodes)) {
      errors.push('Workflow definition must have nodes array');
    }

    if (!definition.edges || !Array.isArray(definition.edges)) {
      errors.push('Workflow definition must have edges array');
    }

    if (errors.length > 0) {
      return { valid: false, errors };
    }

    // Validate nodes
    const nodeIds = new Set<string>();
    for (const node of definition.nodes) {
      if (!node.id) {
        errors.push('All nodes must have an id');
        continue;
      }

      if (nodeIds.has(node.id)) {
        errors.push(`Duplicate node id: ${node.id}`);
        continue;
      }

      nodeIds.add(node.id);

      if (!node.type) {
        errors.push(`Node ${node.id} must have a type`);
      }

      if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
        errors.push(`Node ${node.id} must have valid position {x, y}`);
      }
    }

    // Validate edges
    const edgeIds = new Set<string>();
    for (const edge of definition.edges) {
      if (!edge.id) {
        errors.push('All edges must have an id');
        continue;
      }

      if (edgeIds.has(edge.id)) {
        errors.push(`Duplicate edge id: ${edge.id}`);
        continue;
      }

      edgeIds.add(edge.id);

      if (!edge.source || !nodeIds.has(edge.source)) {
        errors.push(`Edge ${edge.id} has invalid source: ${edge.source}`);
      }

      if (!edge.target || !nodeIds.has(edge.target)) {
        errors.push(`Edge ${edge.id} has invalid target: ${edge.target}`);
      }

      // Check for self-loops
      if (edge.source === edge.target) {
        errors.push(`Edge ${edge.id} creates a self-loop`);
      }
    }

    // Check for circular dependencies (basic check)
    const hasCircularDependency = this.checkCircularDependencies(definition.edges);
    if (hasCircularDependency) {
      errors.push('Workflow has circular dependencies');
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  /**
   * Validate trigger data
   * @param triggerType - Type của trigger
   * @param triggerData - Trigger data
   * @returns Validation result
   */
  async validateTriggerData(
    triggerType: string,
    triggerData: any
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = [];

    if (!triggerData) {
      errors.push('Trigger data is required');
      return { valid: false, errors };
    }

    // Validate based on trigger type
    switch (triggerType) {
      case 'webhook.facebook':
      case 'webhook.zalo':
      case 'webhook.google':
        if (!triggerData.payload) {
          errors.push('Webhook trigger must have payload');
        }
        if (!triggerData.headers) {
          errors.push('Webhook trigger must have headers');
        }
        if (typeof triggerData.verified !== 'boolean') {
          errors.push('Webhook trigger must have verified status');
        }
        break;

      case 'manual':
        if (!triggerData.inputData) {
          errors.push('Manual trigger must have inputData');
        }
        if (!triggerData.triggeredBy) {
          errors.push('Manual trigger must have triggeredBy user ID');
        }
        break;

      case 'scheduled':
        if (!triggerData.scheduleId) {
          errors.push('Scheduled trigger must have scheduleId');
        }
        if (!triggerData.cronExpression) {
          errors.push('Scheduled trigger must have cronExpression');
        }
        break;

      default:
        errors.push(`Unknown trigger type: ${triggerType}`);
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  /**
   * Check for circular dependencies in edges
   * @private
   */
  private checkCircularDependencies(edges: any[]): boolean {
    const graph = new Map<string, string[]>();
    
    // Build adjacency list
    for (const edge of edges) {
      if (!graph.has(edge.source)) {
        graph.set(edge.source, []);
      }
      graph.get(edge.source)!.push(edge.target);
    }

    // DFS to detect cycles
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) {
        return true; // Back edge found
      }

      if (visited.has(node)) {
        return false; // Already processed
      }

      visited.add(node);
      recursionStack.add(node);

      const neighbors = graph.get(node) || [];
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) {
          return true;
        }
      }

      recursionStack.delete(node);
      return false;
    };

    // Check all nodes
    for (const node of graph.keys()) {
      if (!visited.has(node)) {
        if (hasCycle(node)) {
          return true;
        }
      }
    }

    return false;
  }
}
