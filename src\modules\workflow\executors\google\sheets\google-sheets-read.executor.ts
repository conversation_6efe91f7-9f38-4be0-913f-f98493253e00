import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để đọc dữ liệu từ Google Sheets
 */
@Injectable()
export class GoogleSheetsReadExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.getRows';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Read Google Sheets Data';
  readonly description = '<PERSON><PERSON><PERSON> dữ liệu từ Google Sheets theo range chỉ định';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node đọc dữ liệu Google Sheets
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const {
        spreadsheetId,
        range,
        majorDimension = 'ROWS'
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (!range) {
        throw new Error('Range is required');
      }

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Read data using GoogleSheetsService
      const result = await this.googleSheetsService.readData(
        accessToken,
        spreadsheetId,
        range
      );

      // Return formatted result
      return {
        spreadsheetId,
        range: result.range,
        majorDimension,
        values: result.values || [],
        rowCount: result.values?.length || 0,
        columnCount: result.values?.[0]?.length || 0,
        readAt: new Date().toISOString(),
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (!inputs.range || typeof inputs.range !== 'string') {
      throw new Error('Range must be a non-empty string');
    }

    if (inputs.majorDimension && !['ROWS', 'COLUMNS'].includes(inputs.majorDimension)) {
      throw new Error('Major dimension must be either "ROWS" or "COLUMNS"');
    }

    if (inputs.valueRenderOption && !['FORMATTED_VALUE', 'UNFORMATTED_VALUE', 'FORMULA'].includes(inputs.valueRenderOption)) {
      throw new Error('Value render option must be "FORMATTED_VALUE", "UNFORMATTED_VALUE", or "FORMULA"');
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        range: {
          type: 'string',
          description: 'Phạm vi cells để đọc (ví dụ: "Sheet1!A1:C10")',
          pattern: '^[^!]+![A-Z]+[0-9]+:[A-Z]+[0-9]+$',
        },
        majorDimension: {
          type: 'string',
          description: 'Cách sắp xếp dữ liệu trả về',
          enum: ['ROWS', 'COLUMNS'],
          default: 'ROWS',
        },
        valueRenderOption: {
          type: 'string',
          description: 'Cách hiển thị giá trị',
          enum: ['FORMATTED_VALUE', 'UNFORMATTED_VALUE', 'FORMULA'],
          default: 'FORMATTED_VALUE',
        },
        dateTimeRenderOption: {
          type: 'string',
          description: 'Cách hiển thị ngày tháng',
          enum: ['SERIAL_NUMBER', 'FORMATTED_STRING'],
          default: 'SERIAL_NUMBER',
        },
      },
      required: ['spreadsheetId', 'range'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet',
        },
        range: {
          type: 'string',
          description: 'Phạm vi đã được đọc',
        },
        majorDimension: {
          type: 'string',
          description: 'Cách sắp xếp dữ liệu',
        },
        values: {
          type: 'array',
          description: 'Dữ liệu đã đọc (mảng 2 chiều)',
          items: {
            type: 'array',
            items: {
              oneOf: [
                { type: 'string' },
                { type: 'number' },
                { type: 'boolean' },
              ],
            },
          },
        },
        rowCount: {
          type: 'number',
          description: 'Số hàng đã đọc',
        },
        columnCount: {
          type: 'number',
          description: 'Số cột đã đọc',
        },
        readAt: {
          type: 'string',
          description: 'Thời gian đọc',
        },
      },
    };
  }
}
