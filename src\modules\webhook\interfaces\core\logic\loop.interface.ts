/**
 * @file Interface cho Loop node
 * 
 * Định nghĩa type-safe interface cho node Loop bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Loop execution logic và control flow
 * - Sub-workflow integration
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    INodeProperty
} from '../../node-manager.interface';
import {
    ICondition
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Loop node
// =================================================================

/**
 * Loop types - các loại loop được hỗ trợ
 */
export enum ELoopType {
    /** Iterate qua array/object items */
    FOR_EACH = 'for_each',

    /** Continue while condition is true */
    WHILE = 'while',

    /** Traditional for loop với start/end/step */
    FOR = 'for',

    /** Generate và iterate qua number range */
    RANGE = 'range'
}

/**
 * Error handling strategies khi iteration fails
 */
export enum EErrorHandling {
    /** Stop loop khi có iteration fails */
    STOP_ON_ERROR = 'stop_on_error',

    /** Skip failed iterations, continue loop */
    CONTINUE_ON_ERROR = 'continue_on_error',

    /** Continue loop nhưng collect error details */
    COLLECT_ERRORS = 'collect_errors'
}

/**
 * Output collection strategies
 */
export enum EOutputStrategy {
    /** Return array của tất cả results */
    COLLECT_ALL = 'collect_all',

    /** Chỉ return successful results */
    COLLECT_SUCCESS = 'collect_success',

    /** Combine results thành single value */
    AGGREGATE = 'aggregate',

    /** Chỉ return result của iteration cuối */
    LAST_RESULT = 'last_result'
}

/**
 * Loop control actions
 */
export enum ELoopControl {
    /** Continue to next iteration */
    CONTINUE = 'continue',

    /** Break out of loop */
    BREAK = 'break',

    /** Normal iteration completion */
    COMPLETE = 'complete'
}

// =================================================================
// SECTION 2: LOOP CONFIGURATION STRUCTURES
// =================================================================

/**
 * Configuration cho FOR loop
 */
export interface IForLoopConfig {
    /** Starting value */
    start: number;

    /** Ending value (exclusive) */
    end: number;

    /** Step increment */
    step: number;
}

/**
 * Configuration cho RANGE loop
 */
export interface IRangeLoopConfig {
    /** Starting value */
    start: number;

    /** Ending value (inclusive) */
    end: number;

    /** Step increment */
    step: number;
}

/**
 * Loop context được inject vào mỗi iteration
 */
export interface ILoopContext {
    /** Current iteration index (0-based) */
    index: number;

    /** Current iteration counter (1-based) */
    counter: number;

    /** Current item (for FOR_EACH loops) */
    item?: any;

    /** Current key (for object iteration) */
    key?: string;

    /** Total number of items/iterations */
    total: number;

    /** Is first iteration */
    is_first: boolean;

    /** Is last iteration */
    is_last: boolean;

    /** Custom loop variables */
    variables?: Record<string, any>;
}

/**
 * Break/Continue condition configuration
 */
export interface ILoopControlCondition {
    /** Condition để evaluate */
    condition: ICondition;

    /** Action khi condition = true */
    action: ELoopControl;

    /** Có enabled không */
    enabled: boolean;
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Loop node
 */
export interface ILoopParameters {
    /** Loop type */
    type: ELoopType;

    /** Field name chứa array/object để iterate (cho FOR_EACH) */
    items_field?: string;

    /** While loop condition (cho WHILE) */
    while_condition?: ICondition;

    /** For loop configuration (cho FOR) */
    for_config?: IForLoopConfig;

    /** Range loop configuration (cho RANGE) */
    range_config?: IRangeLoopConfig;

    /** Sub-workflow nodes để execute trong mỗi iteration */
    sub_workflow_nodes?: string[];

    /** Break conditions */
    break_conditions?: ILoopControlCondition[];

    /** Continue conditions */
    continue_conditions?: ILoopControlCondition[];

    /** Maximum iterations để prevent infinite loops */
    max_iterations: number;

    /** Timeout in milliseconds */
    timeout?: number;

    /** Error handling strategy */
    error_handling: EErrorHandling;

    /** Output collection strategy */
    output_strategy: EOutputStrategy;

    /** Có execute iterations in parallel không */
    parallel_execution?: boolean;

    /** Batch size cho parallel execution */
    batch_size?: number;

    /** Custom variables có thể access trong loop */
    loop_variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Loop node
 */
export interface ILoopInput extends IBaseNodeInput {
    /** Data chứa items để iterate hoặc loop variables */
    data: Record<string, any>;

    /** Context variables */
    variables?: Record<string, any>;

    /** Override items array (thay vì lấy từ items_field) */
    items?: any[];
}

/**
 * Interface cho output data của Loop node
 */
export interface ILoopOutput extends IBaseNodeOutput {
    /** Results từ tất cả iterations */
    results: any[];

    /** Aggregated result (nếu dùng AGGREGATE strategy) */
    aggregated_result?: any;

    /** Final result (nếu dùng LAST_RESULT strategy) */
    final_result?: any;

    /** Loop execution metadata */
    loop_metadata: {
        /** Total iterations executed */
        iterations_executed: number;

        /** Total iterations planned */
        iterations_planned: number;

        /** Có completed successfully không */
        completed: boolean;

        /** Reason for loop termination */
        termination_reason: 'completed' | 'max_iterations' | 'timeout' | 'break' | 'error';

        /** Total execution time */
        execution_time: number;

        /** Errors encountered */
        errors: Array<{
            iteration: number;
            error: string;
            details?: any;
        }>;

        /** Break/continue actions taken */
        control_actions: Array<{
            iteration: number;
            action: ELoopControl;
            condition?: string;
        }>;
    };

    /** Final loop context */
    final_context: ILoopContext;

    /** Original input data passed through */
    original_data: Record<string, any>;
}

/**
 * Type-safe node execution cho Loop
 */
export type ILoopNodeExecution = ITypedNodeExecution<
    ILoopInput,
    ILoopOutput,
    ILoopParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để generate items array dựa trên loop type
 */
export function generateLoopItems(params: ILoopParameters, inputData: any): any[] {
    switch (params.type) {
        case ELoopType.FOR_EACH:
            const items = params.items_field ?
                getNestedValue(inputData, params.items_field) :
                inputData.items;
            return Array.isArray(items) ? items : Object.values(items || {});

        case ELoopType.FOR:
            if (!params.for_config) return [];
            const { start, end, step } = params.for_config;
            const forItems: number[] = [];
            for (let i = start; i < end; i += step) {
                forItems.push(i);
            }
            return forItems;

        case ELoopType.RANGE:
            if (!params.range_config) return [];
            const { start: rStart, end: rEnd, step: rStep } = params.range_config;
            const rangeItems: number[] = [];
            for (let i = rStart; i <= rEnd; i += rStep) {
                rangeItems.push(i);
            }
            return rangeItems;

        case ELoopType.WHILE:
            // WHILE loop items được generate dynamically during execution
            return [];

        default:
            return [];
    }
}

/**
 * Helper function để get nested value từ object
 */
function getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Helper function để create loop context
 */
export function createLoopContext(
    index: number,
    item: any,
    total: number,
    key?: string,
    variables?: Record<string, any>
): ILoopContext {
    return {
        index,
        counter: index + 1,
        item,
        key,
        total,
        is_first: index === 0,
        is_last: index === total - 1,
        variables: variables || {}
    };
}

/**
 * Helper function để validate loop parameters
 */
export function validateLoopParameters(params: Partial<ILoopParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.type) {
        errors.push('Loop type is required');
    }

    if (!params.max_iterations || params.max_iterations <= 0) {
        errors.push('Max iterations must be greater than 0');
    }

    switch (params.type) {
        case ELoopType.FOR_EACH:
            if (!params.items_field) {
                errors.push('Items field is required for FOR_EACH loop');
            }
            break;

        case ELoopType.WHILE:
            if (!params.while_condition) {
                errors.push('While condition is required for WHILE loop');
            }
            break;

        case ELoopType.FOR:
            if (!params.for_config) {
                errors.push('For configuration is required for FOR loop');
            } else {
                const { start, end, step } = params.for_config;
                if (step <= 0) {
                    errors.push('For loop step must be greater than 0');
                }
                if (start >= end) {
                    errors.push('For loop start must be less than end');
                }
            }
            break;

        case ELoopType.RANGE:
            if (!params.range_config) {
                errors.push('Range configuration is required for RANGE loop');
            } else {
                const { start, end, step } = params.range_config;
                if (step <= 0) {
                    errors.push('Range step must be greater than 0');
                }
                if (start > end) {
                    errors.push('Range start must be less than or equal to end');
                }
            }
            break;
    }

    if (params.timeout && params.timeout < 1000) {
        errors.push('Timeout must be at least 1000ms');
    }

    if (params.parallel_execution && params.batch_size && params.batch_size <= 0) {
        errors.push('Batch size must be greater than 0');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}