import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import {
  HttpRequestExecutor,
  ConditionExecutor,
  DelayExecutor,
  DataTransformExecutor,
  LoopExecutor,
  OutputExecutor,
  ManualTriggerExecutor,
  VariableExecutor,
  ErrorHandlerExecutor,
} from '../system';
import { ExecutionContext } from '../../interfaces/execution-context.interface';

describe('System Executors', () => {
  let httpExecutor: HttpRequestExecutor;
  let conditionExecutor: ConditionExecutor;
  let delayExecutor: DelayExecutor;
  let dataTransformExecutor: DataTransformExecutor;
  let loopExecutor: LoopExecutor;
  let outputExecutor: OutputExecutor;
  let manualTriggerExecutor: ManualTriggerExecutor;
  let variableExecutor: VariableExecutor;
  let errorHandlerExecutor: ErrorHandlerExecutor;

  const mockContext: ExecutionContext = {
    executionId: 'test-execution-123',
    workflowId: 'test-workflow-456',
    userId: 123,
    triggerData: { test: 'data' },
    triggerType: 'manual',
    startTime: Date.now(),
    variables: {},
    nodeResults: {},
    // Required properties for ExecutionContext
    getNodeOutput: jest.fn(),
    setNodeOutput: jest.fn(),
    getNodeInput: jest.fn(),
    isNodeExecuted: jest.fn().mockReturnValue(false),
    getExecutedNodes: jest.fn().mockReturnValue([]),
    logService: {} as any,
    loggingService: {} as any,
    eventService: {} as any,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HttpRequestExecutor,
        ConditionExecutor,
        DelayExecutor,
        DataTransformExecutor,
        LoopExecutor,
        OutputExecutor,
        ManualTriggerExecutor,
        VariableExecutor,
        ErrorHandlerExecutor,
      ],
    }).compile();

    httpExecutor = module.get<HttpRequestExecutor>(HttpRequestExecutor);
    conditionExecutor = module.get<ConditionExecutor>(ConditionExecutor);
    delayExecutor = module.get<DelayExecutor>(DelayExecutor);
    dataTransformExecutor = module.get<DataTransformExecutor>(DataTransformExecutor);
    loopExecutor = module.get<LoopExecutor>(LoopExecutor);
    outputExecutor = module.get<OutputExecutor>(OutputExecutor);
    manualTriggerExecutor = module.get<ManualTriggerExecutor>(ManualTriggerExecutor);
    variableExecutor = module.get<VariableExecutor>(VariableExecutor);
    errorHandlerExecutor = module.get<ErrorHandlerExecutor>(ErrorHandlerExecutor);

    // Suppress console logs during testing
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('HttpRequestExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(httpExecutor.type).toBe('system.http.request');
      expect(httpExecutor.name).toBe('HTTP Request');
      expect(httpExecutor.description).toContain('HTTP requests');
    });

    it('should validate input schema', () => {
      const schema = httpExecutor.getInputSchema();
      expect(schema.required).toContain('url');
      expect(schema.properties.url.type).toBe('string');
      expect(schema.properties.method.enum).toContain('GET');
    });

    it('should validate output schema', () => {
      const schema = httpExecutor.getOutputSchema();
      expect(schema.properties.status).toBeDefined();
      expect(schema.properties.data).toBeDefined();
    });
  });

  describe('ConditionExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(conditionExecutor.type).toBe('system.if.condition');
      expect(conditionExecutor.name).toBe('Condition');
      expect(conditionExecutor.description).toContain('conditional expressions');
    });

    it('should validate input schema', () => {
      const schema = conditionExecutor.getInputSchema();
      expect(schema.required).toContain('condition');
      expect(schema.properties.operator.enum).toContain('javascript');
    });

    it('should execute simple condition', async () => {
      const inputs = {
        condition: 'user.age > 18',
        variables: { user: { age: 25 } },
        operator: 'simple',
      };

      const result = await conditionExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.result).toBe(true);
    });
  });

  describe('DelayExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(delayExecutor.type).toBe('system.delay');
      expect(delayExecutor.name).toBe('Delay');
      expect(delayExecutor.description).toContain('wait');
    });

    it('should execute short delay', async () => {
      const inputs = {
        duration: 10,
        unit: 'milliseconds',
        reason: 'Test delay',
      };

      const startTime = Date.now();
      const result = await delayExecutor.execute(inputs, mockContext);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.output.completed).toBe(true);
      expect(endTime - startTime).toBeGreaterThanOrEqual(10);
    });

    it('should validate delay duration', async () => {
      const inputs = {
        duration: -1,
        unit: 'milliseconds',
      };

      const result = await delayExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid delay duration');
    });
  });

  describe('DataTransformExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(dataTransformExecutor.type).toBe('system.transform.data');
      expect(dataTransformExecutor.name).toBe('Data Transform');
      expect(dataTransformExecutor.description).toContain('Transform data');
    });

    it('should execute mapping transformation', async () => {
      const inputs = {
        data: { firstName: 'John', lastName: 'Doe', age: 30 },
        transformType: 'mapping',
        mapping: {
          fullName: 'firstName',
          userAge: 'age',
        },
      };

      const result = await dataTransformExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.data.fullName).toBe('John');
      expect(result.output.data.userAge).toBe(30);
    });

    it('should execute filter transformation', async () => {
      const inputs = {
        data: [
          { name: 'John', age: 25 },
          { name: 'Jane', age: 17 },
          { name: 'Bob', age: 30 },
        ],
        transformType: 'filter',
        options: {
          condition: 'item.age >= 18',
          limit: 2,
        },
      };

      const result = await dataTransformExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.data).toHaveLength(2);
      expect(result.output.data.every((item: any) => item.age >= 18)).toBe(true);
    });
  });

  describe('LoopExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(loopExecutor.type).toBe('system.loop');
      expect(loopExecutor.name).toBe('Loop');
      expect(loopExecutor.description).toContain('loop operations');
    });

    it('should execute array loop', async () => {
      const inputs = {
        loopType: 'array',
        data: ['item1', 'item2', 'item3'],
        collectResults: true,
      };

      const result = await loopExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.completed).toBe(true);
      expect(result.output.iterations).toBe(3);
      expect(result.output.results).toHaveLength(3);
    });

    it('should execute count loop', async () => {
      const inputs = {
        loopType: 'count',
        count: 5,
        collectResults: true,
      };

      const result = await loopExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.completed).toBe(true);
      expect(result.output.iterations).toBe(5);
    });
  });

  describe('OutputExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(outputExecutor.type).toBe('system.output');
      expect(outputExecutor.name).toBe('Output');
      expect(outputExecutor.description).toContain('output data');
    });

    it('should format JSON output', async () => {
      const inputs = {
        data: { message: 'Hello World', status: 'success' },
        format: 'json',
        includeMetadata: true,
      };

      const result = await outputExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.data).toEqual(inputs.data);
      expect(result.output.format).toBe('json');
      expect(result.output.metadata).toBeDefined();
    });

    it('should format CSV output', async () => {
      const inputs = {
        data: [
          { name: 'John', age: 25 },
          { name: 'Jane', age: 30 },
        ],
        format: 'csv',
      };

      const result = await outputExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(typeof result.output.data).toBe('string');
      expect(result.output.data).toContain('name,age');
    });
  });

  describe('ManualTriggerExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(manualTriggerExecutor.type).toBe('system.manual.trigger');
      expect(manualTriggerExecutor.name).toBe('Manual Trigger');
      expect(manualTriggerExecutor.description).toContain('Manual workflow trigger');
    });

    it('should process trigger data', async () => {
      const inputs = {
        triggerData: { user: 'john', action: 'signup' },
        validateInput: true,
        enrichData: true,
      };

      const result = await manualTriggerExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.triggerType).toBe('manual');
      expect(result.output.data.user).toBe('john');
      expect(result.output.data._context).toBeDefined();
    });
  });

  describe('VariableExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(variableExecutor.type).toBe('system.variable');
      expect(variableExecutor.name).toBe('Variable');
      expect(variableExecutor.description).toContain('workflow variables');
    });

    it('should set and get variables', async () => {
      // Set variable
      const setInputs = {
        operation: 'set',
        variableName: 'testVar',
        value: 'testValue',
        scope: 'execution',
      };

      const setResult = await variableExecutor.execute(setInputs, mockContext);
      expect(setResult.success).toBe(true);
      expect(setResult.output.value).toBe('testValue');

      // Get variable
      const getInputs = {
        operation: 'get',
        variableName: 'testVar',
        scope: 'execution',
      };

      const getResult = await variableExecutor.execute(getInputs, mockContext);
      expect(getResult.success).toBe(true);
      expect(getResult.output.value).toBe('testValue');
    });

    it('should check variable existence', async () => {
      const inputs = {
        operation: 'exists',
        variableName: 'nonExistentVar',
        scope: 'execution',
      };

      const result = await variableExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.value).toBe(false);
    });
  });

  describe('ErrorHandlerExecutor', () => {
    it('should have correct type and metadata', () => {
      expect(errorHandlerExecutor.type).toBe('system.error.handler');
      expect(errorHandlerExecutor.name).toBe('Error Handler');
      expect(errorHandlerExecutor.description).toContain('Handle errors');
    });

    it('should handle error with log strategy', async () => {
      const inputs = {
        errorSource: 'test-node',
        errorData: { message: 'Test error', code: 'TEST_ERROR' },
        strategy: 'log',
        logLevel: 'error',
      };

      const result = await errorHandlerExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.handled).toBe(true);
      expect(result.output.strategy).toBe('log');
    });

    it('should handle error with fallback strategy', async () => {
      const inputs = {
        errorSource: 'test-node',
        errorData: { message: 'Test error' },
        strategy: 'fallback',
        fallbackValue: 'default value',
      };

      const result = await errorHandlerExecutor.execute(inputs, mockContext);
      expect(result.success).toBe(true);
      expect(result.output.handled).toBe(true);
      expect(result.output.result.fallbackValue).toBe('default value');
    });
  });

  describe('Schema Validation', () => {
    it('should have valid input schemas for all executors', () => {
      const executors = [
        httpExecutor,
        conditionExecutor,
        delayExecutor,
        dataTransformExecutor,
        loopExecutor,
        outputExecutor,
        manualTriggerExecutor,
        variableExecutor,
        errorHandlerExecutor,
      ];

      executors.forEach(executor => {
        const inputSchema = executor.getInputSchema();
        expect(inputSchema).toBeDefined();
        expect(inputSchema.type).toBe('object');
        expect(inputSchema.properties).toBeDefined();
      });
    });

    it('should have valid output schemas for all executors', () => {
      const executors = [
        httpExecutor,
        conditionExecutor,
        delayExecutor,
        dataTransformExecutor,
        loopExecutor,
        outputExecutor,
        manualTriggerExecutor,
        variableExecutor,
        errorHandlerExecutor,
      ];

      executors.forEach(executor => {
        const outputSchema = executor.getOutputSchema();
        expect(outputSchema).toBeDefined();
        expect(outputSchema.type).toBe('object');
        expect(outputSchema.properties).toBeDefined();
      });
    });
  });
});
