import { Injectable, Logger } from '@nestjs/common';
import { ExecutionContext, NodeExecutionResult } from '../../interfaces/execution-context.interface';

/**
 * Queue for managing node execution order and dependencies
 * Handles parallel execution and dependency resolution
 */
@Injectable()
export class NodeExecutionQueue {
  private readonly logger = new Logger(NodeExecutionQueue.name);
  private readonly executionQueue = new Map<string, QueueItem[]>();

  /**
   * Add node to execution queue
   */
  async enqueueNode(
    executionId: string,
    nodeId: string,
    dependencies: string[] = [],
    priority: number = 0
  ): Promise<void> {
    if (!this.executionQueue.has(executionId)) {
      this.executionQueue.set(executionId, []);
    }

    const queue = this.executionQueue.get(executionId)!;
    queue.push({
      nodeId,
      dependencies,
      priority,
      status: 'pending',
      enqueuedAt: new Date()
    });

    // Sort by priority
    queue.sort((a, b) => b.priority - a.priority);

    this.logger.debug(`Node ${nodeId} enqueued for execution ${executionId}`);
  }

  /**
   * Get next executable nodes (no pending dependencies)
   */
  async getExecutableNodes(executionId: string, completedNodes: Set<string>): Promise<string[]> {
    const queue = this.executionQueue.get(executionId);
    if (!queue) return [];

    return queue
      .filter(item => 
        item.status === 'pending' && 
        item.dependencies.every(dep => completedNodes.has(dep))
      )
      .map(item => item.nodeId);
  }

  /**
   * Mark node as executing
   */
  async markNodeExecuting(executionId: string, nodeId: string): Promise<void> {
    const queue = this.executionQueue.get(executionId);
    if (!queue) return;

    const item = queue.find(q => q.nodeId === nodeId);
    if (item) {
      item.status = 'executing';
      item.startedAt = new Date();
    }
  }

  /**
   * Mark node as completed
   */
  async markNodeCompleted(executionId: string, nodeId: string): Promise<void> {
    const queue = this.executionQueue.get(executionId);
    if (!queue) return;

    const item = queue.find(q => q.nodeId === nodeId);
    if (item) {
      item.status = 'completed';
      item.completedAt = new Date();
    }
  }

  /**
   * Mark node as failed
   */
  async markNodeFailed(executionId: string, nodeId: string, error: Error): Promise<void> {
    const queue = this.executionQueue.get(executionId);
    if (!queue) return;

    const item = queue.find(q => q.nodeId === nodeId);
    if (item) {
      item.status = 'failed';
      item.error = error.message;
      item.completedAt = new Date();
    }
  }

  /**
   * Clear execution queue
   */
  async clearQueue(executionId: string): Promise<void> {
    this.executionQueue.delete(executionId);
    this.logger.debug(`Queue cleared for execution ${executionId}`);
  }

  /**
   * Get queue status
   */
  async getQueueStatus(executionId: string): Promise<QueueStatus> {
    const queue = this.executionQueue.get(executionId) || [];
    
    return {
      total: queue.length,
      pending: queue.filter(q => q.status === 'pending').length,
      executing: queue.filter(q => q.status === 'executing').length,
      completed: queue.filter(q => q.status === 'completed').length,
      failed: queue.filter(q => q.status === 'failed').length
    };
  }
}

interface QueueItem {
  nodeId: string;
  dependencies: string[];
  priority: number;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  enqueuedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}

interface QueueStatus {
  total: number;
  pending: number;
  executing: number;
  completed: number;
  failed: number;
}
