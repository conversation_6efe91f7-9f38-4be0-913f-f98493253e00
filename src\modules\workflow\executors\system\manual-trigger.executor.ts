import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Manual Trigger Node Executor
 * Handles manual workflow triggers and initial data processing
 */
@Injectable()
export class ManualTriggerExecutor extends BaseNodeExecutor {
  readonly type = 'system.manual.trigger';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Manual Trigger';
  readonly description = 'Manual workflow trigger that processes initial trigger data';

  /**
   * Execute manual trigger
   * @param inputs - Trigger configuration and data
   * @param context - Execution context
   * @returns Processed trigger data
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      triggerData = {},
      validateInput = true,
      enrichData = true,
      metadata = {},
    } = inputs;

    this.logger.debug(`Processing manual trigger for workflow: ${context.workflowId}`);

    try {
      let processedData = { ...triggerData };

      // Validate trigger data if requested
      if (validateInput) {
        await this.validateTriggerData(processedData);
      }

      // Enrich trigger data with context information
      if (enrichData) {
        processedData = await this.enrichTriggerData(processedData, context, metadata);
      }

      // Emit trigger event
      await this.emitEvent('workflow.triggered', {
        executionId: context.executionId,
        workflowId: context.workflowId,
        triggerType: 'manual',
        userId: context.userId,
        dataSize: this.calculateDataSize(processedData),
      }, context);

      const result = {
        triggerType: 'manual',
        data: processedData,
        triggeredAt: new Date().toISOString(),
        triggeredBy: context.userId,
        executionId: context.executionId,
        workflowId: context.workflowId,
        metadata: {
          source: 'manual',
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          ...metadata,
        },
      };

      this.logger.debug(`Manual trigger processed successfully`);

      return result;

    } catch (error) {
      this.logger.error(`Manual trigger processing failed: ${error.message}`);
      throw new Error(`Manual trigger processing failed: ${error.message}`);
    }
  }

  /**
   * Validate trigger data
   * @private
   */
  private async validateTriggerData(data: any): Promise<void> {
    // Basic validation
    if (data === null || data === undefined) {
      throw new Error('Trigger data cannot be null or undefined');
    }

    // Check for common required fields
    if (typeof data === 'object' && !Array.isArray(data)) {
      // Validate object structure
      this.validateObjectStructure(data);
    }

    // Additional validation can be added here based on requirements
  }

  /**
   * Validate object structure
   * @private
   */
  private validateObjectStructure(data: any): void {
    // Check for circular references
    try {
      JSON.stringify(data);
    } catch (error) {
      throw new Error('Trigger data contains circular references');
    }

    // Check data size
    const dataSize = this.calculateDataSize(data);
    const maxSize = 1024 * 1024; // 1MB limit

    if (dataSize > maxSize) {
      throw new Error(`Trigger data size exceeds limit: ${dataSize} bytes > ${maxSize} bytes`);
    }

    // Validate nested depth
    const maxDepth = 10;
    const depth = this.calculateObjectDepth(data);

    if (depth > maxDepth) {
      throw new Error(`Trigger data nesting exceeds limit: ${depth} > ${maxDepth}`);
    }
  }

  /**
   * Enrich trigger data with context information
   * @private
   */
  private async enrichTriggerData(
    data: any,
    context: ExecutionContext,
    metadata: any
  ): Promise<any> {
    const enrichedData = {
      ...data,
      _context: {
        executionId: context.executionId,
        workflowId: context.workflowId,
        userId: context.userId,
        triggeredAt: new Date().toISOString(),
        triggerType: 'manual',
        environment: process.env.NODE_ENV || 'development',
        timestamp: Date.now(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: 'en-US',
      },
      _metadata: {
        ...metadata,
        dataSize: this.calculateDataSize(data),
        dataType: this.getDataType(data),
        processingStarted: Date.now(),
      },
    };

    // Add user information if available
    if (context.userId) {
      enrichedData._context.user = {
        id: context.userId,
        // Additional user info could be fetched here
      };
    }

    // Add workflow information
    enrichedData._context.workflow = {
      id: context.workflowId,
      executionId: context.executionId,
      startTime: context.startTime,
    };

    return enrichedData;
  }

  /**
   * Calculate data size in bytes
   * @private
   */
  private calculateDataSize(data: any): number {
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * Calculate object nesting depth
   * @private
   */
  private calculateObjectDepth(obj: any, depth = 0): number {
    if (obj === null || typeof obj !== 'object') {
      return depth;
    }

    if (Array.isArray(obj)) {
      return Math.max(depth, ...obj.map(item => this.calculateObjectDepth(item, depth + 1)));
    }

    const depths = Object.values(obj).map(value => this.calculateObjectDepth(value, depth + 1));
    return depths.length > 0 ? Math.max(...depths) : depth;
  }

  /**
   * Get data type description
   * @private
   */
  private getDataType(data: any): string {
    if (data === null) return 'null';
    if (Array.isArray(data)) return 'array';
    if (data instanceof Date) return 'date';
    if (typeof data === 'object') return 'object';
    return typeof data;
  }

  /**
   * Process trigger configuration
   * @private
   */
  private async processTriggerConfiguration(config: any): Promise<any> {
    const {
      allowedDataTypes = ['object', 'array', 'string', 'number', 'boolean'],
      maxDataSize = 1024 * 1024, // 1MB
      maxDepth = 10,
      requiredFields = [],
      transformations = [],
    } = config;

    return {
      validation: {
        allowedDataTypes,
        maxDataSize,
        maxDepth,
        requiredFields,
      },
      transformations,
      processed: true,
      processedAt: new Date().toISOString(),
    };
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        triggerData: {
          description: 'Data provided when manually triggering the workflow',
          default: {},
        },
        validateInput: {
          type: 'boolean',
          default: true,
          description: 'Whether to validate the trigger data',
        },
        enrichData: {
          type: 'boolean',
          default: true,
          description: 'Whether to enrich trigger data with context information',
        },
        metadata: {
          type: 'object',
          description: 'Additional metadata to include with the trigger',
          default: {},
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        triggerType: {
          type: 'string',
          enum: ['manual'],
          description: 'Type of trigger that initiated the workflow',
        },
        data: {
          description: 'Processed trigger data',
        },
        triggeredAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when the workflow was triggered',
        },
        triggeredBy: {
          type: 'number',
          description: 'User ID who triggered the workflow',
        },
        executionId: {
          type: 'string',
          description: 'Unique execution identifier',
        },
        workflowId: {
          type: 'string',
          description: 'Workflow identifier',
        },
        metadata: {
          type: 'object',
          properties: {
            source: {
              type: 'string',
              description: 'Source of the trigger',
            },
            version: {
              type: 'string',
              description: 'Trigger processor version',
            },
            environment: {
              type: 'string',
              description: 'Environment where trigger was processed',
            },
          },
        },
      },
    };
  }

  /**
   * Check if node is ready to execute
   */
  async isReady(context: ExecutionContext): Promise<boolean> {
    // Manual triggers are always ready to execute
    return true;
  }

  /**
   * Get node capabilities
   */
  getCapabilities(): string[] {
    return [
      'trigger',
      'data-validation',
      'data-enrichment',
      'context-injection',
      'metadata-processing',
    ];
  }

  /**
   * Get node configuration options
   */
  getConfigurationOptions(): Record<string, any> {
    return {
      validation: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean', default: true },
          maxDataSize: { type: 'number', default: 1048576 }, // 1MB
          maxDepth: { type: 'number', default: 10 },
          allowedTypes: {
            type: 'array',
            items: { type: 'string' },
            default: ['object', 'array', 'string', 'number', 'boolean'],
          },
        },
      },
      enrichment: {
        type: 'object',
        properties: {
          enabled: { type: 'boolean', default: true },
          includeContext: { type: 'boolean', default: true },
          includeMetadata: { type: 'boolean', default: true },
          includeUserInfo: { type: 'boolean', default: true },
        },
      },
    };
  }
}
