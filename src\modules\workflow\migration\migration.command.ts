import { Command, CommandRunner, Option } from 'nest-commander';
import { Logger } from '@nestjs/common';
import { ServiceMigrationUtility } from './service-migration.utility';
import * as fs from 'fs';
import * as path from 'path';

/**
 * CLI command for service migration operations
 * Provides commands to analyze, plan, and execute service migrations
 */
@Command({
  name: 'migrate',
  description: 'Service migration commands for BE App to BE Worker',
})
export class MigrationCommand extends CommandRunner {
  private readonly logger = new Logger(MigrationCommand.name);

  constructor(private readonly migrationUtility: ServiceMigrationUtility) {
    super();
  }

  async run(passedParams: string[], options?: MigrationCommandOptions): Promise<void> {
    const command = passedParams[0];

    switch (command) {
      case 'analyze':
        await this.analyzeServices(options);
        break;
      case 'plan':
        await this.generatePlan(options);
        break;
      case 'generate':
        await this.generateExecutors(options);
        break;
      case 'status':
        await this.showMigrationStatus(options);
        break;
      default:
        this.showHelp();
    }
  }

  /**
   * Analyze existing services for migration
   */
  private async analyzeServices(options?: MigrationCommandOptions): Promise<void> {
    this.logger.log('Starting service analysis...');

    try {
      const beAppPath = options?.beAppPath || '../redai-v201-be-app';
      const outputPath = options?.output || './docs/migration-analysis.json';

      // Analyze services
      const analysis = await this.migrationUtility.analyzeExistingServices(beAppPath);

      // Save analysis report
      fs.writeFileSync(outputPath, JSON.stringify(analysis, null, 2));

      // Display summary
      console.log('\n=== Service Analysis Summary ===');
      console.log(`Total Services: ${analysis.totalServices}`);
      console.log(`Complexity Score: ${analysis.complexityScore}/100`);
      console.log('\nServices by Category:');
      
      Object.entries(analysis.servicesByCategory).forEach(([category, info]) => {
        console.log(`  ${category}: ${info.services.length} services (${info.migrationEstimate}h estimated)`);
      });

      console.log('\nTop Migration Candidates:');
      analysis.migrationCandidates.slice(0, 10).forEach((candidate, index) => {
        console.log(`  ${index + 1}. ${candidate.category}.${candidate.serviceName} (${candidate.priority} priority, ${candidate.estimatedHours}h)`);
      });

      if (analysis.recommendations.length > 0) {
        console.log('\nRecommendations:');
        analysis.recommendations.forEach(rec => console.log(`  - ${rec}`));
      }

      console.log(`\nDetailed analysis saved to: ${outputPath}`);

    } catch (error) {
      this.logger.error('Service analysis failed:', error);
      process.exit(1);
    }
  }

  /**
   * Generate migration plan for specific category
   */
  private async generatePlan(options?: MigrationCommandOptions): Promise<void> {
    this.logger.log('Generating migration plan...');

    try {
      const category = options?.category;
      if (!category) {
        console.error('Category is required. Use --category google|facebook|zalo');
        process.exit(1);
      }

      const beAppPath = options?.beAppPath || '../redai-v201-be-app';
      const outputPath = options?.output || `./docs/migration-plan-${category}.json`;

      // Analyze services first
      const analysis = await this.migrationUtility.analyzeExistingServices(beAppPath);
      const categoryInfo = analysis.servicesByCategory[category];

      if (!categoryInfo) {
        console.error(`Category '${category}' not found in analysis`);
        process.exit(1);
      }

      // Generate migration plan
      const plan = this.migrationUtility.generateMigrationPlan(category, categoryInfo.services);

      // Save plan
      fs.writeFileSync(outputPath, JSON.stringify(plan, null, 2));

      // Display plan summary
      console.log(`\n=== Migration Plan: ${category.toUpperCase()} ===`);
      console.log(`Total Services: ${plan.totalServices}`);
      console.log(`Estimated Hours: ${plan.estimatedHours}h`);
      console.log(`Phases: ${plan.phases.length}`);

      console.log('\nMigration Phases:');
      plan.phases.forEach((phase, index) => {
        console.log(`  ${index + 1}. ${phase.name} (${phase.estimatedHours}h)`);
        console.log(`     ${phase.description}`);
        console.log(`     Tasks: ${phase.tasks.length}`);
        if (phase.dependencies.length > 0) {
          console.log(`     Dependencies: ${phase.dependencies.join(', ')}`);
        }
      });

      if (plan.risks.length > 0) {
        console.log('\nIdentified Risks:');
        plan.risks.forEach(risk => console.log(`  - ${risk}`));
      }

      console.log('\nSuccess Criteria:');
      plan.successCriteria.forEach(criteria => console.log(`  - ${criteria}`));

      console.log(`\nDetailed plan saved to: ${outputPath}`);

    } catch (error) {
      this.logger.error('Migration plan generation failed:', error);
      process.exit(1);
    }
  }

  /**
   * Generate executor templates for services
   */
  private async generateExecutors(options?: MigrationCommandOptions): Promise<void> {
    this.logger.log('Generating executor templates...');

    try {
      const category = options?.category;
      const service = options?.service;

      if (!category) {
        console.error('Category is required. Use --category google|facebook|zalo');
        process.exit(1);
      }

      const beAppPath = options?.beAppPath || '../redai-v201-be-app';
      const outputDir = options?.output || `./src/modules/workflow/executors/${category}`;

      // Analyze services
      const analysis = await this.migrationUtility.analyzeExistingServices(beAppPath);
      const categoryInfo = analysis.servicesByCategory[category];

      if (!categoryInfo) {
        console.error(`Category '${category}' not found in analysis`);
        process.exit(1);
      }

      // Create output directory
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      let servicesToGenerate = categoryInfo.services;

      // Filter by specific service if provided
      if (service) {
        servicesToGenerate = categoryInfo.services.filter(s => s.name === service);
        if (servicesToGenerate.length === 0) {
          console.error(`Service '${service}' not found in category '${category}'`);
          process.exit(1);
        }
      }

      console.log(`\n=== Generating Executors: ${category.toUpperCase()} ===`);
      console.log(`Services to generate: ${servicesToGenerate.length}`);

      // Generate executor templates
      for (const serviceInfo of servicesToGenerate) {
        const executorCode = this.migrationUtility.generateExecutorTemplate(
          serviceInfo.name,
          category,
          serviceInfo
        );

        const fileName = `${serviceInfo.name.toLowerCase()}.executor.ts`;
        const filePath = path.join(outputDir, fileName);

        fs.writeFileSync(filePath, executorCode);
        console.log(`  ✓ Generated: ${fileName}`);
      }

      // Generate index file
      const indexContent = this.generateIndexFile(servicesToGenerate, category);
      const indexPath = path.join(outputDir, 'index.ts');
      fs.writeFileSync(indexPath, indexContent);
      console.log(`  ✓ Generated: index.ts`);

      console.log(`\nExecutor templates generated in: ${outputDir}`);
      console.log('\nNext steps:');
      console.log('1. Implement the actual service logic in each executor');
      console.log('2. Add proper input/output schemas');
      console.log('3. Add authentication and error handling');
      console.log('4. Create unit tests');
      console.log('5. Register executors in the module');

    } catch (error) {
      this.logger.error('Executor generation failed:', error);
      process.exit(1);
    }
  }

  /**
   * Show migration status
   */
  private async showMigrationStatus(options?: MigrationCommandOptions): Promise<void> {
    console.log('\n=== Migration Status ===');
    
    const categories = ['google', 'facebook', 'zalo'];
    
    for (const category of categories) {
      const executorPath = `./src/modules/workflow/executors/${category}`;
      const exists = fs.existsSync(executorPath);
      
      if (exists) {
        const files = fs.readdirSync(executorPath).filter(f => f.endsWith('.executor.ts'));
        console.log(`${category.toUpperCase()}: ${files.length} executors generated`);
        files.forEach(file => {
          console.log(`  ✓ ${file}`);
        });
      } else {
        console.log(`${category.toUpperCase()}: Not started`);
      }
    }
  }

  /**
   * Generate index file for executors
   * @private
   */
  private generateIndexFile(services: any[], category: string): string {
    const exports = services.map(service => {
      const className = this.toPascalCase(`${service.name}-executor`);
      const fileName = `${service.name.toLowerCase()}.executor`;
      return `export * from './${fileName}';`;
    }).join('\n');

    return `// Export all ${category} node executors
${exports}
`;
  }

  /**
   * Convert string to PascalCase
   * @private
   */
  private toPascalCase(str: string): string {
    return str
      .split(/[-_\s]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * Show help information
   * @private
   */
  private showHelp(): void {
    console.log('\n=== Service Migration Commands ===');
    console.log('');
    console.log('Commands:');
    console.log('  analyze    Analyze existing services for migration');
    console.log('  plan       Generate migration plan for a category');
    console.log('  generate   Generate executor templates');
    console.log('  status     Show migration status');
    console.log('');
    console.log('Options:');
    console.log('  --be-app-path    Path to BE App source directory');
    console.log('  --category       Service category (google|facebook|zalo)');
    console.log('  --service        Specific service name');
    console.log('  --output         Output file or directory path');
    console.log('');
    console.log('Examples:');
    console.log('  npm run migrate analyze');
    console.log('  npm run migrate plan --category google');
    console.log('  npm run migrate generate --category google');
    console.log('  npm run migrate generate --category google --service ads');
    console.log('  npm run migrate status');
  }

  @Option({
    flags: '--be-app-path <path>',
    description: 'Path to BE App source directory',
  })
  parseBeAppPath(val: string): string {
    return val;
  }

  @Option({
    flags: '--category <category>',
    description: 'Service category (google|facebook|zalo)',
  })
  parseCategory(val: string): string {
    return val;
  }

  @Option({
    flags: '--service <service>',
    description: 'Specific service name',
  })
  parseService(val: string): string {
    return val;
  }

  @Option({
    flags: '--output <path>',
    description: 'Output file or directory path',
  })
  parseOutput(val: string): string {
    return val;
  }
}

interface MigrationCommandOptions {
  beAppPath?: string;
  category?: string;
  service?: string;
  output?: string;
}
