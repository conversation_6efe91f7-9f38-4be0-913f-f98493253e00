import { Test, TestingModule } from '@nestjs/testing';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext, NodeExecutionResult } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';
import { LoggingService } from '../../services/logging.service';
import { EventService } from '../../services/event.service';

// Mock implementation của BaseNodeExecutor cho testing
class TestNodeExecutor extends BaseNodeExecutor {
  readonly type = 'test.node';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Test Node';
  readonly description = 'Test node for unit testing';

  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    if (inputs.shouldFail) {
      throw new Error('Test execution failure');
    }
    return { message: 'Test execution successful', input: inputs };
  }

  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        message: { type: 'string' },
        shouldFail: { type: 'boolean' },
      },
      required: ['message'],
    };
  }

  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        message: { type: 'string' },
        input: { type: 'object' },
      },
    };
  }
}

describe('BaseNodeExecutor', () => {
  let executor: TestNodeExecutor;
  let mockContext: ExecutionContext;
  let mockLoggingService: jest.Mocked<LoggingService>;
  let mockEventService: jest.Mocked<EventService>;

  beforeEach(async () => {
    mockLoggingService = {
      logNodeEvent: jest.fn(),
    } as any;

    mockEventService = {
      emitNodeEvent: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TestNodeExecutor,
        { provide: LoggingService, useValue: mockLoggingService },
        { provide: EventService, useValue: mockEventService },
      ],
    }).compile();

    executor = module.get<TestNodeExecutor>(TestNodeExecutor);

    // Setup mock context
    mockContext = {
      executionId: 'test-execution-123',
      workflowId: 'test-workflow-456',
      userId: 1,
      triggerType: 'manual',
      triggerData: { test: 'data' },

      getNodeOutput: jest.fn(),
      setNodeOutput: jest.fn(),
      getNodeInput: jest.fn(),
      isNodeExecuted: jest.fn().mockReturnValue(false),
      getExecutedNodes: jest.fn().mockReturnValue([]),
      logService: mockLoggingService,
      loggingService: mockLoggingService,
      eventService: mockEventService,
      startTime: Date.now(),
      variables: {},
      nodeResults: {},
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Properties', () => {
    it('should have correct type and metadata', () => {
      expect(executor.type).toBe('test.node');
      expect(executor.category).toBe(NodeCategory.SYSTEM);
      expect(executor.name).toBe('Test Node');
      expect(executor.description).toBe('Test node for unit testing');
      expect(executor.version).toBe('1.0.0');
    });

    it('should have valid input and output schemas', () => {
      const inputSchema = executor.getInputSchema();
      const outputSchema = executor.getOutputSchema();

      expect(inputSchema).toHaveProperty('type', 'object');
      expect(inputSchema).toHaveProperty('properties');
      expect(inputSchema).toHaveProperty('required');

      expect(outputSchema).toHaveProperty('type', 'object');
      expect(outputSchema).toHaveProperty('properties');
    });
  });

  describe('execute()', () => {
    it('should execute successfully with valid inputs', async () => {
      const inputs = { message: 'Hello World' };
      
      const result = await executor.execute(inputs, mockContext);

      expect(result.success).toBe(true);
      expect(result.output).toEqual({
        message: 'Test execution successful',
        input: inputs,
      });
      expect(result.metadata).toHaveProperty('executionTime');
      expect(result.metadata).toHaveProperty('nodeType', 'test.node');
      expect(result.metadata).toHaveProperty('timestamp');
    });

    it('should handle execution errors gracefully', async () => {
      const inputs = { message: 'Test', shouldFail: true };
      
      const result = await executor.execute(inputs, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Test execution failure');
      expect(result.errorDetails).toHaveProperty('stack');
      expect(result.errorDetails).toHaveProperty('nodeType', 'test.node');
      expect(result.errorDetails).toHaveProperty('inputs', inputs);
    });

    it('should validate inputs before execution', async () => {
      const invalidInputs = { shouldFail: false }; // Missing required 'message'
      
      const result = await executor.execute(invalidInputs, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });

    it('should log execution events', async () => {
      const inputs = { message: 'Test logging' };
      
      await executor.execute(inputs, mockContext);

      // Verify logging calls were made
      expect(mockLoggingService.logNodeEvent).toHaveBeenCalledTimes(2); // start and success
      expect(mockEventService.emitNodeEvent).toHaveBeenCalledTimes(2);
    });
  });

  describe('validateInputs()', () => {
    it('should validate correct inputs', () => {
      const validInputs = { message: 'Hello', shouldFail: false };
      
      expect(() => executor.validateInputs(validInputs)).not.toThrow();
    });

    it('should reject invalid inputs', () => {
      const invalidInputs = { shouldFail: 'not-boolean' }; // Wrong type
      
      expect(() => executor.validateInputs(invalidInputs)).toThrow();
    });

    it('should reject missing required fields', () => {
      const incompleteInputs = { shouldFail: false }; // Missing 'message'
      
      expect(() => executor.validateInputs(incompleteInputs)).toThrow();
    });
  });

  describe('isReady()', () => {
    it('should return true by default', async () => {
      const isReady = await executor.isReady(mockContext);
      expect(isReady).toBe(true);
    });
  });

  describe('cleanup()', () => {
    it('should complete without errors', async () => {
      await expect(executor.cleanup(mockContext)).resolves.toBeUndefined();
    });
  });

  describe('Helper Methods', () => {
    it('should get node config from context', () => {
      const config = (executor as any).getNodeConfig(mockContext);
      expect(config).toEqual({});
    });

    it('should get user credentials', async () => {
      const credentials = await (executor as any).getUserCredentials(mockContext, 'google');
      expect(credentials).toEqual({});
    });

    it('should emit events', async () => {
      await (executor as any).emitEvent('test.event', { data: 'test' }, mockContext);
      // Should not throw error
    });
  });

  describe('Error Handling', () => {
    it('should handle input resolution errors', async () => {
      // Mock input resolver to throw error
      const originalResolver = (executor as any).inputResolver;
      (executor as any).inputResolver = {
        resolveInputs: jest.fn().mockRejectedValue(new Error('Resolution failed')),
      };

      const result = await executor.execute({ message: 'test' }, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Resolution failed');

      // Restore original resolver
      (executor as any).inputResolver = originalResolver;
    });

    it('should handle validation errors', async () => {
      // Mock validation helper to throw error
      const originalValidator = (executor as any).validationHelper;
      (executor as any).validationHelper = {
        validateAgainstSchema: jest.fn().mockImplementation(() => {
          throw new Error('Validation error');
        }),
      };

      const result = await executor.execute({ message: 'test' }, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation error');

      // Restore original validator
      (executor as any).validationHelper = originalValidator;
    });

    it('should handle isReady() returning false', async () => {
      // Override isReady to return false
      jest.spyOn(executor, 'isReady').mockResolvedValue(false);

      const result = await executor.execute({ message: 'test' }, mockContext);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not ready for execution');
    });
  });

  describe('Execution Timing', () => {
    it('should measure execution time accurately', async () => {
      const inputs = { message: 'Timing test' };
      
      const startTime = Date.now();
      const result = await executor.execute(inputs, mockContext);
      const endTime = Date.now();

      expect(result.metadata?.executionTime).toBeGreaterThan(0);
      expect(result.metadata?.executionTime).toBeLessThan(endTime - startTime + 100); // Allow some margin
    });

    it('should include execution time in error results', async () => {
      const inputs = { message: 'Error timing test', shouldFail: true };
      
      const result = await executor.execute(inputs, mockContext);

      expect(result.success).toBe(false);
      expect(result.metadata?.executionTime).toBeGreaterThan(0);
    });
  });

  describe('Metadata', () => {
    it('should include correct metadata in successful results', async () => {
      const inputs = { message: 'Metadata test' };
      
      const result = await executor.execute(inputs, mockContext);

      expect(result.metadata).toEqual({
        executionTime: expect.any(Number),
        nodeType: 'test.node',
        timestamp: expect.any(Number),
      });
    });

    it('should include correct metadata in error results', async () => {
      const inputs = { message: 'Error metadata test', shouldFail: true };
      
      const result = await executor.execute(inputs, mockContext);

      expect(result.metadata).toEqual({
        executionTime: expect.any(Number),
        nodeType: 'test.node',
        timestamp: expect.any(Number),
      });
    });
  });
});
