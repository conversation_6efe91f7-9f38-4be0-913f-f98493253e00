import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
} from 'typeorm';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';
import { IExecutionData, IExecutionErrorDetails } from '../interfaces';

@Entity('executions')
export class Execution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'workflow_id', type: 'uuid' })
  workflowId: string;

  @Column({
    type: 'enum',
    enum: ExecutionStatusEnum,
    default: ExecutionStatusEnum.SUCCEEDED,
  })
  status: ExecutionStatusEnum;

  @Column({ type: 'jsonb', nullable: true })
  input: IExecutionData;

  @Column({ type: 'jsonb', nullable: true })
  output: IExecutionData;

  @Column({ type: 'jsonb', nullable: true })
  context: IExecutionData;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'error_details', type: 'jsonb', nullable: true })
  errorDetails: IExecutionErrorDetails;

  @Column({ name: 'triggered_by', type: 'uuid', nullable: true })
  triggeredBy: string;

  @Column({ name: 'trigger_type', type: 'varchar', length: 100, nullable: true })
  triggerType: string;

  @Column({
    name: 'started_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  startedAt: number;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ name: 'finished_at', type: 'bigint', nullable: true })
  finishedAt: number;

  @Column({ type: 'int', default: 0 })
  duration: number;
}
