import { IsBoolean, IsArray, IsString, IsOptional, IsObject, ValidateNested, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { SharedWorkflowDefinitionDto } from './workflow-definition.dto';

/**
 * Shared import/export DTOs between BE App and BE Worker
 * Synced with BE-003 import/export service for consistency
 */

/**
 * Workflow export data DTO - synced với BE-003
 */
export class WorkflowExportDataDto {
  @IsObject()
  @ValidateNested()
  @Type(() => WorkflowExportMetadataDto)
  workflow: WorkflowExportMetadataDto;
}

/**
 * Workflow export metadata DTO
 */
export class WorkflowExportMetadataDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @ValidateNested()
  @Type(() => SharedWorkflowDefinitionDto)
  definition: SharedWorkflowDefinitionDto;

  @IsObject()
  @ValidateNested()
  @Type(() => ExportMetadataDto)
  metadata: ExportMetadataDto;
}

/**
 * Export metadata DTO
 */
export class ExportMetadataDto {
  @IsNumber()
  exportedAt: number;

  @IsNumber()
  exportedBy: number;

  @IsString()
  version: string;

  @IsString()
  schemaVersion: string;

  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;

  @IsOptional()
  @IsNumber()
  templateCreatedAt?: number;

  @IsOptional()
  @IsNumber()
  templateCreatedBy?: number;

  @IsOptional()
  @IsString()
  originalWorkflowId?: string;
}

/**
 * Import options DTO - synced với BE-003
 */
export class ImportOptionsDto {
  @IsOptional()
  @IsBoolean()
  overwriteExisting?: boolean;

  @IsOptional()
  @IsBoolean()
  validateOnly?: boolean;

  @IsOptional()
  @IsBoolean()
  preserveIds?: boolean;

  @IsOptional()
  @IsString()
  namePrefix?: string;

  @IsOptional()
  @IsString()
  nameSuffix?: string;

  @IsOptional()
  @IsBoolean()
  skipValidation?: boolean;

  @IsOptional()
  @IsBoolean()
  enableBackup?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeNodeTypes?: string[];

  @IsOptional()
  @IsObject()
  variableMapping?: Record<string, any>;
}

/**
 * Import result DTO - synced với BE-003
 */
export class ImportResultDto {
  @IsBoolean()
  success: boolean;

  @IsOptional()
  @IsString()
  workflowId?: string;

  @IsOptional()
  @IsObject()
  workflow?: any;

  @IsOptional()
  @IsObject()
  validation?: any;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  errors?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  warnings?: string[];

  @IsOptional()
  @IsObject()
  metadata?: {
    importedAt: number;
    importedBy: number;
    originalName: string;
    finalName: string;
    nodeCount: number;
    edgeCount: number;
    processingTime: number;
  };
}

/**
 * Bulk import request DTO
 */
export class BulkImportRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowExportDataDto)
  workflows: WorkflowExportDataDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => ImportOptionsDto)
  options?: ImportOptionsDto;

  @IsOptional()
  @IsBoolean()
  continueOnError?: boolean;

  @IsOptional()
  @IsNumber()
  batchSize?: number;
}

/**
 * Bulk import result DTO
 */
export class BulkImportResultDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImportResultDto)
  results: ImportResultDto[];

  @IsObject()
  summary: {
    total: number;
    successful: number;
    failed: number;
    skipped: number;
    processingTime: number;
  };

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  globalErrors?: string[];
}

/**
 * Template creation request DTO
 */
export class TemplateCreationRequestDto {
  @IsString()
  workflowId: string;

  @IsString()
  templateName: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @IsOptional()
  @IsObject()
  templateMetadata?: {
    author?: string;
    version?: string;
    compatibility?: string[];
    requirements?: string[];
  };
}

/**
 * Template export result DTO
 */
export class TemplateExportResultDto {
  @IsBoolean()
  success: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExportDataDto)
  template?: WorkflowExportDataDto;

  @IsOptional()
  @IsString()
  templateId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  errors?: string[];

  @IsOptional()
  @IsObject()
  metadata?: {
    createdAt: number;
    createdBy: number;
    originalWorkflowId: string;
    templateName: string;
    nodeCount: number;
    edgeCount: number;
    processingTime: number;
  };
}

/**
 * Workflow comparison DTO for import validation
 */
export class WorkflowComparisonDto {
  @IsString()
  sourceWorkflowId: string;

  @IsString()
  targetWorkflowId: string;

  @IsObject()
  differences: {
    nodes: {
      added: string[];
      removed: string[];
      modified: string[];
    };
    edges: {
      added: string[];
      removed: string[];
      modified: string[];
    };
    metadata: {
      changed: string[];
    };
  };

  @IsObject()
  compatibility: {
    isCompatible: boolean;
    issues: string[];
    warnings: string[];
  };

  @IsOptional()
  @IsObject()
  migrationPlan?: {
    steps: Array<{
      type: string;
      description: string;
      automated: boolean;
    }>;
    estimatedTime: number;
    riskLevel: 'low' | 'medium' | 'high';
  };
}
