import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng workflow_execution_logs trong cơ sở dữ liệu
 * Cung cấp một dấu vết kiểm toán chi tiết cho từng bước thực thi workflow
 * Synced with BE App for Worker execution
 */
@Entity('workflow_execution_logs')
@Index('idx_workflow_execution_logs_execution_id', ['workflowExecutionId'])
export class WorkflowExecutionLog {
  /**
   * ID định danh duy nhất cho một log entry
   */
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  /**
   * Khóa ngoại, liên kết đến lần thực thi workflow
   */
  @Column({ name: 'workflow_execution_id', type: 'uuid', nullable: false })
  workflowExecutionId: string;

  /**
   * ID của node đư<PERSON><PERSON> thực thi
   */
  @Column({ name: 'node_id', type: 'varchar', length: 255, nullable: false })
  nodeId: string;

  /**
   * <PERSON><PERSON><PERSON> sự kiện (e.g., 'node.start', 'node.success', 'node.error')
   */
  @Column({ name: 'event_type', type: 'varchar', length: 50, nullable: false })
  eventType: string;

  /**
   * Dữ liệu payload của sự kiện, lưu dưới dạng JSONB
   */
  @Column({ name: 'payload', type: 'jsonb', nullable: true })
  payload: Record<string, any> | null;

  /**
   * Thời điểm xảy ra sự kiện, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'timestamp',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  timestamp: number;
}
