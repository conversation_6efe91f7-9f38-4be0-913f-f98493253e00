import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsBoolean, IsObject, IsEnum, IsArray, ValidateNested, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * DTO cho workflow execution job options
 */
export class WorkflowExecutionOptionsDto {
  @IsOptional()
  @IsBoolean()
  enableSSE?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(3600000) // Max 1 hour
  timeout?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @IsOptional()
  @IsBoolean()
  enableRetry?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(10)
  maxRetries?: number;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000) // Max 5 minutes
  retryDelay?: number;
}

/**
 * DTO cho workflow execution job metadata
 */
export class WorkflowExecutionMetadataDto {
  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsString()
  clientIp?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsObject()
  tracking?: Record<string, any>;
}

/**
 * DTO cho workflow execution job
 * Synced với BE App để đảm bảo consistency
 */
export class WorkflowExecutionJobDto {
  @IsString()
  executionId: string;

  @IsString()
  workflowId: string;

  @IsNumber()
  userId: number;

  @IsString()
  triggerType: string;

  @IsObject()
  triggerData: any;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionOptionsDto)
  options?: WorkflowExecutionOptionsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionMetadataDto)
  metadata?: WorkflowExecutionMetadataDto;
}

/**
 * DTO cho webhook trigger data
 */
export class WebhookTriggerDataDto {
  @IsEnum(['facebook', 'zalo', 'google', 'custom'])
  source: 'facebook' | 'zalo' | 'google' | 'custom';

  @IsString()
  eventType: string;

  @IsObject()
  payload: Record<string, any>;

  @IsObject()
  headers: Record<string, string>;

  @IsNumber()
  timestamp: number;

  @IsBoolean()
  verified: boolean;
}

/**
 * DTO cho manual trigger data
 */
export class ManualTriggerDataDto {
  @IsObject()
  inputData: Record<string, any>;

  @IsEnum(['ui', 'api', 'test'])
  source: 'ui' | 'api' | 'test';

  @IsNumber()
  triggeredBy: number;

  @IsNumber()
  timestamp: number;
}

/**
 * DTO cho scheduled trigger data
 */
export class ScheduledTriggerDataDto {
  @IsString()
  scheduleId: string;

  @IsString()
  cronExpression: string;

  @IsNumber()
  scheduledTime: number;

  @IsNumber()
  executionTime: number;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * Union type cho trigger data validation
 */
export type TriggerDataDto = WebhookTriggerDataDto | ManualTriggerDataDto | ScheduledTriggerDataDto;

/**
 * DTO cho workflow execution request từ BE App
 */
export class CreateWorkflowExecutionJobDto {
  @IsString()
  workflowId: string;

  @IsNumber()
  userId: number;

  @IsString()
  triggerType: string;

  @IsObject()
  triggerData: TriggerDataDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionOptionsDto)
  options?: WorkflowExecutionOptionsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionMetadataDto)
  metadata?: WorkflowExecutionMetadataDto;
}

/**
 * DTO cho bulk workflow execution
 */
export class BulkWorkflowExecutionJobDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateWorkflowExecutionJobDto)
  jobs: CreateWorkflowExecutionJobDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowExecutionOptionsDto)
  defaultOptions?: WorkflowExecutionOptionsDto;
}
