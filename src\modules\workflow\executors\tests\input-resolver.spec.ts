import { Test, TestingModule } from '@nestjs/testing';
import { InputResolver } from '../base/input-resolver';
import { ExecutionContext } from '../../interfaces/execution-context.interface';

describe('InputResolver', () => {
  let resolver: InputResolver;
  let mockContext: ExecutionContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InputResolver],
    }).compile();

    resolver = module.get<InputResolver>(InputResolver);

    // Setup mock context
    mockContext = {
      executionId: 'test-execution-123',
      workflowId: 'test-workflow-456',
      userId: 1,
      triggerType: 'webhook.facebook',
      triggerData: {
        message: 'Hello from Facebook',
        user: { id: '12345', name: '<PERSON>' },
      },
      getNodeOutput: jest.fn((nodeId: string) => {
        const outputs = new Map([
          ['node1', { result: 'Node 1 output', count: 42 }],
          ['node2', { data: { items: ['a', 'b', 'c'] } }],
        ]);
        return outputs.get(nodeId);
      }),
      setNodeOutput: jest.fn(),
      getNodeInput: jest.fn(),
      isNodeExecuted: jest.fn().mockReturnValue(false),
      getExecutedNodes: jest.fn().mockReturnValue([]),
      logService: {} as any,
      loggingService: {} as any,
      eventService: {} as any,
      startTime: Date.now(),
      variables: {},
      nodeResults: {},
    };
  });

  describe('resolveInputs()', () => {
    it('should return inputs unchanged when no expressions', async () => {
      const inputs = {
        message: 'Hello World',
        count: 42,
        enabled: true,
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual(inputs);
    });

    it('should handle null and undefined inputs', async () => {
      expect(await resolver.resolveInputs(null, mockContext)).toBeNull();
      expect(await resolver.resolveInputs(undefined, mockContext)).toBeUndefined();
    });

    it('should resolve simple string expressions', async () => {
      const inputs = {
        message: '{{trigger.message}}',
        userId: '{{workflow.userId}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        message: 'Hello from Facebook',
        userId: 1,
      });
    });

    it('should resolve nested object expressions', async () => {
      const inputs = {
        user: {
          id: '{{trigger.user.id}}',
          name: '{{trigger.user.name}}',
        },
        nodeResult: '{{node1.result}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        user: {
          id: '12345',
          name: 'John Doe',
        },
        nodeResult: 'Node 1 output',
      });
    });

    it('should resolve array expressions', async () => {
      const inputs = {
        items: ['{{node2.data.items}}', '{{trigger.message}}'],
        mixed: [1, '{{node1.count}}', true],
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        items: [['a', 'b', 'c'], 'Hello from Facebook'],
        mixed: [1, 42, true],
      });
    });

    it('should handle string interpolation', async () => {
      const inputs = {
        greeting: 'Hello {{trigger.user.name}}, you have {{node1.count}} messages',
        template: 'User {{trigger.user.id}} said: {{trigger.message}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        greeting: 'Hello John Doe, you have 42 messages',
        template: 'User 12345 said: Hello from Facebook',
      });
    });
  });

  describe('Expression Types', () => {
    it('should resolve trigger expressions', async () => {
      const inputs = {
        message: '{{trigger.message}}',
        userId: '{{trigger.user.id}}',
        userName: '{{trigger.user.name}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        message: 'Hello from Facebook',
        userId: '12345',
        userName: 'John Doe',
      });
    });

    it('should resolve workflow expressions', async () => {
      const inputs = {
        workflowId: '{{workflow.id}}',
        executionId: '{{workflow.executionId}}',
        userId: '{{workflow.userId}}',
        triggerType: '{{workflow.triggerType}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        workflowId: 'test-workflow-456',
        executionId: 'test-execution-123',
        userId: 1,
        triggerType: 'webhook.facebook',
      });
    });

    it('should resolve execution expressions', async () => {
      const inputs = {
        executionId: '{{execution.id}}',
        workflowId: '{{execution.workflowId}}',
        userId: '{{execution.userId}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        executionId: 'test-execution-123',
        workflowId: 'test-workflow-456',
        userId: 1,
      });
    });

    it('should resolve node output expressions', async () => {
      const inputs = {
        nodeResult: '{{node1.result}}',
        nodeCount: '{{node1.count}}',
        nodeData: '{{node2.data}}',
        nodeItems: '{{node2.data.items}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        nodeResult: 'Node 1 output',
        nodeCount: 42,
        nodeData: { items: ['a', 'b', 'c'] },
        nodeItems: ['a', 'b', 'c'],
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle undefined expressions gracefully', async () => {
      const inputs = {
        missing: '{{trigger.nonexistent}}',
        nested: '{{node1.missing.field}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        missing: undefined,
        nested: undefined,
      });
    });

    it('should handle missing node outputs', async () => {
      const inputs = {
        missingNode: '{{node999.output}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        missingNode: undefined,
      });
    });

    it('should return original expression on resolution failure', async () => {
      // Mock getNodeOutput to throw error
      mockContext.getNodeOutput = jest.fn().mockImplementation(() => {
        throw new Error('Node output error');
      });

      const inputs = {
        nodeResult: '{{node1.result}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result).toEqual({
        nodeResult: '{{node1.result}}', // Original expression returned
      });
    });

    it('should handle malformed expressions', async () => {
      const inputs = {
        malformed1: '{{}}',
        malformed2: '{{invalid.}}',
        malformed3: '{{.invalid}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      // Should not throw, but return original expressions
      expect(result.malformed1).toBe('{{}}');
      expect(result.malformed2).toBe('{{invalid.}}');
      expect(result.malformed3).toBe('{{.invalid}}');
    });
  });

  describe('Value Conversion', () => {
    it('should convert objects to JSON strings in interpolation', async () => {
      const inputs = {
        objectString: 'Data: {{node2.data}}',
        arrayString: 'Items: {{node2.data.items}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result.objectString).toBe('Data: {"items":["a","b","c"]}');
      expect(result.arrayString).toBe('Items: ["a","b","c"]');
    });

    it('should handle null and undefined in interpolation', async () => {
      const inputs = {
        nullValue: 'Value: {{trigger.nonexistent}}',
        undefinedValue: 'Missing: {{node999.output}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result.nullValue).toBe('Value: ');
      expect(result.undefinedValue).toBe('Missing: ');
    });

    it('should preserve data types for single expressions', async () => {
      const inputs = {
        number: '{{node1.count}}',
        object: '{{node2.data}}',
        array: '{{node2.data.items}}',
        string: '{{trigger.message}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(typeof result.number).toBe('number');
      expect(typeof result.object).toBe('object');
      expect(Array.isArray(result.array)).toBe(true);
      expect(typeof result.string).toBe('string');
    });
  });

  describe('Utility Methods', () => {
    it('should detect expressions in strings', () => {
      expect(resolver.hasExpressions('{{trigger.message}}')).toBe(true);
      expect(resolver.hasExpressions('Hello {{user.name}}')).toBe(true);
      expect(resolver.hasExpressions('No expressions here')).toBe(false);
      expect(resolver.hasExpressions('')).toBe(false);
    });

    it('should extract expressions from strings', () => {
      const expressions1 = resolver.extractExpressions('{{trigger.message}}');
      expect(expressions1).toEqual(['trigger.message']);

      const expressions2 = resolver.extractExpressions('Hello {{user.name}}, you have {{count}} messages');
      expect(expressions2).toEqual(['user.name', 'count']);

      const expressions3 = resolver.extractExpressions('No expressions');
      expect(expressions3).toEqual([]);
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle deeply nested objects', async () => {
      const inputs = {
        level1: {
          level2: {
            level3: {
              message: '{{trigger.message}}',
              data: '{{node1.result}}',
            },
          },
        },
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result.level1.level2.level3.message).toBe('Hello from Facebook');
      expect(result.level1.level2.level3.data).toBe('Node 1 output');
    });

    it('should handle mixed arrays with expressions', async () => {
      const inputs = {
        mixedArray: [
          '{{trigger.message}}',
          42,
          { id: '{{trigger.user.id}}', name: '{{trigger.user.name}}' },
          ['{{node1.count}}', 'static'],
        ],
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result.mixedArray).toEqual([
        'Hello from Facebook',
        42,
        { id: '12345', name: 'John Doe' },
        [42, 'static'],
      ]);
    });

    it('should handle multiple expressions in single string', async () => {
      const inputs = {
        template: '{{trigger.user.name}} ({{trigger.user.id}}) sent: "{{trigger.message}}" at {{workflow.executionId}}',
      };

      const result = await resolver.resolveInputs(inputs, mockContext);
      expect(result.template).toBe('John Doe (12345) sent: "Hello from Facebook" at test-execution-123');
    });
  });
});
