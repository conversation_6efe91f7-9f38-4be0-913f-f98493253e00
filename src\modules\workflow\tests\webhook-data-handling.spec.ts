import { Test, TestingModule } from '@nestjs/testing';
import { ValidationService } from '../services/validation.service';
import {
  WorkflowExecutionJobDto,
  WebhookTriggerDataDto,
  ManualTriggerDataDto,
  ScheduledTriggerDataDto
} from '../dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

describe('Webhook Data Handling Tests', () => {
  let validationService: ValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationService],
    }).compile();

    validationService = module.get<ValidationService>(ValidationService);
  });

  describe('Webhook Trigger Data Validation', () => {
    it('should validate Facebook webhook data', async () => {
      const webhookData = {
        source: 'facebook',
        eventType: 'page.message',
        payload: {
          object: 'page',
          entry: [{
            id: '123456789',
            time: **********,
            messaging: [{
              sender: { id: 'user123' },
              recipient: { id: 'page123' },
              timestamp: **********,
              message: {
                mid: 'msg123',
                text: 'Hello World'
              }
            }]
          }]
        },
        headers: {
          'x-hub-signature-256': 'sha256=signature',
          'content-type': 'application/json'
        },
        timestamp: Date.now(),
        verified: true
      };

      const dto = plainToClass(WebhookTriggerDataDto, webhookData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.source).toBe('facebook');
      expect(dto.eventType).toBe('page.message');
      expect(dto.verified).toBe(true);
    });

    it('should validate Zalo webhook data', async () => {
      const webhookData = {
        source: 'zalo',
        eventType: 'oa.message',
        payload: {
          app_id: '123456789',
          user_id_by_app: 'user123',
          oa_id: 'oa123',
          data: {
            message: 'Hello from Zalo',
            timestamp: '**********'
          }
        },
        headers: {
          'x-zalo-signature': 'signature',
          'content-type': 'application/json'
        },
        timestamp: Date.now(),
        verified: true
      };

      const dto = plainToClass(WebhookTriggerDataDto, webhookData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.source).toBe('zalo');
      expect(dto.eventType).toBe('oa.message');
    });

    it('should validate Google webhook data', async () => {
      const webhookData = {
        source: 'google',
        eventType: 'sheets.update',
        payload: {
          kind: 'api#channel',
          id: 'channel123',
          resourceId: 'resource123',
          resourceUri: 'https://sheets.googleapis.com/v4/spreadsheets/123',
          token: 'token123'
        },
        headers: {
          'x-goog-channel-id': 'channel123',
          'x-goog-resource-state': 'update'
        },
        timestamp: Date.now(),
        verified: true
      };

      const dto = plainToClass(WebhookTriggerDataDto, webhookData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.source).toBe('google');
      expect(dto.eventType).toBe('sheets.update');
    });

    it('should reject invalid webhook source', async () => {
      const webhookData = {
        source: 'invalid_source',
        eventType: 'test.event',
        payload: {},
        headers: {},
        timestamp: Date.now(),
        verified: true
      };

      const dto = plainToClass(WebhookTriggerDataDto, webhookData);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });
  });

  describe('Manual Trigger Data Validation', () => {
    it('should validate manual trigger data', async () => {
      const manualData = {
        inputData: {
          param1: 'value1',
          param2: 123,
          param3: true
        },
        source: 'ui',
        triggeredBy: 1,
        timestamp: Date.now()
      };

      const dto = plainToClass(ManualTriggerDataDto, manualData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.source).toBe('ui');
      expect(dto.triggeredBy).toBe(1);
    });

    it('should reject invalid manual trigger source', async () => {
      const manualData = {
        inputData: {},
        source: 'invalid_source',
        triggeredBy: 1,
        timestamp: Date.now()
      };

      const dto = plainToClass(ManualTriggerDataDto, manualData);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });
  });

  describe('Scheduled Trigger Data Validation', () => {
    it('should validate scheduled trigger data', async () => {
      const scheduledData = {
        scheduleId: 'schedule-123',
        cronExpression: '0 0 * * *',
        scheduledTime: Date.now(),
        executionTime: Date.now() + 1000,
        metadata: {
          timezone: 'UTC',
          description: 'Daily execution'
        }
      };

      const dto = plainToClass(ScheduledTriggerDataDto, scheduledData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.scheduleId).toBe('schedule-123');
      expect(dto.cronExpression).toBe('0 0 * * *');
    });
  });

  describe('Workflow Execution Job Validation', () => {
    it('should validate complete workflow execution job', async () => {
      const jobData = {
        executionId: 'exec-123',
        workflowId: 'workflow-456',
        userId: 1,
        triggerType: 'webhook.facebook',
        triggerData: {
          source: 'facebook',
          eventType: 'page.message',
          payload: { test: 'data' },
          headers: { 'content-type': 'application/json' },
          timestamp: Date.now(),
          verified: true
        },
        options: {
          enableSSE: true,
          timeout: 300000,
          priority: 5,
          enableRetry: true,
          maxRetries: 3,
          retryDelay: 5000
        },
        metadata: {
          source: 'webhook',
          clientIp: '***********',
          userAgent: 'Mozilla/5.0',
          tracking: {
            sessionId: 'session-123'
          }
        }
      };

      const dto = plainToClass(WorkflowExecutionJobDto, jobData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
      expect(dto.executionId).toBe('exec-123');
      expect(dto.workflowId).toBe('workflow-456');
      expect(dto.options?.enableSSE).toBe(true);
      expect(dto.metadata?.clientIp).toBe('***********');
    });

    it('should validate job with minimal required fields', async () => {
      const jobData = {
        executionId: 'exec-123',
        workflowId: 'workflow-456',
        userId: 1,
        triggerType: 'manual',
        triggerData: {
          inputData: {},
          source: 'ui',
          triggeredBy: 1,
          timestamp: Date.now()
        }
      };

      const dto = plainToClass(WorkflowExecutionJobDto, jobData);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should reject job with invalid timeout', async () => {
      const jobData = {
        executionId: 'exec-123',
        workflowId: 'workflow-456',
        userId: 1,
        triggerType: 'manual',
        triggerData: {},
        options: {
          timeout: 500 // Too low, minimum is 1000
        }
      };

      const dto = plainToClass(WorkflowExecutionJobDto, jobData);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });

    it('should reject job with invalid priority', async () => {
      const jobData = {
        executionId: 'exec-123',
        workflowId: 'workflow-456',
        userId: 1,
        triggerType: 'manual',
        triggerData: {},
        options: {
          priority: 15 // Too high, maximum is 10
        }
      };

      const dto = plainToClass(WorkflowExecutionJobDto, jobData);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('Trigger Data Validation Service', () => {
    it('should validate Facebook webhook trigger', async () => {
      const result = await validationService.validateTriggerData(
        'webhook.facebook',
        {
          payload: { test: 'data' },
          headers: { 'x-hub-signature': 'signature' },
          verified: true
        }
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should validate manual trigger', async () => {
      const result = await validationService.validateTriggerData(
        'manual',
        {
          inputData: { param: 'value' },
          triggeredBy: 1
        }
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should validate scheduled trigger', async () => {
      const result = await validationService.validateTriggerData(
        'scheduled',
        {
          scheduleId: 'schedule-123',
          cronExpression: '0 0 * * *'
        }
      );

      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should reject unknown trigger type', async () => {
      const result = await validationService.validateTriggerData(
        'unknown.trigger',
        {}
      );

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Unknown trigger type: unknown.trigger');
    });

    it('should reject webhook without required fields', async () => {
      const result = await validationService.validateTriggerData(
        'webhook.facebook',
        {
          // Missing payload, headers, verified
        }
      );

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Webhook trigger must have payload');
      expect(result.errors).toContain('Webhook trigger must have headers');
      expect(result.errors).toContain('Webhook trigger must have verified status');
    });
  });
});
