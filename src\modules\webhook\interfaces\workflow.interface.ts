/**
 * @file Workflow Module - Workflow Interfaces
 *
 * File này định nghĩa tất cả các interface liên quan đến workflow:
 * - Workflow entities và settings
 * - Node entities trong workflow
 * - Connection entities
 * - API requests/responses cho workflow management
 *
 */

import { ENodeType } from './node-manager.interface';
import { INodeParameters } from './node-manager.interface';

// =================================================================
// SECTION 1: CORE TYPES
// =================================================================

/**
 * Kiểu dữ liệu cho tọa độ của node trên canvas.
 */
export type TNodePosition = {
    x: number;
    y: number;
};

// =================================================================
// SECTION 2: WORKFLOW SETTINGS
// =================================================================

/**
 * Interface cho settings của workflow - Cài đặt chung cho toàn bộ workflow
 *
 * Chứa các cấu hình ảnh hưởng đến cách workflow chạy và xử lý lỗi
 */
export interface IWorkflowSettings {
    /** Loại thông báo khi workflow hoàn thành hoặc lỗi */
    notification_type?: 'email' | 'webhook' | 'none';

    /** Thời gian timeout cho toàn bộ workflow (milliseconds) */
    timeout?: number;

    /** Chính sách retry khi workflow gặp lỗi */
    retry_policy?: {
        /** Có bật retry không */
        enabled: boolean;
        /** Số lần retry tối đa */
        max_retries: number;
        /** Thời gian delay giữa các lần retry (milliseconds) */
        delay: number;
    };

    /** Biến toàn cục có thể sử dụng trong các node */
    variables?: Record<string, any>;

    /** Cho phép thêm các setting tùy chỉnh khác */
    [key: string]: any;
}

// =================================================================
// SECTION 3: DATABASE ENTITIES
// =================================================================

/**
 * Interface cho bảng `workflows` - Thông tin cơ bản của một workflow
 *
 * Workflow là một "quy trình tự động" bao gồm nhiều node được kết nối với nhau
 * để thực hiện một tác vụ phức tạp.
 */
export interface IWorkflowEntity {
    /** ID duy nhất của workflow */
    id: string;

    /** Tên workflow do user đặt */
    name: string;

    /** Workflow có đang hoạt động không (có thể chạy được) */
    is_active: boolean;

    /** Cài đặt chung cho workflow (timeout, retry, notification...) */
    settings?: IWorkflowSettings;

    /** ID của user sở hữu workflow (nếu là personal workflow) */
    user_id?: number;

    /** ID của employee sở hữu workflow (nếu là company workflow) */
    employee_id?: number;

    /** Thời gian tạo workflow */
    created_at: number;

    /** Thời gian cập nhật cuối */
    updated_at: number;
}

/**
 * Interface cho bảng `nodes` - Thực thể node cụ thể trong một workflow
 *
 * Đây là một "instance" được tạo ra từ node definition, chứa dữ liệu cấu hình
 * cụ thể mà user đã nhập cho node này trong workflow này.
 *
 * Ví dụ: Từ "OpenAI Chat" definition, user tạo ra node "Tóm tắt văn bản"
 * với model="gpt-4", temperature=0.7...
 */
export interface INodeEntity {
    /** ID duy nhất của node instance này */
    id: string;

    /** ID của workflow chứa node này */
    workflow_id: string;

    /** Tên do user đặt cho node này (ví dụ: "Tóm tắt văn bản", "Gửi email thông báo") */
    name: string;

    /** Loại node - tham chiếu đến type_name trong node_definitions */
    type: ENodeType;

    /** Phiên bản của node definition được sử dụng */
    type_version: string;

    /** Vị trí của node trên canvas workflow */
    position: TNodePosition;

    /**
     * Dữ liệu cấu hình mà user đã nhập cho node này
     * Key-value pairs tương ứng với các property trong node definition
     * Ví dụ: { "model": "gpt-4", "temperature": 0.7, "prompt": "Hãy tóm tắt..." }
     */
    parameters: INodeParameters;

    /** Node có bị vô hiệu hóa không (skip khi chạy workflow) */
    disabled?: boolean;

    /** Ghi chú của user cho node này */
    notes?: string;

    /** Có retry khi node bị lỗi không */
    retry_on_fail?: boolean;

    /** Số lần retry tối đa */
    max_tries?: number;

    /** Thời gian chờ giữa các lần retry (milliseconds) */
    wait_between_tries?: number;

    /** Hành động khi node lỗi ("stop", "continue", "goto:nodeId") */
    on_error?: string;

    /** ID của integration được sử dụng (nếu node cần kết nối external service) */
    integration_id?: string;

    /** ID của node definition mà node này được tạo ra từ đó */
    node_definition_id: string;
}

/**
 * Interface cho bảng `connections` - Kết nối giữa các node trong workflow
 *
 * Mỗi connection đại diện cho một "dây nối" từ output của node này
 * đến input của node khác, định nghĩa luồng dữ liệu trong workflow.
 */
export interface IConnectionEntity {
    /** ID duy nhất của connection */
    id: number;

    /** ID của workflow chứa connection này */
    workflow_id: string;

    /** ID của node nguồn (node gửi dữ liệu) */
    source_node_id: string;

    /** ID của node đích (node nhận dữ liệu) */
    target_node_id: string;

    /** Tên cổng output của node nguồn (ví dụ: "main", "error") */
    source_handle: string;

    /** Tên cổng input của node đích (ví dụ: "main", "fallback") */
    target_handle: string;
}

// =================================================================
// SECTION 4: API INTERFACES
// =================================================================

/**
 * Request để cập nhật connections trong workflow
 */
export interface IUpdateConnectionsRequest {
    connections: Record<string, Record<string, { node: string; type: string; }[]>>;
}

/**
 * Dữ liệu trả về khi lấy thông tin chi tiết của một node.
 * Kết hợp dữ liệu đã lưu và bản thiết kế giao diện.
 */
export interface ICombinedNodeResponse extends Omit<INodeEntity, 'parameters' | 'workflow_id'> {
    node_definition: {
        id: string;
        type_name: ENodeType;
        version: number;
        display_name: string;
        description?: string;
        group_name: string;
        icon?: string;
        properties: any[];
        inputs?: string[];
        outputs?: string[];
        credentials?: any[];
        created_at: number;
        updated_at: number;
    };
}

/**
 * Dữ liệu trả về khi lấy toàn bộ workflow.
 * BE sẽ tái cấu trúc dữ liệu từ bảng `connections` thành định dạng này.
 */
export interface IWorkflowResponse extends IWorkflowEntity {
    nodes: INodeEntity[];
    connections: Record<string, Record<string, { node: string; type: string; }[]>>;
}
