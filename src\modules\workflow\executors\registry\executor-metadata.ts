import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Metadata interface cho node executors
 */
export interface ExecutorMetadata {
  type: string;
  name: string;
  description: string;
  category: NodeCategory;
  version: string;
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
  requiresAuth?: boolean;
  supportedPlatforms?: string[];
  examples?: ExecutorExample[];
}

/**
 * Example usage cho executor
 */
export interface ExecutorExample {
  name: string;
  description: string;
  inputs: Record<string, any>;
  expectedOutput: Record<string, any>;
  notes?: string;
}

/**
 * Registry entry cho executor
 */
export interface ExecutorRegistryEntry {
  metadata: ExecutorMetadata;
  executorClass: any; // Constructor function
  instance?: any; // Cached instance
  isLoaded: boolean;
  loadedAt?: number;
  errorCount: number;
  lastError?: string;
}

/**
 * Decorator để register node executor
 * @param metadata - Executor metadata
 */
export function NodeExecutor(metadata: Partial<ExecutorMetadata>) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    // Store metadata on the constructor
    Reflect.defineMetadata('executor:metadata', metadata, constructor);
    
    // Store constructor reference for registry
    Reflect.defineMetadata('executor:constructor', constructor, constructor);
    
    return constructor;
  };
}

/**
 * Helper để extract metadata từ executor class
 * @param executorClass - Executor class constructor
 * @returns Executor metadata
 */
export function extractExecutorMetadata(executorClass: any): ExecutorMetadata | null {
  try {
    const metadata = Reflect.getMetadata('executor:metadata', executorClass);
    if (!metadata) {
      return null;
    }

    // Create instance to get runtime metadata
    const instance = new executorClass();
    
    return {
      type: instance.type || metadata.type,
      name: instance.name || metadata.name,
      description: instance.description || metadata.description,
      category: instance.category || metadata.category,
      version: instance.version || metadata.version || '1.0.0',
      inputSchema: instance.getInputSchema?.() || metadata.inputSchema || {},
      outputSchema: instance.getOutputSchema?.() || metadata.outputSchema || {},
      tags: metadata.tags || [],
      deprecated: metadata.deprecated || false,
      experimental: metadata.experimental || false,
      requiresAuth: metadata.requiresAuth || false,
      supportedPlatforms: metadata.supportedPlatforms || ['all'],
      examples: metadata.examples || [],
    };
  } catch (error) {
    console.error('Failed to extract executor metadata:', error);
    return null;
  }
}

/**
 * Validate executor metadata
 * @param metadata - Metadata to validate
 * @returns Validation result
 */
export function validateExecutorMetadata(metadata: ExecutorMetadata): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Required fields
  if (!metadata.type) {
    errors.push('Executor type is required');
  }

  if (!metadata.name) {
    errors.push('Executor name is required');
  }

  if (!metadata.description) {
    errors.push('Executor description is required');
  }

  if (!metadata.category) {
    errors.push('Executor category is required');
  }

  // Type format validation
  if (metadata.type && !/^[a-z]+(\.[a-z]+)*$/.test(metadata.type)) {
    errors.push('Executor type must be in format: category.subcategory.action');
  }

  // Version format validation
  if (metadata.version && !/^\d+\.\d+\.\d+$/.test(metadata.version)) {
    errors.push('Version must be in semver format (e.g., 1.0.0)');
  }

  // Schema validation
  if (metadata.inputSchema && typeof metadata.inputSchema !== 'object') {
    errors.push('Input schema must be an object');
  }

  if (metadata.outputSchema && typeof metadata.outputSchema !== 'object') {
    errors.push('Output schema must be an object');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Create default metadata cho executor
 * @param type - Executor type
 * @param category - Executor category
 * @returns Default metadata
 */
export function createDefaultMetadata(
  type: string,
  category: NodeCategory
): Partial<ExecutorMetadata> {
  const nameParts = type.split('.');
  const actionName = nameParts[nameParts.length - 1];
  
  return {
    type,
    name: actionName.charAt(0).toUpperCase() + actionName.slice(1),
    description: `${actionName} operation for ${category}`,
    category,
    version: '1.0.0',
    inputSchema: {
      type: 'object',
      properties: {},
      required: [],
    },
    outputSchema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: { type: 'object' },
      },
    },
    tags: [category, actionName],
    deprecated: false,
    experimental: false,
    requiresAuth: category !== NodeCategory.SYSTEM,
    supportedPlatforms: ['all'],
    examples: [],
  };
}

/**
 * Compare executor versions
 * @param version1 - First version
 * @param version2 - Second version
 * @returns -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
export function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    
    if (v1Part < v2Part) return -1;
    if (v1Part > v2Part) return 1;
  }
  
  return 0;
}

/**
 * Check if executor is compatible với platform
 * @param metadata - Executor metadata
 * @param platform - Target platform
 * @returns true if compatible
 */
export function isCompatibleWithPlatform(
  metadata: ExecutorMetadata,
  platform: string
): boolean {
  if (!metadata.supportedPlatforms || metadata.supportedPlatforms.includes('all')) {
    return true;
  }
  
  return metadata.supportedPlatforms.includes(platform);
}
