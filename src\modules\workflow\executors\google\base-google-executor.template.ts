import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../base/base-authenticated-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';
import { IntegrationService } from '../../../../shared/services/integration.service';
import { ProviderEnum } from '../../enums/provider.enum';

/**
 * Template chuẩn cho Google Executors
 * Sao chép template này và thay thế các placeholder
 *
 * HƯỚNG DẪN SỬ DỤNG:
 * 1. Copy template này
 * 2. Thay thế GoogleExecutorTemplate bằng tên class thực tế
 * 3. Thay thế GoogleExecutorInput/Output bằng interfaces thực tế
 * 4. Cập nhật type, category, name, description
 * 5. Implement getInputSchema(), getOutputSchema()
 * 6. Implement validateInputs() và executeNode()
 */

// PLACEHOLDER: Định nghĩa Input Interface
export interface GoogleExecutorInput {
  // TODO: Định nghĩa input fields
  // Ví dụ:
  // customerId?: string;
  // refreshToken?: string;
}

// PLACEHOLDER: Định nghĩa Output Interface
export interface GoogleExecutorOutput {
  // TODO: Định nghĩa output fields
  // Ví dụ:
  // success: boolean;
  // data: any;
  // metadata?: any;
}

/**
 * PLACEHOLDER: Mô tả executor
 */
@Injectable()
export class GoogleExecutorTemplate extends BaseAuthenticatedExecutor {
  // REQUIRED: Định nghĩa type duy nhất
  readonly type = 'google.service.action';

  // REQUIRED: Định nghĩa category
  readonly category = NodeCategory.GOOGLE_ADS; // hoặc GOOGLE_CALENDAR, GOOGLE_DRIVE, etc.

  // REQUIRED: Tên hiển thị
  readonly name = 'Action Name';

  // REQUIRED: Mô tả
  readonly description = 'Mô tả chức năng của executor';

  constructor(
    integrationService: IntegrationService,
    // PLACEHOLDER: Inject service cần thiết
    // private readonly serviceInstance: ServiceClass,
  ) {
    super(integrationService);
  }

  /**
   * REQUIRED: Định nghĩa input schema cho validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        // TODO: Định nghĩa input schema
        // Ví dụ:
        // customerId: { type: 'string' },
        // refreshToken: { type: 'string' },
      },
      required: [
        // TODO: Danh sách required fields
        // 'customerId'
      ],
    };
  }

  /**
   * REQUIRED: Định nghĩa output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        // TODO: Định nghĩa output schema
        // Ví dụ:
        // success: { type: 'boolean' },
        // data: { type: 'object' },
        // metadata: { type: 'object' },
      },
    };
  }

  /**
   * REQUIRED: Validation logic (trả về boolean)
   */
  validateInputs(inputs: GoogleExecutorInput): boolean {
    try {
      // TODO: Implement validation logic
      if (!inputs) {
        throw new Error('Inputs are required');
      }

      // Ví dụ validation:
      // if (!inputs.customerId) {
      //   throw new Error('Customer ID is required');
      // }

      return true;
    } catch (error) {
      this.logger.error(`Input validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * REQUIRED: Main execution logic
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<GoogleExecutorOutput> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      this.logger.log(`Executing ${this.name} with inputs:`, inputs);

      // Validate inputs
      if (!this.validateInputs(inputs)) {
        throw new Error('Invalid inputs provided');
      }

      // Get access token
      const accessToken = await this.getGoogleAccessToken(context);

      try {
        // TODO: Implement main logic here

        // PLACEHOLDER: Call service method
        // const result = await this.serviceInstance.methodName(params, accessToken);

        // TODO: Process and return result
        const output: GoogleExecutorOutput = {
          // TODO: Map result to output format
          // success: true,
          // data: result,
          // metadata: {
          //   executedAt: new Date().toISOString(),
          //   nodeType: this.type,
          // }
        };

        this.logger.log(`${this.name} completed successfully`);
        return output;

      } catch (error) {
        this.logger.error(`${this.name} execution failed:`, error);
        throw new Error(`Failed to execute ${this.name}: ${error.message}`);
      }
    });
  }
}
