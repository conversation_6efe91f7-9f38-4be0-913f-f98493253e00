import { Injectable, Logger } from '@nestjs/common';
import { ExecutionContext, NodeExecutionResult } from '../../interfaces/execution-context.interface';
import { NodeExecutorFactory } from '../registry/node-executor-factory';
import { LoggingService } from '../../services/logging.service';
import { EventService } from '../../services/event.service';

/**
 * Main dispatcher để execute workflow nodes
 * Orchestrates node execution với proper error handling và logging
 */
@Injectable()
export class WorkflowExecutorDispatcher {
  private readonly logger = new Logger(WorkflowExecutorDispatcher.name);

  constructor(
    private readonly executorFactory: NodeExecutorFactory,
    private readonly loggingService: LoggingService,
    private readonly eventService: EventService,
  ) {}

  /**
   * Execute một node trong workflow
   * @param nodeType - Type của node cần execute
   * @param inputs - Input data cho node
   * @param context - Execution context
   * @returns Node execution result
   */
  async executeNode(
    nodeType: string,
    inputs: any,
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Validate node type
      const validation = this.executorFactory.validateNodeType(nodeType);
      if (!validation.valid) {
        throw new Error(`Invalid node type ${nodeType}: ${validation.errors.join(', ')}`);
      }

      // Get executor instance
      const executor = await this.executorFactory.createExecutor(nodeType, context);
      if (!executor) {
        throw new Error(`Failed to create executor for node type: ${nodeType}`);
      }

      // Log execution start
      await this.logExecutionStart(nodeType, inputs, context);

      // Execute node
      const result = await executor.execute(inputs, context);

      // Log execution result
      await this.logExecutionResult(nodeType, result, Date.now() - startTime, context);

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Log execution error
      await this.logExecutionError(nodeType, error, executionTime, context);

      return {
        success: false,
        error: error.message,
        errorDetails: {
          stack: error.stack,
          nodeType,
          inputs,
        },
        metadata: {
          executionTime,
          nodeType,
          timestamp: Date.now(),
        },
      };
    }
  }

  /**
   * Execute multiple nodes in parallel
   * @param nodeExecutions - Array of node execution requests
   * @param context - Execution context
   * @returns Array of execution results
   */
  async executeNodesParallel(
    nodeExecutions: Array<{ nodeType: string; inputs: any; nodeId: string }>,
    context: ExecutionContext
  ): Promise<Array<{ nodeId: string; result: NodeExecutionResult }>> {
    this.logger.log(`Executing ${nodeExecutions.length} nodes in parallel`);

    const executionPromises = nodeExecutions.map(async ({ nodeType, inputs, nodeId }) => {
      const result = await this.executeNode(nodeType, inputs, context);
      return { nodeId, result };
    });

    const results = await Promise.all(executionPromises);
    
    this.logger.log(`Parallel execution completed: ${results.length} results`);
    return results;
  }

  /**
   * Execute nodes in sequence
   * @param nodeExecutions - Array of node execution requests
   * @param context - Execution context
   * @returns Array of execution results
   */
  async executeNodesSequential(
    nodeExecutions: Array<{ nodeType: string; inputs: any; nodeId: string }>,
    context: ExecutionContext
  ): Promise<Array<{ nodeId: string; result: NodeExecutionResult }>> {
    this.logger.log(`Executing ${nodeExecutions.length} nodes sequentially`);

    const results: Array<{ nodeId: string; result: NodeExecutionResult }> = [];

    for (const { nodeType, inputs, nodeId } of nodeExecutions) {
      const result = await this.executeNode(nodeType, inputs, context);
      results.push({ nodeId, result });

      // Stop execution if node failed and no error handling
      if (!result.success) {
        this.logger.warn(`Node ${nodeId} failed, stopping sequential execution`);
        break;
      }
    }

    this.logger.log(`Sequential execution completed: ${results.length} results`);
    return results;
  }

  /**
   * Test node execution với mock data
   * @param nodeType - Type của node
   * @param testInputs - Test input data
   * @param mockContext - Mock execution context
   * @returns Test execution result
   */
  async testNodeExecution(
    nodeType: string,
    testInputs: any,
    mockContext?: Partial<ExecutionContext>
  ): Promise<NodeExecutionResult> {
    // Create mock context
    const context: ExecutionContext = {
      executionId: 'test-execution',
      workflowId: 'test-workflow',
      userId: 1,
      triggerType: 'manual',
      triggerData: {},
      getNodeOutput: (nodeId: string) => undefined,
      setNodeOutput: (nodeId: string, output: any) => {},
      getNodeInput: (nodeId: string) => undefined,
      isNodeExecuted: (nodeId: string) => false,
      getExecutedNodes: () => [],
      logService: this.loggingService,
      loggingService: this.loggingService,
      eventService: this.eventService,
      startTime: Date.now(),
      variables: {},
      nodeResults: {},
      ...mockContext,
    };

    this.logger.log(`Testing node execution: ${nodeType}`);
    
    const result = await this.executeNode(nodeType, testInputs, context);
    
    this.logger.log(`Test execution completed for ${nodeType}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
    return result;
  }

  /**
   * Get available node types cho execution
   * @returns Array of available node types
   */
  getAvailableNodeTypes(): string[] {
    return this.executorFactory.getAvailableNodeTypes();
  }

  /**
   * Check if node type can be executed
   * @param nodeType - Node type to check
   * @returns true if can be executed
   */
  canExecuteNodeType(nodeType: string): boolean {
    return this.executorFactory.canCreateExecutor(nodeType);
  }

  /**
   * Get dispatcher statistics
   * @returns Dispatcher statistics
   */
  getStatistics(): any {
    return {
      factoryStats: this.executorFactory.getStatistics(),
      availableNodeTypes: this.getAvailableNodeTypes().length,
    };
  }

  /**
   * Health check cho dispatcher
   * @returns Health status
   */
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = [];
    
    // Check factory health
    const factoryHealth = this.executorFactory.healthCheck();
    if (!factoryHealth.healthy) {
      issues.push(...factoryHealth.issues);
    }

    // Check available node types
    const availableTypes = this.getAvailableNodeTypes();
    if (availableTypes.length === 0) {
      issues.push('No node types available for execution');
    }

    return {
      healthy: issues.length === 0,
      issues,
    };
  }

  /**
   * Log execution start
   */
  private async logExecutionStart(
    nodeType: string,
    inputs: any,
    context: ExecutionContext
  ): Promise<void> {
    try {
      await this.loggingService.logNodeEvent(
        context.executionId,
        nodeType,
        'node.execution.start',
        { inputs, timestamp: Date.now() }
      );

      await this.eventService.emitNodeEvent(
        context.executionId,
        nodeType,
        'node.execution.start',
        { inputs, timestamp: Date.now() }
      );
    } catch (error) {
      this.logger.warn('Failed to log execution start:', error);
    }
  }

  /**
   * Log execution result
   */
  private async logExecutionResult(
    nodeType: string,
    result: NodeExecutionResult,
    executionTime: number,
    context: ExecutionContext
  ): Promise<void> {
    try {
      const eventType = result.success ? 'node.execution.success' : 'node.execution.error';
      const logData = {
        result,
        executionTime,
        timestamp: Date.now(),
      };

      await this.loggingService.logNodeEvent(
        context.executionId,
        nodeType,
        eventType,
        logData
      );

      await this.eventService.emitNodeEvent(
        context.executionId,
        nodeType,
        eventType,
        logData
      );
    } catch (error) {
      this.logger.warn('Failed to log execution result:', error);
    }
  }

  /**
   * Log execution error
   */
  private async logExecutionError(
    nodeType: string,
    error: Error,
    executionTime: number,
    context: ExecutionContext
  ): Promise<void> {
    try {
      const logData = {
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        executionTime,
        timestamp: Date.now(),
      };

      await this.loggingService.logNodeEvent(
        context.executionId,
        nodeType,
        'node.execution.error',
        logData
      );

      await this.eventService.emitNodeEvent(
        context.executionId,
        nodeType,
        'node.execution.error',
        logData
      );
    } catch (logError) {
      this.logger.warn('Failed to log execution error:', logError);
    }
  }
}
