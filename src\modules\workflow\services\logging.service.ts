import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkflowExecutionLog } from '../entities/workflow-execution-log.entity';

/**
 * Service để ghi log execution cho workflow
 * Hỗ trợ real-time logging và audit trail
 */
@Injectable()
export class LoggingService {
  constructor(
    @InjectRepository(WorkflowExecutionLog)
    private readonly logRepository: Repository<WorkflowExecutionLog>,
  ) {}

  /**
   * Ghi log khi node bắt đầu thực thi
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param payload - Input data của node
   */
  async logNodeStart(
    executionId: string,
    nodeId: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, nodeId, 'node.start', payload);
  }

  /**
   * Ghi log khi node thực thi thành công
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param payload - Output data của node
   */
  async logNodeSuccess(
    executionId: string,
    nodeId: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, nodeId, 'node.success', payload);
  }

  /**
   * Ghi log khi node thực thi failed
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param error - Error information
   */

  /**
   * Ghi log cho node event (generic method)
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param eventType - Loại event
   * @param payload - Data kèm theo
   */
  async logNodeEvent(
    executionId: string,
    nodeId: string,
    eventType: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, nodeId, eventType, payload);
  }
  async logNodeError(
    executionId: string,
    nodeId: string,
    error: any
  ): Promise<void> {
    await this.createLog(executionId, nodeId, 'node.error', {
      error: error.message || error,
      stack: error.stack,
      details: error.details || error
    });
  }

  /**
   * Ghi log khi node bị skip
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param reason - Lý do skip
   */
  async logNodeSkip(
    executionId: string,
    nodeId: string,
    reason: string
  ): Promise<void> {
    await this.createLog(executionId, nodeId, 'node.skip', { reason });
  }

  /**
   * Ghi log khi workflow bắt đầu
   * @param executionId - ID của execution
   * @param payload - Trigger data
   */
  async logWorkflowStart(
    executionId: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, 'workflow', 'workflow.start', payload);
  }

  /**
   * Ghi log khi workflow hoàn thành
   * @param executionId - ID của execution
   * @param payload - Result data
   */
  async logWorkflowComplete(
    executionId: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, 'workflow', 'workflow.complete', payload);
  }

  /**
   * Ghi log khi workflow failed
   * @param executionId - ID của execution
   * @param error - Error information
   */
  async logWorkflowError(
    executionId: string,
    error: any
  ): Promise<void> {
    await this.createLog(executionId, 'workflow', 'workflow.error', {
      error: error.message || error,
      stack: error.stack,
      details: error.details || error
    });
  }

  /**
   * Ghi custom log
   * @param executionId - ID của execution
   * @param nodeId - ID của node
   * @param eventType - Loại event
   * @param payload - Data payload
   */
  async logCustomEvent(
    executionId: string,
    nodeId: string,
    eventType: string,
    payload?: any
  ): Promise<void> {
    await this.createLog(executionId, nodeId, eventType, payload);
  }

  /**
   * Lấy logs của một execution
   * @param executionId - ID của execution
   * @param nodeId - ID của node (optional)
   * @returns Array of logs
   */
  async getExecutionLogs(
    executionId: string,
    nodeId?: string
  ): Promise<WorkflowExecutionLog[]> {
    const query = this.logRepository
      .createQueryBuilder('log')
      .where('log.workflowExecutionId = :executionId', { executionId })
      .orderBy('log.timestamp', 'ASC');

    if (nodeId) {
      query.andWhere('log.nodeId = :nodeId', { nodeId });
    }

    return query.getMany();
  }

  /**
   * Lấy logs theo event type
   * @param executionId - ID của execution
   * @param eventType - Loại event
   * @returns Array of logs
   */
  async getLogsByEventType(
    executionId: string,
    eventType: string
  ): Promise<WorkflowExecutionLog[]> {
    return this.logRepository.find({
      where: {
        workflowExecutionId: executionId,
        eventType
      },
      order: {
        timestamp: 'ASC'
      }
    });
  }

  /**
   * Ghi log thông tin chung
   * @param executionId - ID của execution
   * @param message - Thông điệp log
   * @param data - Dữ liệu bổ sung
   */
  async logInfo(
    executionId: string,
    message: string,
    data?: any
  ): Promise<void> {
    await this.createLog(executionId, 'system', 'info', {
      message,
      data,
      level: 'info'
    });
  }

  /**
   * Ghi log cảnh báo
   * @param executionId - ID của execution
   * @param message - Thông điệp log
   * @param data - Dữ liệu bổ sung
   */
  async logWarn(
    executionId: string,
    message: string,
    data?: any
  ): Promise<void> {
    await this.createLog(executionId, 'system', 'warn', {
      message,
      data,
      level: 'warn'
    });
  }

  /**
   * Ghi log lỗi
   * @param executionId - ID của execution
   * @param message - Thông điệp lỗi
   * @param data - Dữ liệu lỗi
   */
  async logError(
    executionId: string,
    message: string,
    data?: any
  ): Promise<void> {
    await this.createLog(executionId, 'system', 'error', {
      message,
      data,
      level: 'error'
    });
  }

  /**
   * Xóa logs cũ (cleanup)
   * @param olderThanDays - Xóa logs cũ hơn số ngày này
   */
  async cleanupOldLogs(olderThanDays: number = 30): Promise<number> {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

    const result = await this.logRepository
      .createQueryBuilder()
      .delete()
      .where('timestamp < :cutoffTime', { cutoffTime })
      .execute();

    return result.affected || 0;
  }

  /**
   * Tạo log entry
   * @private
   */
  private async createLog(
    executionId: string,
    nodeId: string,
    eventType: string,
    payload?: any
  ): Promise<WorkflowExecutionLog> {
    const log = this.logRepository.create({
      workflowExecutionId: executionId,
      nodeId,
      eventType,
      payload,
      timestamp: Date.now()
    });

    return this.logRepository.save(log);
  }
}
