import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  Workflow,
  WorkflowNode,
  WorkflowEdge,
  NodeDefinition,
  WorkflowExecution,
  WorkflowExecutionLog
} from './entities';
import {
  LoggingService,
  EventService,
  ValidationService,
  SharedValidationService,
  BaseExecutorService,
  WorkflowExecutorService,
  // Node Test services removed - focusing on real execution only
} from './services';
import { WorkflowExecutionProcessor } from './processors/workflow-execution.processor';
// Node Test processor removed - focusing on real execution only
import { RedisService } from '../../infra/redis/redis.service';
import {
  NodeExecutorRegistry,
  NodeExecutorFactory,
  SystemExecutorRegistryService,
} from './executors/registry';
import {
  HttpRequestExecutor,
  ConditionExecutor,
  DelayExecutor,
  DataTransformExecutor,
  LoopExecutor,
  OutputExecutor,
  ManualTriggerExecutor,
  VariableExecutor,
  ErrorHandlerExecutor,
} from './executors/system';
// LangGraph services temporarily commented out due to type errors
// import {
//   WorkflowDefinitionParserService,
// } from './langgraph';
import {
  ServiceMigrationUtility,
  MigrationCommand,
} from './migration';

/**
 * Workflow module for BE Worker
 * Handles workflow execution, node processing, and shared entities
 * Synced with BE App for consistent data structures
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      WorkflowNode,
      WorkflowEdge,
      NodeDefinition,
      WorkflowExecution,
      WorkflowExecutionLog
    ])
  ],
  providers: [
    // Existing services
    LoggingService,
    EventService,
    ValidationService,
    SharedValidationService,

    // Queue consumer services - real execution only
    BaseExecutorService,
    WorkflowExecutorService,
    RedisService,

    // Queue processors - real execution only
    WorkflowExecutionProcessor,

    // Executor registry
    NodeExecutorRegistry,
    NodeExecutorFactory,
    SystemExecutorRegistryService,

    // System executors
    HttpRequestExecutor,
    ConditionExecutor,
    DelayExecutor,
    DataTransformExecutor,
    LoopExecutor,
    OutputExecutor,
    ManualTriggerExecutor,
    VariableExecutor,
    ErrorHandlerExecutor,

    // LangGraph integration - temporarily commented out due to type errors
    // WorkflowDefinitionParserService,

    // Migration utilities
    ServiceMigrationUtility,
    MigrationCommand,
  ],
  exports: [
    TypeOrmModule,
    LoggingService,
    EventService,
    ValidationService,
    SharedValidationService,
    BaseExecutorService,
    WorkflowExecutorService,
    NodeExecutorRegistry,
    NodeExecutorFactory,
    SystemExecutorRegistryService,
    // WorkflowDefinitionParserService,
    ServiceMigrationUtility,
    MigrationCommand,
  ]
})
export class WorkflowModule {}
