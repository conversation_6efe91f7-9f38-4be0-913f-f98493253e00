import { Injectable } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { NodeCategory } from '../../entities/node-definition.entity';

/**
 * Variable Node Executor
 * Handles variable operations including get, set, and manipulation
 */
@Injectable()
export class VariableExecutor extends BaseNodeExecutor {
  readonly type = 'system.variable';
  readonly category = NodeCategory.SYSTEM;
  readonly name = 'Variable';
  readonly description = 'Manage workflow variables with get, set, and manipulation operations';

  /**
   * Execute variable operation
   * @param inputs - Variable operation configuration
   * @param context - Execution context
   * @returns Variable operation result
   */
  protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    const {
      operation = 'get',
      variableName,
      value,
      defaultValue,
      scope = 'execution',
      dataType,
      transform,
      validation,
    } = inputs;

    this.logger.debug(`Executing variable operation: ${operation} on ${variableName}`);

    try {
      let result: any;

      switch (operation) {
        case 'get':
          result = await this.getVariable(variableName, defaultValue, scope, context);
          break;
        case 'set':
          result = await this.setVariable(variableName, value, scope, dataType, context);
          break;
        case 'delete':
          result = await this.deleteVariable(variableName, scope, context);
          break;
        case 'exists':
          result = await this.checkVariableExists(variableName, scope, context);
          break;
        case 'list':
          result = await this.listVariables(scope, context);
          break;
        case 'transform':
          result = await this.transformVariable(variableName, transform, scope, context);
          break;
        case 'validate':
          result = await this.validateVariable(variableName, validation, scope, context);
          break;
        default:
          throw new Error(`Unsupported variable operation: ${operation}`);
      }

      // Apply post-processing transformations if specified
      if (transform && operation === 'get') {
        result = await this.applyTransformation(result, transform);
      }

      return {
        operation,
        variableName,
        value: result,
        scope,
        dataType: this.getDataType(result),
        processedAt: new Date().toISOString(),
        metadata: {
          operationType: operation,
          scope,
          hasValue: result !== undefined && result !== null,
          valueSize: this.calculateValueSize(result),
        },
      };

    } catch (error) {
      this.logger.error(`Variable operation failed: ${error.message}`);
      throw new Error(`Variable operation failed: ${error.message}`);
    }
  }

  /**
   * Get variable value
   * @private
   */
  private async getVariable(
    name: string,
    defaultValue: any,
    scope: string,
    context: ExecutionContext
  ): Promise<any> {
    if (!name) {
      throw new Error('Variable name is required for get operation');
    }

    let value: any;

    switch (scope) {
      case 'execution':
        value = context.variables?.[name];
        break;
      case 'workflow':
        value = context.variables?.[`workflow.${name}`];
        break;
      case 'global':
        value = await this.getGlobalVariable(name, context);
        break;
      case 'user':
        value = await this.getUserVariable(name, context);
        break;
      case 'node':
        value = context.nodeResults?.[name];
        break;
      default:
        throw new Error(`Unsupported variable scope: ${scope}`);
    }

    return value !== undefined ? value : defaultValue;
  }

  /**
   * Set variable value
   * @private
   */
  private async setVariable(
    name: string,
    value: any,
    scope: string,
    dataType: string,
    context: ExecutionContext
  ): Promise<any> {
    if (!name) {
      throw new Error('Variable name is required for set operation');
    }

    // Convert value to specified data type
    const convertedValue = dataType ? this.convertDataType(value, dataType) : value;

    switch (scope) {
      case 'execution':
        if (!context.variables) {
          context.variables = {};
        }
        context.variables[name] = convertedValue;
        break;
      case 'workflow':
        if (!context.variables) {
          context.variables = {};
        }
        context.variables[`workflow.${name}`] = convertedValue;
        break;
      case 'global':
        await this.setGlobalVariable(name, convertedValue, context);
        break;
      case 'user':
        await this.setUserVariable(name, convertedValue, context);
        break;
      case 'node':
        if (!context.nodeResults) {
          context.nodeResults = {};
        }
        context.nodeResults[name] = convertedValue;
        break;
      default:
        throw new Error(`Unsupported variable scope: ${scope}`);
    }

    // Emit variable set event
    await this.emitEvent('variable.set', {
      name,
      scope,
      dataType: this.getDataType(convertedValue),
      executionId: context.executionId,
    }, context);

    return convertedValue;
  }

  /**
   * Delete variable
   * @private
   */
  private async deleteVariable(name: string, scope: string, context: ExecutionContext): Promise<boolean> {
    if (!name) {
      throw new Error('Variable name is required for delete operation');
    }

    let deleted = false;

    switch (scope) {
      case 'execution':
        if (context.variables && context.variables[name] !== undefined) {
          delete context.variables[name];
          deleted = true;
        }
        break;
      case 'workflow':
        if (context.variables && context.variables[`workflow.${name}`] !== undefined) {
          delete context.variables[`workflow.${name}`];
          deleted = true;
        }
        break;
      case 'global':
        deleted = await this.deleteGlobalVariable(name, context);
        break;
      case 'user':
        deleted = await this.deleteUserVariable(name, context);
        break;
      case 'node':
        if (context.nodeResults && context.nodeResults[name] !== undefined) {
          delete context.nodeResults[name];
          deleted = true;
        }
        break;
      default:
        throw new Error(`Unsupported variable scope: ${scope}`);
    }

    if (deleted) {
      await this.emitEvent('variable.deleted', {
        name,
        scope,
        executionId: context.executionId,
      }, context);
    }

    return deleted;
  }

  /**
   * Check if variable exists
   * @private
   */
  private async checkVariableExists(name: string, scope: string, context: ExecutionContext): Promise<boolean> {
    if (!name) {
      throw new Error('Variable name is required for exists operation');
    }

    switch (scope) {
      case 'execution':
        return context.variables?.[name] !== undefined;
      case 'workflow':
        return context.variables?.[`workflow.${name}`] !== undefined;
      case 'global':
        return await this.globalVariableExists(name, context);
      case 'user':
        return await this.userVariableExists(name, context);
      case 'node':
        return context.nodeResults?.[name] !== undefined;
      default:
        throw new Error(`Unsupported variable scope: ${scope}`);
    }
  }

  /**
   * List variables in scope
   * @private
   */
  private async listVariables(scope: string, context: ExecutionContext): Promise<Record<string, any>> {
    switch (scope) {
      case 'execution':
        return context.variables ? { ...context.variables } : {};
      case 'workflow':
        const workflowVars: Record<string, any> = {};
        if (context.variables) {
          Object.entries(context.variables).forEach(([key, value]) => {
            if (key.startsWith('workflow.')) {
              workflowVars[key.substring(9)] = value;
            }
          });
        }
        return workflowVars;
      case 'global':
        return await this.listGlobalVariables(context);
      case 'user':
        return await this.listUserVariables(context);
      case 'node':
        return context.nodeResults ? { ...context.nodeResults } : {};
      default:
        throw new Error(`Unsupported variable scope: ${scope}`);
    }
  }

  /**
   * Transform variable value
   * @private
   */
  private async transformVariable(
    name: string,
    transform: any,
    scope: string,
    context: ExecutionContext
  ): Promise<any> {
    const currentValue = await this.getVariable(name, undefined, scope, context);
    
    if (currentValue === undefined) {
      throw new Error(`Variable '${name}' not found in scope '${scope}'`);
    }

    const transformedValue = await this.applyTransformation(currentValue, transform);
    await this.setVariable(name, transformedValue, scope, '', context);

    return transformedValue;
  }

  /**
   * Validate variable value
   * @private
   */
  private async validateVariable(
    name: string,
    validation: any,
    scope: string,
    context: ExecutionContext
  ): Promise<{ valid: boolean; errors: string[] }> {
    const value = await this.getVariable(name, undefined, scope, context);
    
    if (value === undefined) {
      return { valid: false, errors: [`Variable '${name}' not found in scope '${scope}'`] };
    }

    return this.validateValue(value, validation);
  }

  /**
   * Apply transformation to value
   * @private
   */
  private async applyTransformation(value: any, transform: any): Promise<any> {
    const { type, config = {} } = transform;

    switch (type) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'trim':
        return String(value).trim();
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      case 'string':
        return String(value);
      case 'json_parse':
        return JSON.parse(String(value));
      case 'json_stringify':
        return JSON.stringify(value);
      case 'array_join':
        return Array.isArray(value) ? value.join(config.separator || ',') : value;
      case 'array_split':
        return String(value).split(config.separator || ',');
      case 'date_format':
        return new Date(value).toISOString();
      case 'custom':
        return this.applyCustomTransformation(value, config);
      default:
        throw new Error(`Unsupported transformation type: ${type}`);
    }
  }

  /**
   * Apply custom transformation
   * @private
   */
  private applyCustomTransformation(value: any, config: any): any {
    const { script } = config;

    if (!script) {
      return value;
    }

    try {
      const func = new Function('value', script);
      return func(value);
    } catch (error) {
      throw new Error(`Custom transformation failed: ${error.message}`);
    }
  }

  /**
   * Validate value against validation rules
   * @private
   */
  private validateValue(value: any, validation: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { type, required, min, max, pattern, custom } = validation;

    // Required check
    if (required && (value === undefined || value === null || value === '')) {
      errors.push('Value is required');
    }

    // Type check
    if (type && typeof value !== type) {
      errors.push(`Expected type ${type}, got ${typeof value}`);
    }

    // Min/max checks
    if (typeof value === 'number') {
      if (min !== undefined && value < min) {
        errors.push(`Value ${value} is less than minimum ${min}`);
      }
      if (max !== undefined && value > max) {
        errors.push(`Value ${value} is greater than maximum ${max}`);
      }
    }

    // Pattern check for strings
    if (typeof value === 'string' && pattern) {
      const regex = new RegExp(pattern);
      if (!regex.test(value)) {
        errors.push(`Value does not match pattern ${pattern}`);
      }
    }

    // Custom validation
    if (custom && custom.script) {
      try {
        const func = new Function('value', custom.script);
        const result = func(value);
        if (!result) {
          errors.push(custom.message || 'Custom validation failed');
        }
      } catch (error) {
        errors.push(`Custom validation error: ${error.message}`);
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Convert value to specified data type
   * @private
   */
  private convertDataType(value: any, dataType: string): any {
    switch (dataType) {
      case 'string':
        return String(value);
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      case 'object':
        return typeof value === 'object' ? value : JSON.parse(String(value));
      case 'array':
        return Array.isArray(value) ? value : [value];
      default:
        return value;
    }
  }

  /**
   * Get data type of value
   * @private
   */
  private getDataType(value: any): string {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
  }

  /**
   * Calculate value size
   * @private
   */
  private calculateValueSize(value: any): number {
    try {
      return JSON.stringify(value).length;
    } catch {
      return 0;
    }
  }

  // Placeholder methods for global/user variable operations
  // These would be implemented with actual storage mechanisms

  private async getGlobalVariable(name: string, context: ExecutionContext): Promise<any> {
    // Implementation would fetch from global storage (Redis, database, etc.)
    return undefined;
  }

  private async setGlobalVariable(name: string, value: any, context: ExecutionContext): Promise<void> {
    // Implementation would store in global storage
  }

  private async deleteGlobalVariable(name: string, context: ExecutionContext): Promise<boolean> {
    // Implementation would delete from global storage
    return false;
  }

  private async globalVariableExists(name: string, context: ExecutionContext): Promise<boolean> {
    // Implementation would check global storage
    return false;
  }

  private async listGlobalVariables(context: ExecutionContext): Promise<Record<string, any>> {
    // Implementation would list from global storage
    return {};
  }

  private async getUserVariable(name: string, context: ExecutionContext): Promise<any> {
    // Implementation would fetch user-specific variable
    return undefined;
  }

  private async setUserVariable(name: string, value: any, context: ExecutionContext): Promise<void> {
    // Implementation would store user-specific variable
  }

  private async deleteUserVariable(name: string, context: ExecutionContext): Promise<boolean> {
    // Implementation would delete user-specific variable
    return false;
  }

  private async userVariableExists(name: string, context: ExecutionContext): Promise<boolean> {
    // Implementation would check user-specific variable
    return false;
  }

  private async listUserVariables(context: ExecutionContext): Promise<Record<string, any>> {
    // Implementation would list user-specific variables
    return {};
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      required: ['operation'],
      properties: {
        operation: {
          type: 'string',
          enum: ['get', 'set', 'delete', 'exists', 'list', 'transform', 'validate'],
          description: 'Variable operation to perform',
        },
        variableName: {
          type: 'string',
          description: 'Name of the variable (required for most operations)',
        },
        value: {
          description: 'Value to set (required for set operation)',
        },
        defaultValue: {
          description: 'Default value to return if variable not found (get operation)',
        },
        scope: {
          type: 'string',
          enum: ['execution', 'workflow', 'global', 'user', 'node'],
          default: 'execution',
          description: 'Variable scope',
        },
        dataType: {
          type: 'string',
          enum: ['string', 'number', 'boolean', 'object', 'array'],
          description: 'Data type to convert value to (set operation)',
        },
        transform: {
          type: 'object',
          description: 'Transformation to apply to variable value',
        },
        validation: {
          type: 'object',
          description: 'Validation rules for variable value',
        },
      },
    };
  }

  /**
   * Get output schema for documentation
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          description: 'Operation that was performed',
        },
        variableName: {
          type: 'string',
          description: 'Name of the variable',
        },
        value: {
          description: 'Variable value or operation result',
        },
        scope: {
          type: 'string',
          description: 'Variable scope',
        },
        dataType: {
          type: 'string',
          description: 'Data type of the value',
        },
        processedAt: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when operation was processed',
        },
        metadata: {
          type: 'object',
          properties: {
            operationType: {
              type: 'string',
              description: 'Type of operation performed',
            },
            scope: {
              type: 'string',
              description: 'Variable scope used',
            },
            hasValue: {
              type: 'boolean',
              description: 'Whether the variable has a value',
            },
            valueSize: {
              type: 'number',
              description: 'Size of the value in bytes',
            },
          },
        },
      },
    };
  }
}
