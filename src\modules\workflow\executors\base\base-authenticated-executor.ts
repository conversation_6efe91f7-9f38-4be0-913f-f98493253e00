import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from './base-node-executor';
import { ExecutionContext } from '../../interfaces/execution-context.interface';
import { IntegrationService } from '../../../../shared/services/integration.service';
import { ProviderEnum } from '../../enums/provider.enum';
import { PayloadEncryption } from '../../interfaces/payload_encryption.interface';

/**
 * Base class cho các executors cần authentication
 * Cung cấp common functionality để lấy credentials từ integrations
 */
@Injectable()
export abstract class BaseAuthenticatedExecutor extends BaseNodeExecutor {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly integrationService: IntegrationService,
  ) {
    super();
  }

  /**
   * Lấy userId từ execution context
   */
  protected getUserId(context: ExecutionContext): number {
    const userId = context.userId;

    if (!userId) {
      throw new Error('User ID not found in execution context');
    }

    return userId;
  }

  /**
   * Lấy credentials đã giải mã cho một provider
   */
  protected async getCredentials<T extends PayloadEncryption>(
    context: ExecutionContext,
    providerType: ProviderEnum,
  ): Promise<T> {
    const userId = this.getUserId(context);
    return this.integrationService.getDecryptedCredentials<T>(userId, providerType);
  }

  /**
   * Lấy Google access token
   */
  protected async getGoogleAccessToken(context: ExecutionContext): Promise<string> {
    const userId = this.getUserId(context);
    return this.integrationService.getGoogleAccessToken(userId);
  }

  /**
   * Lấy Facebook Page access token
   */
  protected async getFacebookPageAccessToken(context: ExecutionContext): Promise<string> {
    const userId = this.getUserId(context);
    return this.integrationService.getFacebookPageAccessToken(userId);
  }

  /**
   * Lấy Zalo OA access token
   */
  protected async getZaloOAAccessToken(context: ExecutionContext): Promise<string> {
    const userId = this.getUserId(context);
    return this.integrationService.getZaloOAAccessToken(userId);
  }

  /**
   * Kiểm tra integration có tồn tại không
   */
  protected async hasIntegration(
    context: ExecutionContext,
    providerType: ProviderEnum,
  ): Promise<boolean> {
    const userId = this.getUserId(context);
    return this.integrationService.hasIntegration(userId, providerType);
  }

  /**
   * Validate integration trước khi execute
   */
  protected async validateIntegration(
    context: ExecutionContext,
    providerType: ProviderEnum,
  ): Promise<void> {
    const hasIntegration = await this.hasIntegration(context, providerType);
    
    if (!hasIntegration) {
      throw new Error(
        `Integration not found for provider: ${providerType}. Please configure the integration first.`,
      );
    }
  }

  /**
   * Execute với authentication validation
   */
  protected async executeWithAuth<T>(
    context: ExecutionContext,
    providerType: ProviderEnum,
    operation: () => Promise<T>,
  ): Promise<T> {
    try {
      // Validate integration exists
      await this.validateIntegration(context, providerType);

      // Execute operation
      const result = await operation();

      this.logger.debug(`Successfully executed ${this.type} with ${providerType} auth`);
      return result;

    } catch (error) {
      this.logger.error(`Error executing ${this.type} with ${providerType} auth:`, error);
      
      // Re-throw with more context
      if (error.message.includes('Integration not found')) {
        throw new Error(
          `Authentication required: Please configure ${providerType} integration in your account settings.`,
        );
      }
      
      if (error.message.includes('access token')) {
        throw new Error(
          `Authentication expired: Please re-authenticate your ${providerType} account.`,
        );
      }

      throw error;
    }
  }

  /**
   * Get provider-specific error message
   */
  protected getProviderErrorMessage(providerType: string, error: any): string {
    const providerNames = {
      google: 'Google',
      facebook_page: 'Facebook Page',
      facebook_business: 'Facebook Business',
      zalo_oa: 'Zalo Official Account',
      zalo_zns: 'Zalo ZNS',
    };

    const providerName = providerNames[providerType] || providerType;

    if (error.response?.status === 401) {
      return `${providerName} authentication failed. Please re-authenticate your account.`;
    }

    if (error.response?.status === 403) {
      return `${providerName} access forbidden. Please check your permissions and quotas.`;
    }

    if (error.response?.status === 429) {
      return `${providerName} rate limit exceeded. Please try again later.`;
    }

    return `${providerName} API error: ${error.message}`;
  }
}
