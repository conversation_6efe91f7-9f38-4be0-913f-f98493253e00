#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> để fix hàng loạt Google Executors
 * Tự động thêm missing implementations và fix common errors
 */

const fs = require('fs');
const path = require('path');

// <PERSON><PERSON> s<PERSON>ch các executors cần fix
const EXECUTORS_TO_FIX = [
  // Google Ads
  'ads/google-ads-ad-ab-testing.executor.ts',
  'ads/google-ads-ad-creative-optimization.executor.ts',
  'ads/google-ads-ad-performance-analysis.executor.ts',
  'ads/google-ads-analyze-keyword-quality-score.executor.ts',
  'ads/google-ads-create-custom-audience.executor.ts',
  'ads/google-ads-create-image-ad.executor.ts',
  'ads/google-ads-create-keyword.executor.ts',
  'ads/google-ads-create-responsive-search-ad.executor.ts',
  'ads/google-ads-create-text-ad.executor.ts',
  'ads/google-ads-create-video-ad.executor.ts',
  'ads/google-ads-delete-ad-group.executor.ts',
  'ads/google-ads-delete-ad.executor.ts',
  'ads/google-ads-delete-campaign.executor.ts',
  'ads/google-ads-delete-keyword.executor.ts',
  'ads/google-ads-get-ad-groups.executor.ts',
  'ads/google-ads-get-ads.executor.ts',
  'ads/google-ads-get-campaigns.executor.ts',
  'ads/google-ads-get-keywords.executor.ts',
  'ads/google-ads-get-performance-report.executor.ts',
  'ads/google-ads-keyword-bulk-operations.executor.ts',
  'ads/google-ads-keyword-performance-analysis.executor.ts',
  'ads/google-ads-keyword-research.executor.ts',
  'ads/google-ads-manage-ad-extensions.executor.ts',
  'ads/google-ads-manage-ad-group-negative-keywords.executor.ts',
  'ads/google-ads-manage-campaign-extensions.executor.ts',
  'ads/google-ads-pause-campaign.executor.ts',
  'ads/google-ads-update-ad-group-audiences.executor.ts',
  'ads/google-ads-update-ad-group-bids.executor.ts',
  'ads/google-ads-update-ad-group-demographics.executor.ts',
  'ads/google-ads-update-ad-group.executor.ts',
  'ads/google-ads-update-ad.executor.ts',
  'ads/google-ads-update-campaign-bid-strategy.executor.ts',
  'ads/google-ads-update-campaign-budget.executor.ts',
  'ads/google-ads-update-campaign-schedule.executor.ts',
  'ads/google-ads-update-campaign-targeting.executor.ts',
  'ads/google-ads-update-campaign.executor.ts',
  'ads/google-ads-update-keyword-bids.executor.ts',
  'ads/google-ads-update-keyword.executor.ts',
  
  // Google Drive
  'drive/google-drive-copy-file.executor.ts',
  'drive/google-drive-create-folder.executor.ts',
  'drive/google-drive-delete-file.executor.ts',
  'drive/google-drive-download-file.executor.ts',
  'drive/google-drive-list-files.executor.ts',
  'drive/google-drive-manage-permissions.executor.ts',
  'drive/google-drive-move-file.executor.ts',
  'drive/google-drive-search-files.executor.ts',
  'drive/google-drive-share-file.executor.ts',
  'drive/google-drive-upload-file.executor.ts',
];

// Patterns để detect missing implementations
const MISSING_PATTERNS = {
  name: /readonly name\s*=/,
  getInputSchema: /getInputSchema\(\)/,
  getOutputSchema: /getOutputSchema\(\)/,
  validateInputs: /validateInputs\(/,
};

// Fix patterns
const FIXES = {
  // Fix validateInputs method signature
  validateInputsSignature: {
    pattern: /private validateInputs\(.*?\): void/g,
    replacement: 'validateInputs(inputs: any): boolean'
  },
  
  // Fix validateInputs return
  validateInputsReturn: {
    pattern: /validateInputs\(.*?\): boolean \{[\s\S]*?throw new Error\(/g,
    replacement: (match) => {
      return match.replace(/throw new Error\(/g, 'this.logger.error(`Input validation failed: ${error.message}`); return false; } catch (error) { this.logger.error(`Input validation failed: ${error.message}`); return false; } } validateInputsOld(inputs: any): void { throw new Error(');
    }
  },
  
  // Fix service method calls
  getAdGroupById: {
    pattern: /getAdGroupById\(/g,
    replacement: 'getAdGroups('
  }
};

function fixExecutor(filePath) {
  console.log(`Fixing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Check for missing implementations
  const className = extractClassName(content);
  if (!className) {
    console.log(`  ❌ Could not extract class name`);
    return;
  }
  
  // Add missing name property
  if (!MISSING_PATTERNS.name.test(content)) {
    const nameToAdd = generateNameFromClassName(className);
    content = addNameProperty(content, nameToAdd);
    hasChanges = true;
    console.log(`  ✅ Added name property: ${nameToAdd}`);
  }
  
  // Add missing getInputSchema
  if (!MISSING_PATTERNS.getInputSchema.test(content)) {
    content = addGetInputSchema(content);
    hasChanges = true;
    console.log(`  ✅ Added getInputSchema method`);
  }
  
  // Add missing getOutputSchema
  if (!MISSING_PATTERNS.getOutputSchema.test(content)) {
    content = addGetOutputSchema(content);
    hasChanges = true;
    console.log(`  ✅ Added getOutputSchema method`);
  }
  
  // Fix validateInputs method signature
  if (content.includes('private validateInputs') && content.includes('): void')) {
    content = fixValidateInputsMethod(content);
    hasChanges = true;
    console.log(`  ✅ Fixed validateInputs method signature`);
  }
  
  // Apply other fixes
  for (const [fixName, fix] of Object.entries(FIXES)) {
    if (fix.pattern.test(content)) {
      content = content.replace(fix.pattern, fix.replacement);
      hasChanges = true;
      console.log(`  ✅ Applied fix: ${fixName}`);
    }
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content);
    console.log(`  💾 Saved changes`);
  } else {
    console.log(`  ℹ️  No changes needed`);
  }
}

function extractClassName(content) {
  const match = content.match(/export class (\w+)/);
  return match ? match[1] : null;
}

function generateNameFromClassName(className) {
  // Convert GoogleAdsCreateCampaignExecutor -> Create Google Ads Campaign
  return className
    .replace(/Executor$/, '')
    .replace(/([A-Z])/g, ' $1')
    .trim()
    .replace(/^Google /, '')
    .replace(/Ads /, 'Ads ')
    .replace(/Drive /, 'Drive ')
    .replace(/Calendar /, 'Calendar ')
    .replace(/Analytics /, 'Analytics ')
    .replace(/Sheets /, 'Sheets ');
}

function addNameProperty(content, name) {
  const insertAfter = /readonly description = [^;]+;/;
  if (insertAfter.test(content)) {
    return content.replace(insertAfter, (match) => {
      return `readonly name = '${name}';\n  ${match}`;
    });
  }
  
  // Fallback: insert after category
  const fallbackInsert = /readonly category = [^;]+;/;
  if (fallbackInsert.test(content)) {
    return content.replace(fallbackInsert, (match) => {
      return `${match}\n  readonly name = '${name}';`;
    });
  }
  
  return content;
}

function addGetInputSchema(content) {
  const insertBefore = /async executeNode\(/;
  const schemaMethod = `
  /**
   * Get input schema for validation
   */
  getInputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        // TODO: Define input schema properties
      },
      required: [
        // TODO: Define required fields
      ],
    };
  }

`;
  
  return content.replace(insertBefore, schemaMethod + '  async executeNode(');
}

function addGetOutputSchema(content) {
  const insertBefore = /async executeNode\(/;
  const schemaMethod = `
  /**
   * Get output schema
   */
  getOutputSchema(): Record<string, any> {
    return {
      type: 'object',
      properties: {
        // TODO: Define output schema properties
      },
    };
  }

`;
  
  return content.replace(insertBefore, schemaMethod + '  async executeNode(');
}

function fixValidateInputsMethod(content) {
  // Change private validateInputs(): void to validateInputs(): boolean
  content = content.replace(/private validateInputs\([^)]*\): void/g, 'validateInputs(inputs: any): boolean');
  
  // Wrap method body in try-catch and return boolean
  const methodPattern = /(validateInputs\([^)]*\): boolean \{)([\s\S]*?)(\n  \})/;
  
  return content.replace(methodPattern, (match, start, body, end) => {
    const wrappedBody = `
    try {
      ${body.trim()}
      return true;
    } catch (error) {
      this.logger.error(\`Input validation failed: \${error.message}\`);
      return false;
    }`;
    
    return start + wrappedBody + end;
  });
}

// Main execution
function main() {
  console.log('🔧 Starting Google Executors Fix Script...\n');
  
  const baseDir = __dirname;
  let fixedCount = 0;
  let errorCount = 0;
  
  for (const executorPath of EXECUTORS_TO_FIX) {
    const fullPath = path.join(baseDir, executorPath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${executorPath}`);
      errorCount++;
      continue;
    }
    
    try {
      fixExecutor(fullPath);
      fixedCount++;
    } catch (error) {
      console.log(`❌ Error fixing ${executorPath}: ${error.message}`);
      errorCount++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`  ✅ Fixed: ${fixedCount}`);
  console.log(`  ❌ Errors: ${errorCount}`);
  console.log(`  📁 Total: ${EXECUTORS_TO_FIX.length}`);
  
  if (errorCount === 0) {
    console.log(`\n🎉 All executors fixed successfully!`);
  } else {
    console.log(`\n⚠️  Some executors had errors. Please review manually.`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixExecutor, extractClassName, generateNameFromClassName };
