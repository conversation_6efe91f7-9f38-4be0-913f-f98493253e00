import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để xóa hàng trong Google Sheets
 */
@Injectable()
export class GoogleSheetsDeleteRowExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.deleteRow';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Delete Google Sheets Row';
  readonly description = 'Xóa hàng trong Google Sheets';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node xóa hàng Google Sheets
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const { 
        spreadsheetId, 
        sheetId = 0,
        startIndex,
        endIndex
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (startIndex === undefined || startIndex < 0) {
        throw new Error('Start index must be a non-negative number (0-based)');
      }

      const actualEndIndex = endIndex !== undefined ? endIndex : startIndex + 1;

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Delete rows using GoogleSheetsService
      const result = await this.googleSheetsService.deleteRows(
        accessToken,
        spreadsheetId,
        sheetId,
        startIndex,
        actualEndIndex
      );

      // Return formatted result
      return {
        spreadsheetId,
        sheetId,
        startIndex,
        endIndex: actualEndIndex,
        deletedRows: actualEndIndex - startIndex,
        success: true,
        deletedAt: new Date().toISOString(),
        sheetsResponse: result,
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (inputs.startIndex === undefined || !Number.isInteger(inputs.startIndex) || inputs.startIndex < 0) {
      throw new Error('Start index must be a non-negative integer (0-based)');
    }

    if (inputs.endIndex !== undefined && (!Number.isInteger(inputs.endIndex) || inputs.endIndex <= inputs.startIndex)) {
      throw new Error('End index must be an integer greater than start index');
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        sheetId: {
          type: 'integer',
          description: 'ID của sheet (mặc định: 0)',
          default: 0,
          minimum: 0,
        },
        startIndex: {
          type: 'integer',
          description: 'Chỉ số hàng bắt đầu xóa (0-based)',
          minimum: 0,
        },
        endIndex: {
          type: 'integer',
          description: 'Chỉ số hàng kết thúc xóa (không bao gồm, mặc định: startIndex + 1)',
          minimum: 1,
        },
      },
      required: ['spreadsheetId', 'startIndex'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet',
        },
        sheetId: {
          type: 'integer',
          description: 'ID của sheet',
        },
        startIndex: {
          type: 'integer',
          description: 'Chỉ số hàng bắt đầu đã xóa',
        },
        endIndex: {
          type: 'integer',
          description: 'Chỉ số hàng kết thúc đã xóa',
        },
        deletedRows: {
          type: 'integer',
          description: 'Số hàng đã xóa',
        },
        success: {
          type: 'boolean',
          description: 'Trạng thái thành công',
        },
        deletedAt: {
          type: 'string',
          description: 'Thời gian xóa',
        },
        sheetsResponse: {
          type: 'object',
          description: 'Response từ Google Sheets API',
        },
      },
    };
  }
}
