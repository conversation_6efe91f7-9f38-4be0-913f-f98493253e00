import { Injectable } from '@nestjs/common';
import { BaseAuthenticatedExecutor } from '../../base/base-authenticated-executor';
import { ExecutionContext } from '../../../interfaces/execution-context.interface';
import { NodeCategory } from '../../../entities/node-definition.entity';
import { GoogleSheetsService } from '../../../../../shared/services/google/sheets/google-sheets.service';
import { IntegrationService } from '../../../../../shared/services/integration.service';
import { ProviderEnum } from '../../../enums/provider.enum';

/**
 * Executor để cập nhật một hàng cụ thể trong Google Sheets
 */
@Injectable()
export class GoogleSheetsUpdateRowExecutor extends BaseAuthenticatedExecutor {
  readonly type = 'google.sheet.updateRow';
  readonly category = NodeCategory.GOOGLE_SHEET;
  readonly name = 'Update Google Sheets Row';
  readonly description = '<PERSON>ập nhật một hàng cụ thể trong Google Sheets';

  constructor(
    integrationService: IntegrationService,
    private readonly googleSheetsService: GoogleSheetsService,
  ) {
    super(integrationService);
  }

  /**
   * Thực thi node cập nhật hàng Google Sheets
   */
  async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
    return this.executeWithAuth(context, ProviderEnum.GMAIL, async () => {
      // Validate inputs
      const { 
        spreadsheetId, 
        sheetName = 'Sheet1',
        rowIndex,
        values,
        valueInputOption = 'USER_ENTERED'
      } = inputs;

      if (!spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }

      if (rowIndex === undefined || rowIndex < 1) {
        throw new Error('Row index must be a positive number (1-based)');
      }

      if (!values || !Array.isArray(values)) {
        throw new Error('Values must be an array');
      }

      // Get Google access token
      const accessToken = await this.getGoogleAccessToken(context);

      // Calculate range for the specific row
      const lastColumn = String.fromCharCode(65 + values.length - 1); // A, B, C, etc.
      const range = `${sheetName}!A${rowIndex}:${lastColumn}${rowIndex}`;

      // Update row using GoogleSheetsService
      const result = await this.googleSheetsService.writeData(
        accessToken,
        spreadsheetId,
        range,
        [values], // Wrap in array since writeData expects 2D array
        valueInputOption as 'RAW' | 'USER_ENTERED'
      );

      // Return formatted result
      return {
        spreadsheetId,
        sheetName,
        rowIndex,
        range: result.updatedRange,
        updatedCells: result.updatedCells,
        values,
        valueInputOption,
        updatedAt: new Date().toISOString(),
      };
    });
  }

  /**
   * Validate input schema
   */
  validateInputs(inputs: any): boolean {
    if (!inputs.spreadsheetId || typeof inputs.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a non-empty string');
    }

    if (inputs.rowIndex === undefined || !Number.isInteger(inputs.rowIndex) || inputs.rowIndex < 1) {
      throw new Error('Row index must be a positive integer (1-based)');
    }

    if (!inputs.values || !Array.isArray(inputs.values)) {
      throw new Error('Values must be an array');
    }

    if (inputs.valueInputOption && !['RAW', 'USER_ENTERED'].includes(inputs.valueInputOption)) {
      throw new Error('Value input option must be either "RAW" or "USER_ENTERED"');
    }

    return true;
  }

  /**
   * Get input schema for validation
   */
  getInputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của Google Spreadsheet',
          minLength: 1,
        },
        sheetName: {
          type: 'string',
          description: 'Tên sheet (mặc định: Sheet1)',
          default: 'Sheet1',
        },
        rowIndex: {
          type: 'integer',
          description: 'Chỉ số hàng cần cập nhật (bắt đầu từ 1)',
          minimum: 1,
        },
        values: {
          type: 'array',
          description: 'Dữ liệu cho hàng (mảng 1 chiều)',
          items: {
            oneOf: [
              { type: 'string' },
              { type: 'number' },
              { type: 'boolean' },
            ],
          },
          minItems: 1,
        },
        valueInputOption: {
          type: 'string',
          description: 'Cách xử lý dữ liệu đầu vào',
          enum: ['RAW', 'USER_ENTERED'],
          default: 'USER_ENTERED',
        },
      },
      required: ['spreadsheetId', 'rowIndex', 'values'],
    };
  }

  /**
   * Get output schema
   */
  getOutputSchema(): any {
    return {
      type: 'object',
      properties: {
        spreadsheetId: {
          type: 'string',
          description: 'ID của spreadsheet',
        },
        sheetName: {
          type: 'string',
          description: 'Tên sheet',
        },
        rowIndex: {
          type: 'integer',
          description: 'Chỉ số hàng đã cập nhật',
        },
        range: {
          type: 'string',
          description: 'Phạm vi đã được cập nhật',
        },
        updatedCells: {
          type: 'number',
          description: 'Số cells đã cập nhật',
        },
        values: {
          type: 'array',
          description: 'Dữ liệu đã cập nhật',
        },
        valueInputOption: {
          type: 'string',
          description: 'Cách xử lý dữ liệu đã sử dụng',
        },
        updatedAt: {
          type: 'string',
          description: 'Thời gian cập nhật',
        },
      },
    };
  }
}
