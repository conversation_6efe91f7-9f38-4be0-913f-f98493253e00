import { Injectable, Logger } from '@nestjs/common';
import { Workflow, WorkflowNode, WorkflowEdge } from '../entities';
import { WorkflowGraphMetadata } from './interfaces/langgraph.interface';

/**
 * Service for parsing and analyzing workflow definitions
 * Converts workflow definitions into LangGraph-compatible formats
 */
@Injectable()
export class WorkflowDefinitionParserService {
  private readonly logger = new Logger(WorkflowDefinitionParserService.name);

  /**
   * Parse workflow definition and extract metadata
   * @param workflow - Workflow entity to parse
   * @returns Workflow graph metadata
   */
  parseWorkflowDefinition(workflow: Workflow): WorkflowGraphMetadata {
    this.logger.debug(`Parsing workflow definition: ${workflow.id}`);

    try {
      const nodes = workflow.nodes || [];
      const edges = workflow.edges || [];

      // Basic structure analysis
      const nodeCount = nodes.length;
      const edgeCount = edges.length;
      const entryNodes = this.findEntryNodes(nodes, edges);
      const exitNodes = this.findExitNodes(nodes, edges);

      // Complexity analysis
      const cyclomaticComplexity = this.calculateCyclomaticComplexity(nodes, edges);
      const maxDepth = this.calculateMaxDepth(nodes, edges);
      const parallelPaths = this.calculateParallelPaths(nodes, edges);

      // Validation
      const validation = this.validateWorkflowStructure(workflow);

      // Performance estimates
      const estimatedExecutionTime = this.estimateExecutionTime(nodes);
      const estimatedMemoryUsage = this.estimateMemoryUsage(nodes);

      // Dependencies
      const requiredExecutors = this.extractRequiredExecutors(nodes);
      const externalDependencies = this.extractExternalDependencies(nodes);

      // Versioning
      const checksum = this.calculateWorkflowChecksum(workflow);

      const metadata: WorkflowGraphMetadata = {
        nodeCount,
        edgeCount,
        entryNodes,
        exitNodes,
        cyclomaticComplexity,
        maxDepth,
        parallelPaths,
        isValid: validation.isValid,
        validationErrors: validation.errors,
        validationWarnings: validation.warnings,
        estimatedExecutionTime,
        estimatedMemoryUsage,
        requiredExecutors,
        externalDependencies,
        version:'1.0.0',
        lastModified: Date.now(),
        checksum,
      };

      this.logger.debug(`Parsed workflow metadata for: ${workflow.id}`, {
        nodeCount,
        edgeCount,
        complexity: cyclomaticComplexity,
        isValid: validation.isValid,
      });

      return metadata;

    } catch (error) {
      this.logger.error(`Failed to parse workflow definition ${workflow.id}:`, error);
      throw new Error(`Workflow parsing failed: ${error.message}`);
    }
  }

  /**
   * Find entry nodes (trigger nodes or nodes with no incoming edges)
   * @private
   */
  private findEntryNodes(nodes: WorkflowNode[], edges: WorkflowEdge[]): string[] {
    const nodesWithIncoming = new Set(edges.map(edge => edge.targetNodeId));
    const triggerNodes = nodes.filter(node => node.nodeType.includes('trigger'));
    
    if (triggerNodes.length > 0) {
      return triggerNodes.map(node => node.id);
    }

    return nodes
      .filter(node => !nodesWithIncoming.has(node.id))
      .map(node => node.id);
  }

  /**
   * Find exit nodes (output nodes or nodes with no outgoing edges)
   * @private
   */
  private findExitNodes(nodes: WorkflowNode[], edges: WorkflowEdge[]): string[] {
    const nodesWithOutgoing = new Set(edges.map(edge => edge.sourceNodeId));
    const outputNodes = nodes.filter(node => node.nodeType.includes('output'));
    
    if (outputNodes.length > 0) {
      return outputNodes.map(node => node.id);
    }

    return nodes
      .filter(node => !nodesWithOutgoing.has(node.id))
      .map(node => node.id);
  }

  /**
   * Calculate cyclomatic complexity
   * @private
   */
  private calculateCyclomaticComplexity(nodes: WorkflowNode[], edges: WorkflowEdge[]): number {
    // Cyclomatic complexity = E - N + 2P
    // E = number of edges, N = number of nodes, P = number of connected components
    const E = edges.length;
    const N = nodes.length;
    const P = this.countConnectedComponents(nodes, edges);
    
    return Math.max(1, E - N + 2 * P);
  }

  /**
   * Count connected components in the graph
   * @private
   */
  private countConnectedComponents(nodes: WorkflowNode[], edges: WorkflowEdge[]): number {
    const visited = new Set<string>();
    let components = 0;

    const adjacencyList = new Map<string, string[]>();
    
    // Build adjacency list (undirected)
    nodes.forEach(node => adjacencyList.set(node.id, []));
    edges.forEach(edge => {
      adjacencyList.get(edge.sourceNodeId)?.push(edge.targetNodeId);
      adjacencyList.get(edge.targetNodeId)?.push(edge.sourceNodeId);
    });

    // DFS to find connected components
    const dfs = (nodeId: string) => {
      visited.add(nodeId);
      const neighbors = adjacencyList.get(nodeId) || [];
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor)) {
          dfs(neighbor);
        }
      });
    };

    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        dfs(node.id);
        components++;
      }
    });

    return Math.max(1, components);
  }

  /**
   * Calculate maximum depth of the workflow
   * @private
   */
  private calculateMaxDepth(nodes: WorkflowNode[], edges: WorkflowEdge[]): number {
    const adjacencyList = new Map<string, string[]>();
    const inDegree = new Map<string, number>();

    // Initialize
    nodes.forEach(node => {
      adjacencyList.set(node.id, []);
      inDegree.set(node.id, 0);
    });

    // Build adjacency list and calculate in-degrees
    edges.forEach(edge => {
      adjacencyList.get(edge.sourceNodeId)?.push(edge.targetNodeId);
      inDegree.set(edge.targetNodeId, (inDegree.get(edge.targetNodeId) || 0) + 1);
    });

    // Topological sort with depth tracking
    const queue: Array<{ nodeId: string; depth: number }> = [];
    let maxDepth = 0;

    // Start with nodes that have no incoming edges
    nodes.forEach(node => {
      if (inDegree.get(node.id) === 0) {
        queue.push({ nodeId: node.id, depth: 1 });
      }
    });

    while (queue.length > 0) {
      const { nodeId, depth } = queue.shift()!;
      maxDepth = Math.max(maxDepth, depth);

      const neighbors = adjacencyList.get(nodeId) || [];
      neighbors.forEach(neighbor => {
        const newInDegree = (inDegree.get(neighbor) || 0) - 1;
        inDegree.set(neighbor, newInDegree);

        if (newInDegree === 0) {
          queue.push({ nodeId: neighbor, depth: depth + 1 });
        }
      });
    }

    return maxDepth;
  }

  /**
   * Calculate number of parallel execution paths
   * @private
   */
  private calculateParallelPaths(nodes: WorkflowNode[], edges: WorkflowEdge[]): number {
    const outDegree = new Map<string, number>();
    
    nodes.forEach(node => outDegree.set(node.id, 0));
    edges.forEach(edge => {
      outDegree.set(edge.sourceNodeId, (outDegree.get(edge.sourceNodeId) || 0) + 1);
    });

    // Count nodes with multiple outgoing edges (branching points)
    const branchingNodes = Array.from(outDegree.values()).filter(degree => degree > 1);
    
    return branchingNodes.reduce((sum, degree) => sum + degree, 0);
  }

  /**
   * Validate workflow structure
   * @private
   */
  private validateWorkflowStructure(workflow: Workflow): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const nodes = workflow.nodes || [];
    const edges = workflow.edges || [];

    // Check for empty workflow
    if (nodes.length === 0) {
      errors.push('Workflow must contain at least one node');
    }

    // Check for invalid node references in edges
    const nodeIds = new Set(nodes.map(node => node.id));
    edges.forEach(edge => {
      if (!nodeIds.has(edge.sourceNodeId)) {
        errors.push(`Edge references non-existent source node: ${edge.sourceNodeId}`);
      }
      if (!nodeIds.has(edge.targetNodeId)) {
        errors.push(`Edge references non-existent target node: ${edge.targetNodeId}`);
      }
    });

    // Check for cycles (simplified detection)
    if (this.hasCycles(nodes, edges)) {
      warnings.push('Workflow contains cycles - may cause infinite loops');
    }

    // Check for unreachable nodes
    const reachableNodes = this.findReachableNodes(nodes, edges);
    const unreachableNodes = nodes.filter(node => !reachableNodes.has(node.id));
    if (unreachableNodes.length > 0) {
      warnings.push(`Unreachable nodes found: ${unreachableNodes.map(n => n.id).join(', ')}`);
    }

    // Check for missing node types
    const invalidNodes = nodes.filter(node => !node.nodeType || node.nodeType.trim() === '');
    if (invalidNodes.length > 0) {
      errors.push(`Nodes with missing or invalid types: ${invalidNodes.map(n => n.id).join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Check for cycles in the workflow
   * @private
   */
  private hasCycles(nodes: WorkflowNode[], edges: WorkflowEdge[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const adjacencyList = new Map<string, string[]>();

    // Build adjacency list
    nodes.forEach(node => adjacencyList.set(node.id, []));
    edges.forEach(edge => {
      adjacencyList.get(edge.sourceNodeId)?.push(edge.targetNodeId);
    });

    const dfs = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);

      const neighbors = adjacencyList.get(nodeId) || [];
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          if (dfs(neighbor)) return true;
        } else if (recursionStack.has(neighbor)) {
          return true; // Cycle detected
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of nodes) {
      if (!visited.has(node.id)) {
        if (dfs(node.id)) return true;
      }
    }

    return false;
  }

  /**
   * Find all reachable nodes from entry points
   * @private
   */
  private findReachableNodes(nodes: WorkflowNode[], edges: WorkflowEdge[]): Set<string> {
    const reachable = new Set<string>();
    const adjacencyList = new Map<string, string[]>();
    
    // Build adjacency list
    nodes.forEach(node => adjacencyList.set(node.id, []));
    edges.forEach(edge => {
      adjacencyList.get(edge.sourceNodeId)?.push(edge.targetNodeId);
    });

    // Find entry nodes
    const entryNodes = this.findEntryNodes(nodes, edges);

    // DFS from each entry node
    const dfs = (nodeId: string) => {
      if (reachable.has(nodeId)) return;
      reachable.add(nodeId);
      
      const neighbors = adjacencyList.get(nodeId) || [];
      neighbors.forEach(neighbor => dfs(neighbor));
    };

    entryNodes.forEach(entryNode => dfs(entryNode));

    return reachable;
  }

  /**
   * Estimate execution time based on node types
   * @private
   */
  private estimateExecutionTime(nodes: WorkflowNode[]): number {
    const nodeTimeEstimates: Record<string, number> = {
      'system.http.request': 2000,
      'system.delay': 1000,
      'system.condition': 100,
      'system.transform.data': 500,
      'system.loop': 1000,
      'system.output': 200,
      'system.manual.trigger': 100,
      'system.variable': 50,
      'system.error.handler': 300,
    };

    return nodes.reduce((total, node) => {
      const estimate = nodeTimeEstimates[node.nodeType] || 500; // Default 500ms
      return total + estimate;
    }, 0);
  }

  /**
   * Estimate memory usage based on node types
   * @private
   */
  private estimateMemoryUsage(nodes: WorkflowNode[]): number {
    const nodeMemoryEstimates: Record<string, number> = {
      'system.http.request': 1024 * 1024, // 1MB
      'system.delay': 1024, // 1KB
      'system.condition': 2048, // 2KB
      'system.transform.data': 2048 * 1024, // 2MB
      'system.loop': 512 * 1024, // 512KB
      'system.output': 1024 * 1024, // 1MB
      'system.manual.trigger': 4096, // 4KB
      'system.variable': 1024, // 1KB
      'system.error.handler': 8192, // 8KB
    };

    return nodes.reduce((total, node) => {
      const estimate = nodeMemoryEstimates[node.nodeType] || 64 * 1024; // Default 64KB
      return total + estimate;
    }, 0);
  }

  /**
   * Extract required executors from nodes
   * @private
   */
  private extractRequiredExecutors(nodes: WorkflowNode[]): string[] {
    const executors = new Set<string>();
    nodes.forEach(node => {
      if (node.nodeType) {
        executors.add(node.nodeType);
      }
    });
    return Array.from(executors);
  }

  /**
   * Extract external dependencies from node configurations
   * @private
   */
  private extractExternalDependencies(nodes: WorkflowNode[]): string[] {
    const dependencies = new Set<string>();
    
    nodes.forEach(node => {
      if (node.nodeType === 'system.http.request') {
        dependencies.add('axios');
      }
      // Add more dependency detection logic as needed
    });

    return Array.from(dependencies);
  }

  /**
   * Calculate workflow checksum for versioning
   * @private
   */
  private calculateWorkflowChecksum(workflow: Workflow): string {
    const data = {
      nodes: workflow.nodes?.map(n => ({ id: n.id, type: n.nodeType, config: n.config })),
      edges: workflow.edges?.map(e => ({ source: e.sourceNodeId, target: e.targetNodeId, condition: e.condition })),
    };

    // Simple checksum calculation (in production, use a proper hash function)
    const jsonString = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(16);
  }
}
